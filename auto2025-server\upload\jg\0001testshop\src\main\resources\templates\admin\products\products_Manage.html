<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
<head th:replace="@{/admin/head.html}"></head>
  
</head>

<body>
    <div class="content-area" style="height: 100%; padding: 20px;">
        <h4 class="page-title">管理产品</h4>

        <div>
        <div class="container-fluid">
    <div class="card mb-3">
        <div class="card-body">
            <form class="search-form" method="get">
                <div class="row g-3 align-items-end">
                    <div class="col-md-3">
                        <label for="search_pname" class="form-label">产品名称</label>
                        <input type="text" class="form-control" id="search_pname" name="pname" placeholder="请输入产品名称" th:value="${param.pname}">
                    </div>
                    <div class="col-md-3">
                        <label for="search_cid" class="form-label">产品分类</label>
                        <input type="text" class="form-control" id="search_cid" name="cid" placeholder="请输入产品分类" th:value="${param.cid}">
                    </div>
                    <div class="col-md-3">
                        <label for="search_price" class="form-label">价格</label>
                        <input type="text" class="form-control" id="search_price" name="price" placeholder="请输入价格" th:value="${param.price}">
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-search"></i> 搜索
                        </button>
                        <a href="?" class="btn btn-secondary">
                            <i class="bi bi-arrow-clockwise"></i> 重置
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">产品列表</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="thead-light">
                        <tr>
                    <th>产品名称</th>
                    <th>产品分类</th>
                    <th>产品图片</th>
                    <th>价格</th>
                    <th>数量</th>
                    <th>产品介绍</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr th:each="item : ${list}">
                    <td th:text="${item.pname}"></td>
                    <td th:text="${item.cid}"></td>
                    <td>
                        <div th:if="${item.photo}">
                            <img th:src="@{'/upload/' + ${item.photo}}" alt="图片" style="width: 60px; height: 60px; object-fit: cover; border: 1px solid #ddd; border-radius: 4px; cursor: pointer;" onclick="previewImage(this.src)">
                        </div>
                        <span th:unless="${item.photo}">无图片</span>
                    </td>
                    <td th:text="${item.price}"></td>
                    <td th:text="${item.quantity}"></td>
                    <td th:text="${item.pmemo}"></td>
                    <td>
                        <a th:href="@{/productsToDetail(id=${item.pid})}" class="btn btn-sm btn-info me-1">
                            <i class="bi bi-eye-fill"></i> 详情
                        </a>
                        <a th:href="@{/productsToEdit(id=${item.pid})}" class="btn btn-sm btn-primary me-1">
                            <i class="bi bi-pencil-square"></i> 编辑
                        </a>
                        <a href="javascript:void(0)" th:data-id="${item.pid}" onclick="deleteData('productsDel',this)" class="btn btn-sm btn-danger">
                            <i class="bi bi-trash3"></i> 删除
                        </a>
                    </td>
                        </tr>
                        <tr th:if="${#lists.isEmpty(list)}">
                            <td colspan="100%" class="text-center text-muted">暂无数据</td>
                        </tr>
                    </tbody>
                </table>
            
            <!-- 分页区域 -->
            <nav aria-label="分页导航" th:if="${totalPages > 1}">
                <ul class="pagination justify-content-center">
                    <li class="page-item" th:classappend="${currentPage == 1} ? 'disabled'">
                        <a class="page-link" th:href="@{''(page=${currentPage - 1})}">上一页</a>
                    </li>
                    <li class="page-item" th:each="i : ${#numbers.sequence(1, totalPages)}" 
                        th:classappend="${i == currentPage} ? 'active'">
                        <a class="page-link" th:href="@{''(page=${i})}" th:text="${i}"></a>
                    </li>
                    <li class="page-item" th:classappend="${currentPage == totalPages} ? 'disabled'">
                        <a class="page-link" th:href="@{''(page=${currentPage + 1})}">下一页</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
</div>
        <script>
        // 分页功能
        function goToPage(page) {
            var url = new URL(window.location);
            url.searchParams.set('page', page);
            window.location.href = url.toString();
        }
        
        // 图片预览函数
        function previewImage(src) {
            var modal = '<div class="modal fade" id="imagePreviewModal" tabindex="-1">' +
                       '<div class="modal-dialog modal-lg modal-dialog-centered">' +
                       '<div class="modal-content">' +
                       '<div class="modal-header">' +
                       '<h5 class="modal-title">图片预览</h5>' +
                       '<button type="button" class="btn-close" data-bs-dismiss="modal"></button>' +
                       '</div>' +
                       '<div class="modal-body text-center">' +
                       '<img src="' + src + '" class="img-fluid" style="max-height: 70vh;">' +
                       '</div>' +
                       '</div></div></div>';
            
            $('body').append(modal);
            var modalInstance = new bootstrap.Modal(document.getElementById('imagePreviewModal'));
            modalInstance.show();
            
            $('#imagePreviewModal').on('hidden.bs.modal', function() {
                $(this).remove();
            });
        }
        </script>

        </div>
    </div>

    <!-- Bootstrap JS -->
</body>

</html>