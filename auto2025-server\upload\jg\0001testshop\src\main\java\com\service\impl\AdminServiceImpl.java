package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mapper.AdminMapper;
import com.model.Admin;
import com.service.AdminService;
import com.util.PageBean;

/**
 * 管理员业务逻辑服务实现类
 *
 * 功能说明：
 * 1. 实现管理员相关的业务逻辑
 * 2. 提供完整的CRUD操作实现
 * 3. 处理数据查询和分页逻辑
 * 4. 调用Mapper层进行数据库操作
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024
 */
@Service
public class AdminServiceImpl implements AdminService {

	/**
	 * 管理员数据访问层
	 * 通过Spring自动注入
	 */
	@Autowired
	private AdminMapper adminMapper;

	// 查询多条记录
	public List<Admin> queryAdminList(Admin admin, PageBean page) throws Exception {
		Map<String, Object> map = getQueryMap(admin, page);

		List<Admin> getAdmin = adminMapper.query(map);

		return getAdmin;
	}

	// 得到记录总数
	@Override
	public int getCount(Admin admin) {
		Map<String, Object> map = getQueryMap(admin, null);
		int count = adminMapper.getCount(map);
		return count;
	}

	private Map<String, Object> getQueryMap(Admin admin, PageBean page) {
		Map<String, Object> map = new HashMap<String, Object>();
		if (admin != null) {
						map.put("id", admin.getId());
			map.put("lname", admin.getLname());
			map.put("password", admin.getPassword());
			map.put("marrys", admin.getMarrys());
			map.put("sort", admin.getSort());
			map.put("condition", admin.getCondition());

		}
		PageBean.setPageMap(map, page);
		return map;
	}

	// 添加
	public int insertAdmin(Admin admin) throws Exception {
		return adminMapper.insertAdmin(admin);
	}

	/**
	 * 根据ID删除管理员
	 * @param id 主键ID
	 * @return 影响的行数
	 * @throws Exception 异常
	 */
	public int deleteAdmin(Integer id) throws Exception {
		return adminMapper.deleteAdmin(id);
	}

	/**
	 * 更新管理员
	 * @param admin 管理员实体对象
	 * @return 影响的行数
	 * @throws Exception 异常
	 */
	public int updateAdmin(Admin admin) throws Exception {
		return adminMapper.updateAdmin(admin);
	}

	/**
	 * 根据ID查询管理员详情
	 * @param id 主键ID
	 * @return 管理员实体对象，如果不存在则返回null
	 * @throws Exception 异常
	 */
	public Admin queryAdminById(Integer id) throws Exception {
		Admin po = adminMapper.queryAdminById(id);
		return po;
	}
}
