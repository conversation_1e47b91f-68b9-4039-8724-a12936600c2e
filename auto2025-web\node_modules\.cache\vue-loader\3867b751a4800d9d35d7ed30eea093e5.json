{"remainingRequest": "J:\\auto2025\\auto2025-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!J:\\auto2025\\auto2025-web\\src\\views\\web\\CodeGenerator.vue?vue&type=script&lang=js", "dependencies": [{"path": "J:\\auto2025\\auto2025-web\\src\\views\\web\\CodeGenerator.vue", "mtime": 1749465006927}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748675476717}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748675484569}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748675476717}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1748675485115}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["J:\\auto2025\\auto2025-web\\src\\views\\web\\CodeGenerator.vue"], "names": [], "mappings": ";EA4tCE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzD,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE/B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzD,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE7B,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACd,CAAC;;MAED,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACb,CAAC;;MAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACvD,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACtD;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAC7B,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC9B,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAChB,CAAC;;MAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEtC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACrB,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACjC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrC,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACpB,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7E,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7E,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpF,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEzB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACpE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACrE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC1E,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1E,CAAC;;MAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACvC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7E;;MAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC1B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;UAClC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzE;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACZ;;MAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7E;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;QAC7B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE3D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACnC,CAAC,CAAC,EAAE;YACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAChD;QACF;;QAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACb,CAAC,CAAC,EAAE;YACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;UACxB,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC;QACF,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAChC;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QAC9B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE9B,CAAC,EAAE,CAAC,CAAC,CAAC;QACN,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC;QACP;QACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC;QACP;;QAEA,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;UAExB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB;;UAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;UAEzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEhD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAE/C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB;YACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;YAE/E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;YAE/B,CAAC,EAAE,CAAC,CAAC,CAAC;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;;YAEtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,EAAE,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC;QACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACrC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;QACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEhC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEhC,CAAC,EAAE,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;QAE9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACtC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACvB;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChD;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B;;MAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;QACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACzC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAClC;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QAC/B,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;;UAEf;;UAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;UAE9C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;cACzB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;gBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7D,CAAC,CAAC;YACJ;UACF,EAAE,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B;QACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAC9B;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC/C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;QAErB,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;UAGlD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;UAEtC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;YAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;YAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;YACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;YAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;YAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;;YAEzC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;cACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;cAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;cAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;YAClD;;YAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,EAAE,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B;QACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACtC,CAAC,CAAC,EAAE;UACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB;;UAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;UAEX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACZ,CAAC,EAAE,CAAC,CAAC,CAAC;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7C,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C,EAAE,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,EAAE,CAAC,CAAC,CAAC;YACN,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C;;UAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;gBACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrB;YACF;;YAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACvF,EAAE,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;UAC1B;QACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;QAC1B;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC7C,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf;;UAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE1C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;;YAEtC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACrC,CAAC,CAAC,EAAE;gBACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;gBAElE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;kBAC/C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;oBAC7C,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACzB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;oBACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;kBACxC,CAAC,CAAC;;kBAEF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBACrD,EAAE,CAAC,CAAC,CAAC,EAAE;kBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;gBAClB;cACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;cAClB;YACF;;YAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9C,EAAE,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B;QACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACnC;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN;YACE,CAAC,CAAC,EAAE,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAClB;QACF;MACF,CAAC;;MAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE5B,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UAChC;YACE,CAAC,CAAC,EAAE,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACnB;QACF;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAExC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEtC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEjC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEzC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAElC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAErC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB;QACF,CAAC;MACH;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC5B;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACtC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC;QACP;;QAEA,CAAC,EAAE,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAErB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP;;QAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACpC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACxB,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;QAEtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAErC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACV;gBACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpG,CAAC;cACD;gBACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtB;YACF;UACF,CAAC;QACH;;QAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAErC,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC;;UAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1D;;UAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;UAEnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE5C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAErC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YACnD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE7B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACpC,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UAC1E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE7B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;YACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5D;;UAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3D;;UAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE3C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;;UAEjG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;;UAEjG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;;UAE1F,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAC/F,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3G;;UAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;UAChB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YAC7C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;cACZ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;cAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACnB;YACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB;;UAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEvC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;UACxC;;UAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;UACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;UACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;UACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;UACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;UAErD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC;;QAEH,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACtC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACxF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAClE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEV,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACvC,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;UAC9C,EAAE,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B;QACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/B;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACxC,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;UAC/C,EAAE,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B;QACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/B;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC1C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtE;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC3C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtE;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MACtC;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;eAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;eAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;eACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC;eAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC;;MAED,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QAClC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC;QACP;;QAEA,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;UAE5B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;cACxC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;cACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;YAC3B,CAAC,CAAC;UACJ;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAElC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC,CAAC;;UAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;YACnG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;;YAEzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B;;YAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,EAAE,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC;;QAEF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAClB;UACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACnC;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;UACzF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC;QACP;;QAEA,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;UAEnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;;UAEd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEhC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACtC;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;QAChC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEtD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEnD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACf,CAAC;;QAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACd;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;QACpC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEtD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACf,CAAC;;QAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACd;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC1C,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;QAE9C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAChD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE/E,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;YAC3D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC1C,EAAE,CAAC,CAAC,CAAC,EAAE;cACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC3B;UACF,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAC3B;;UAEA,CAAC,EAAE,CAAC;UACJ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjD;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC;;QAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEjC,CAAC,EAAE,CAAC,CAAC,CAAC;QACN,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;UACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEjG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACd;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC9C,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3F,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;QAE5C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAChD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEjF,CAAC,EAAE,CAAC,CAAC,CAAC;UACN,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;YAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC;;UAEA,CAAC,EAAE,CAAC,CAAC,CAAC;UACN,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAC3B;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC;;QAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEjC,CAAC,EAAE,CAAC,CAAC,CAAC;QACN,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;UACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9E;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1H;;QAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACtB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClK;QACF,CAAC;;QAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACd;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE/B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACvC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEhC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACjC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C;QACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE3D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;QAC5B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC;QACP;;QAEA,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAC9E,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;UAEd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QAChC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC;QACP;;QAEA,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,EAAE;YACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC;QACF;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACrC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;UACzF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC;QACP;;QAEA,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;UAEpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YACzD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UACrB,CAAC;;UAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC,CAAC,CAAC;UACP;;UAEA,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;;UAEd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;UACf;;UAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE/B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACvC;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC7C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;;QAE3C,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE7D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UAChD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE3E,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;YAC3D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9C,EAAE,CAAC,CAAC,CAAC,EAAE;cACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC1C;UACF,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;qBACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAC1C,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI;UAC9B,EAAE,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI;UAC3B;;UAEA,CAAC,EAAE,CAAC;UACJ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjD;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC;;QAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAErE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACd;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACjC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC;QACP;;QAEA,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,EAAE;YACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC;QACF;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACrC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;QAEpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACV;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrE;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B;QACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;QAExB,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;;UAEzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACV;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC7C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YACzB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;cAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;cACtB;gBACE,CAAC,CAAC,EAAE,CAAC;gBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACnB;YACF;UACF;QACF,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,EAAE,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MAClC;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UACzB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACN;cACE,CAAC,CAAC,EAAE,CAAC;cACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACnB;UACF;QACF;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MAC9B;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;QAChC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UACf,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC3B;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/C;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACjC,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACN;cACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB;UACF;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC;QACF;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC7C,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACN;cACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB;UACF;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC;QACF;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC7B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;UACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC/B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;UACtD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MACrC;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC7B;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;QAC9B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC9D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAExC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QAC3C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC;QACP;;QAEA,CAAC,CAAC,EAAE;UACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACpC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QAC7C,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;UAEzE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;cACtD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;gBAC3C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;kBACrF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;kBAClC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACjC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;oBACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACrC;gBACF;cACF;;cAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;gBACxF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cAChB;YACF,CAAC;UACH;QACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAClC;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;QACzC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC;QACP;;QAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEV,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QAClC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC;QACP;;QAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC;QACP;;QAEA,CAAC,EAAE,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACjD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP;QACF;;QAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP;;QAEA,CAAC,CAAC,EAAE;UACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1D;;UAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5C;;UAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/D,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE7C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YACpB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cAC5B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC;;YAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;cAC9C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACZ,CAAC,CAAC,EAAE;kBACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAClE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;kBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjC;cACF;;cAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACjD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;kBAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;kBAChC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;kBAClC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;kBAC9B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;kBAClC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;kBAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;kBAC9B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;kBACP,CAAC,CAAC,CAAC,EAAE,CAAC;gBACR;;gBAEA,CAAC,CAAC,EAAE;kBACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;kBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvC;cACF;YACF;;YAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChD;UACF,EAAE,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpC;QACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACnC,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACN;cACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB;UACF;;UAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;UAEtC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChD;UACF,EAAE,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC;QACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC;QACF;MACF;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QAC3B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACjC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC;UACP;UACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC,CAAC,CAAC;UACP;;UAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACtC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3B,CAAC,CAAC,CAAC,CAAC,CAAC;YACP;YACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5B,CAAC,CAAC,CAAC,CAAC,CAAC;YACP;UACF,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAClD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5B,CAAC,CAAC,CAAC,CAAC,CAAC;YACP;UACF;;UAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YACzB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;UAC1C;;UAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChD;;UAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChD,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACvC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChD;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACrC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;YAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP;UACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC;;MAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEhB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEhB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEb,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAExB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B;IACF;EACF", "file": "J:/auto2025/auto2025-web/src/views/web/CodeGenerator.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"code-generator-container\">\n    <!-- 用户信息浮动显示 -->\n    <div v-if=\"isLoggedIn\" class=\"user-info-float\">\n      <el-dropdown @command=\"handleUserCommand\">\n        <span class=\"user-info-trigger\">\n          <el-icon>\n            <User />\n          </el-icon>\n          {{ userInfo.username }}\n          <el-icon class=\"el-icon--right\">\n            <ArrowDown />\n          </el-icon>\n        </span>\n        <template #dropdown>\n          <el-dropdown-menu>\n            <el-dropdown-item command=\"logout\">退出登录</el-dropdown-item>\n          </el-dropdown-menu>\n        </template>\n      </el-dropdown>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"main-content\">\n      <el-tabs v-model=\"activeTab\" type=\"card\" class=\"generator-tabs\" @tab-click=\"handleTabClick\">\n        <!-- 设置项目 -->\n        <el-tab-pane label=\"设置项目\" name=\"project\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <Folder />\n              </el-icon>\n              设置项目\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <!-- 页面标题 -->\n            <div class=\"page-header\">\n              <div class=\"header-content\">\n                <h1 class=\"page-title\">\n                  <el-icon class=\"title-icon\">\n                    <Setting />\n                  </el-icon>\n                  代码生成器\n                </h1>\n                <p class=\"page-description\">快速生成高质量的代码，提升开发效率</p>\n              </div>\n            </div>\n\n            <!-- 项目类型选择 -->\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>支持的项目类型</h3>\n                <p>选择您需要的技术栈，快速生成项目模板</p>\n              </div>\n\n              <!-- 功能卡片网格 -->\n              <div class=\"features-grid\">\n                <div class=\"feature-card\" @click=\"selectProject('springboot-thymeleaf')\"\n                  :class=\"{ active: selectedProject === 'springboot-thymeleaf' }\">\n                  <h3>🍃 SpringBoot + Thymeleaf</h3>\n                  <p>传统的服务端渲染架构，适合企业级应用开发，集成Thymeleaf模板引擎</p>\n                </div>\n\n                <div class=\"feature-card\" @click=\"selectProject('springboot-miniprogram')\"\n                  :class=\"{ active: selectedProject === 'springboot-miniprogram' }\">\n                  <h3>📱 SpringBoot + 小程序</h3>\n                  <p>微信小程序后端API开发，提供完整的用户认证和数据管理功能</p>\n                </div>\n\n                <div class=\"feature-card\" @click=\"selectProject('springboot-vue')\"\n                  :class=\"{ active: selectedProject === 'springboot-vue' }\">\n                  <h3>⚡ SpringBoot + Vue</h3>\n                  <p>现代化前后端分离架构，Vue.js前端 + SpringBoot后端API</p>\n                </div>\n\n                <div class=\"feature-card\" @click=\"selectProject('ssm-vue')\"\n                  :class=\"{ active: selectedProject === 'ssm-vue' }\">\n                  <h3>🔧 SSM + Vue</h3>\n                  <p>经典的SSM框架（Spring + SpringMVC + MyBatis）配合Vue.js前端</p>\n                </div>\n              </div>\n            </div>\n\n            <!-- 项目配置区域 -->\n            <div v-if=\"selectedProject\" class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>项目配置 - {{ getProjectName(selectedProject) }}</h3>\n                <p>配置项目基本信息和生成参数</p>\n              </div>\n              <div class=\"form-section\">\n                <el-form :model=\"projectForm\" label-width=\"120px\" class=\"project-form\">\n                  <el-row :gutter=\"20\">\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"数据库类型\">\n                        <el-select v-model=\"projectForm.databaseType\" placeholder=\"请选择数据库类型\" style=\"width: 100%\">\n                          <el-option label=\"MySQL\" value=\"mysql\" />\n                          <el-option label=\"SQL Server\" value=\"sqlserver\" />\n                        </el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"数据库模式\">\n                        <el-select v-model=\"projectForm.databaseMode\" placeholder=\"请选择数据库模式\" style=\"width: 100%\"\n                          @change=\"handleDatabaseModeChange\">\n                          <el-option label=\"新建数据库\" value=\"new\" />\n                          <el-option label=\"已有数据库\" value=\"existing\" />\n                        </el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"项目中文名称\">\n                        <el-input v-model=\"projectForm.name\" placeholder=\"请输入项目中文名称\" />\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n\n                  <el-row :gutter=\"20\" v-if=\"projectForm.databaseMode === 'new'\">\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"项目编号\">\n                        <el-input v-model=\"projectForm.projectCode\" placeholder=\"项目编号\" />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"数据库名称\">\n                        <el-input v-model=\"projectForm.databaseName\" placeholder=\"请输入数据库名称\" />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"学校名称\">\n                        <el-input v-model=\"projectForm.schoolName\" placeholder=\"请输入学校名称\" />\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n\n                  <el-row :gutter=\"20\" v-if=\"projectForm.databaseMode === 'existing'\">\n                    <el-col :span=\"24\">\n                      <el-form-item label=\"选择数据库\">\n                        <el-select v-model=\"projectForm.selectedDatabase\" placeholder=\"请选择数据库\" style=\"width: 100%\"\n                          @change=\"handleProjectSelect\" :loading=\"projectsLoading\">\n                          <el-option v-for=\"db in availableDatabases\" :key=\"db.value\" :label=\"db.text\" :value=\"db.value\"\n                            :disabled=\"!db.value\" />\n                        </el-select>\n                        <div class=\"form-text\" v-if=\"availableDatabases.length > 0\">\n                          格式：项目编号--数据库名称 (项目中文名称)\n                        </div>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n\n                  <el-row :gutter=\"20\">\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"后台模板\">\n                        <el-input v-model=\"projectForm.backendTemplate\" placeholder=\"点击选择后台模板\" readonly\n                          @click=\"openTemplateModal\" />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"前台模板\">\n                        <el-input v-model=\"projectForm.frontendTemplate\" placeholder=\"点击选择前台模板\" readonly\n                          @click=\"openFrontTemplateModal\" />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"复制项目\">\n                        <el-input v-model=\"projectForm.copyProject\" placeholder=\"请输入要复制的项目\" />\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n\n                  <el-row :gutter=\"20\">\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"layer弹出层\">\n                        <el-select v-model=\"projectForm.layer\" style=\"width: 100%\">\n                          <el-option label=\"否\" value=\"否\" />\n                          <el-option label=\"是\" value=\"是\" />\n                        </el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"统计图表\">\n                        <el-select v-model=\"projectForm.charts\" style=\"width: 100%\">\n                          <el-option label=\"否\" value=\"否\" />\n                          <el-option label=\"是\" value=\"是\" />\n                        </el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <!-- 空列，保持布局平衡 -->\n                    </el-col>\n                  </el-row>\n\n                  <el-form-item label=\"Session信息\">\n                    <el-row :gutter=\"10\">\n                      <el-col :span=\"6\">\n                        <el-input v-model=\"projectForm.adminId\" placeholder=\"管理员ID\" />\n                      </el-col>\n                      <el-col :span=\"6\">\n                        <el-input v-model=\"projectForm.adminName\" placeholder=\"管理员姓名\" />\n                      </el-col>\n                      <el-col :span=\"6\">\n                        <el-input v-model=\"projectForm.adminRole\" placeholder=\"管理员角色\" />\n                      </el-col>\n                      <el-col :span=\"6\">\n                        <el-input v-model=\"projectForm.adminLoginName\" placeholder=\"登录名\" />\n                      </el-col>\n                    </el-row>\n                  </el-form-item>\n                </el-form>\n\n                <div class=\"form-actions\">\n                  <el-button type=\"primary\" @click=\"nextStep\" size=\"large\">\n                    🎯 下一步：设计表单\n                  </el-button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 设计表单 -->\n        <el-tab-pane label=\"设计表单\" name=\"form\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <Edit />\n              </el-icon>\n              设计表单\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>数据表管理</h3>\n                <p>设计您的数据库表结构和表单字段</p>\n              </div>\n\n              <!-- 项目信息显示 -->\n              <div v-if=\"currentProjectInfo\" class=\"project-info-section\">\n                <div class=\"project-info-card\">\n                  <h4>当前项目：{{ currentProjectInfo.name }}</h4>\n                  <div class=\"project-details\">\n                    <span class=\"project-detail-item\">\n                      <strong>项目编号：</strong>{{ currentProjectInfo.projectCode }}\n                    </span>\n                    <span class=\"project-detail-item\">\n                      <strong>数据库：</strong>{{ currentProjectInfo.databaseName }}\n                    </span>\n                    <span class=\"project-detail-item\">\n                      <strong>类型：</strong>{{ getProjectName(selectedProject) }}\n                    </span>\n                  </div>\n                </div>\n              </div>\n\n              <!-- 表单列表 -->\n              <div class=\"table-designer\">\n                <div class=\"table-list-header\">\n                  <h4>数据表列表</h4>\n                  <el-button type=\"primary\" :icon=\"Plus\" @click=\"openTableDesignModal()\">\n                    添加新表\n                  </el-button>\n                </div>\n\n                <div v-if=\"projectTablesLoading\" class=\"loading-section\">\n                  <el-icon class=\"is-loading\">\n                    <Loading />\n                  </el-icon>\n                  <span>正在加载表单数据...</span>\n                </div>\n\n                <div v-else-if=\"projectTables.length === 0\" class=\"empty-section\">\n                  <el-empty description=\"暂无数据表\">\n                    <el-button type=\"primary\" @click=\"openTableDesignModal()\">创建第一个表</el-button>\n                  </el-empty>\n                </div>\n\n                <div v-else class=\"table-list\">\n                  <div class=\"table-item\" v-for=\"table in projectTables\" :key=\"table.tid\"\n                    @click=\"openTableDesignModal(table)\">\n                    <div class=\"table-item-header\">\n                      <h5>{{ table.tword || table.tname }}</h5>\n                      <div class=\"table-item-actions\">\n                        <el-button size=\"small\" :icon=\"Edit\" @click.stop=\"openTableDesignModal(table)\">编辑</el-button>\n                        <el-button size=\"small\" type=\"danger\" :icon=\"Delete\"\n                          @click.stop=\"deleteTable(table)\">删除</el-button>\n                      </div>\n                    </div>\n                    <p class=\"table-item-description\">\n                      {{ table.tname }} ({{ getTableFieldCount(table) }}个字段)\n                    </p>\n                    <div class=\"table-item-functions\" v-if=\"table.tgn\">\n                      <span class=\"function-tag\" v-for=\"func in getTableFunctions(table)\" :key=\"func\">{{ func }}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"form-actions\" style=\"margin-top: 30px;\">\n                <el-button @click=\"activeTab = 'project'\" size=\"large\">\n                  ← 上一步：项目配置\n                </el-button>\n                <el-button type=\"primary\" @click=\"nextStep\" size=\"large\">\n                  下一步：生成项目 →\n                </el-button>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 生成项目 -->\n        <el-tab-pane label=\"生成项目\" name=\"generate\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <Cpu />\n              </el-icon>\n              生成项目\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>生成项目</h3>\n                <p>确认配置信息并生成您的项目</p>\n              </div>\n\n              <!-- 项目配置摘要 -->\n              <div class=\"generation-summary\">\n                <h4>项目配置摘要</h4>\n                <el-row :gutter=\"20\">\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">项目名称:</span>\n                      <span class=\"summary-value\">{{ projectForm.name || '未设置' }}</span>\n                    </div>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">数据库名称:</span>\n                      <span class=\"summary-value\">{{ projectForm.databaseName || '未设置' }}</span>\n                    </div>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">项目编号:</span>\n                      <span class=\"summary-value\">{{ projectForm.projectCode || '未设置' }}</span>\n                    </div>\n                  </el-col>\n                </el-row>\n                <el-row :gutter=\"20\" style=\"margin-top: 15px;\">\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">数据表数量:</span>\n                      <span class=\"summary-value\">{{ projectTables.length }} 个</span>\n                    </div>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">后台模板:</span>\n                      <span class=\"summary-value\">{{ projectForm.backendTemplate || '未选择' }}</span>\n                    </div>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">前台模板:</span>\n                      <span class=\"summary-value\">{{ projectForm.frontendTemplate || '未选择' }}</span>\n                    </div>\n                  </el-col>\n                </el-row>\n              </div>\n\n              <!-- 生成操作区域 -->\n              <div class=\"generation-actions\">\n                <el-button\n                  type=\"primary\"\n                  size=\"large\"\n                  @click=\"generateProject\"\n                  :loading=\"generationInProgress\"\n                  :disabled=\"!canGenerate || generationInProgress\">\n                  <el-icon v-if=\"!generationInProgress\">\n                    <Cpu />\n                  </el-icon>\n                  <el-icon v-else>\n                    <Loading />\n                  </el-icon>\n                  {{ generationInProgress ? '正在生成项目，请稍候...' : '🚀 生成项目' }}\n                </el-button>\n\n                <!-- 生成进度提示 -->\n                <div v-if=\"generationInProgress\" class=\"generation-progress\">\n                  <el-progress :percentage=\"100\" :show-text=\"false\" status=\"success\" :indeterminate=\"true\" />\n                  <p class=\"progress-text\">正在生成代码文件和项目结构...</p>\n                </div>\n              </div>\n\n              <!-- 项目生成结果显示区域 -->\n              <div v-if=\"generationResult\" class=\"generation-result\">\n                <h4>项目生成结果</h4>\n\n                <!-- 生成状态 -->\n                <div class=\"generation-status\" :class=\"generationResult.status\">\n                  <el-icon class=\"status-icon\">\n                    <Check v-if=\"generationResult.status === 'success'\" />\n                    <Close v-else />\n                  </el-icon>\n                  <span class=\"status-text\">{{ generationResult.message }}</span>\n                </div>\n\n                <!-- 文件列表 -->\n                <div v-if=\"generationResult.files\" class=\"file-list-section\">\n                  <h5>生成的文件列表 ({{ generationResult.files.savedCount }}/{{ generationResult.files.totalCount }})</h5>\n\n                  <!-- 成功文件列表 -->\n                  <div v-if=\"generationResult.files.savedFiles && generationResult.files.savedFiles.length > 0\" class=\"success-files\">\n                    <h6>✅ 成功生成的文件:</h6>\n                    <el-scrollbar max-height=\"200px\">\n                      <ul class=\"file-list success\">\n                        <li v-for=\"file in generationResult.files.savedFiles\" :key=\"file\">\n                          <el-icon class=\"file-icon\"><Document /></el-icon>\n                          <span class=\"file-name\">{{ file }}</span>\n                        </li>\n                      </ul>\n                    </el-scrollbar>\n                  </div>\n\n                  <!-- 失败文件列表 -->\n                  <div v-if=\"generationResult.files.failedFiles && generationResult.files.failedFiles.length > 0\" class=\"failed-files\">\n                    <h6>❌ 生成失败的文件:</h6>\n                    <el-scrollbar max-height=\"200px\">\n                      <ul class=\"file-list error\">\n                        <li v-for=\"file in generationResult.files.failedFiles\" :key=\"file.fileName\">\n                          <el-icon class=\"file-icon\"><Warning /></el-icon>\n                          <span class=\"file-name\">{{ file.fileName }}</span>\n                          <span class=\"file-error\">{{ file.error }}</span>\n                        </li>\n                      </ul>\n                    </el-scrollbar>\n                  </div>\n                </div>\n\n                <!-- 压缩结果 -->\n                <div v-if=\"generationResult.compression\" class=\"compression-result\">\n                  <h5>项目压缩结果</h5>\n                  <div class=\"compression-info\" :class=\"generationResult.compression.status\">\n                    <el-icon class=\"status-icon\">\n                      <Box v-if=\"generationResult.compression.status === 'success'\" />\n                      <Close v-else />\n                    </el-icon>\n                    <span class=\"compression-text\">{{ generationResult.compression.message }}</span>\n                    <div v-if=\"generationResult.compression.data\" class=\"compression-details\">\n                      <p>文件名: {{ generationResult.compression.data.zipFileName }}</p>\n                      <p>文件大小: {{ generationResult.compression.data.fileSize }}</p>\n                      <el-button type=\"success\" @click=\"downloadProject(generationResult.compression.data.zipFilePath)\">\n                        <el-icon><Download /></el-icon>\n                        下载项目文件\n                      </el-button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 数据 -->\n        <el-tab-pane label=\"数据\" name=\"data\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <DataBoard />\n              </el-icon>\n              数据\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>数据脚本管理</h3>\n                <p>生成和管理数据库建表及插入数据脚本</p>\n              </div>\n              <div class=\"data-section\">\n                <div class=\"data-toolbar\">\n                  <el-button type=\"primary\" :icon=\"Plus\" @click=\"generateDataScript\" :loading=\"dataGenerationInProgress\">生成数据脚本</el-button>\n                  <el-button :icon=\"DocumentCopy\" @click=\"copyDataScript\">复制脚本</el-button>\n                </div>\n                <div class=\"data-editor\">\n                  <el-input v-model=\"dataContent\" type=\"textarea\" :rows=\"15\" placeholder=\"数据脚本将在这里显示...\"\n                    class=\"data-textarea\" />\n                </div>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- SQL脚本 -->\n        <el-tab-pane label=\"SQL脚本\" name=\"sql\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <DocumentCopy />\n              </el-icon>\n              SQL脚本\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>SQL脚本管理</h3>\n                <p>生成和管理数据库脚本</p>\n              </div>\n              <div class=\"sql-section\">\n                <div class=\"sql-toolbar\">\n                  <el-button type=\"primary\" :icon=\"Plus\" @click=\"generateSqlScript\" :loading=\"sqlGenerationInProgress\">生成建表脚本</el-button>\n                  <el-button :icon=\"Download\" @click=\"exportSqlScript\">导出脚本</el-button>\n                  <el-button :icon=\"DocumentCopy\" @click=\"copySqlScript\">复制脚本</el-button>\n                </div>\n                <div class=\"sql-editor\">\n                  <el-input v-model=\"sqlContent\" type=\"textarea\" :rows=\"25\" placeholder=\"SQL脚本将在这里显示...\"\n                    class=\"sql-textarea\" />\n                </div>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 错误日志 -->\n        <el-tab-pane label=\"错误日志\" name=\"logs\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <Warning />\n              </el-icon>\n              错误日志\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>错误日志</h3>\n                <p>查看生成过程中的错误和警告信息</p>\n              </div>\n              <div class=\"logs-section\">\n                <div class=\"logs-toolbar\">\n                  <el-button :icon=\"Refresh\">刷新日志</el-button>\n                  <el-button :icon=\"Delete\">清空日志</el-button>\n                  <el-select v-model=\"logLevel\" placeholder=\"日志级别\" style=\"width: 120px\">\n                    <el-option label=\"全部\" value=\"all\" />\n                    <el-option label=\"错误\" value=\"error\" />\n                    <el-option label=\"警告\" value=\"warning\" />\n                    <el-option label=\"信息\" value=\"info\" />\n                  </el-select>\n                </div>\n                <div class=\"logs-content\">\n                  <div class=\"log-item\" v-for=\"(log, index) in logs\" :key=\"index\" :class=\"log.level\">\n                    <div class=\"log-time\">{{ log.time }}</div>\n                    <div class=\"log-level\">{{ log.level.toUpperCase() }}</div>\n                    <div class=\"log-message\">{{ log.message }}</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n      </el-tabs>\n    </div>\n\n    <!-- 登录对话框 -->\n    <el-dialog v-model=\"loginDialogVisible\" title=\"用户登录\" width=\"400px\" :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\" :show-close=\"false\">\n      <el-form :model=\"loginForm\" :rules=\"loginRules\" ref=\"loginFormRef\" label-width=\"80px\">\n        <el-form-item label=\"用户名\" prop=\"username\">\n          <el-input v-model=\"loginForm.username\" placeholder=\"请输入用户名\" />\n        </el-form-item>\n        <el-form-item label=\"密码\" prop=\"password\">\n          <el-input v-model=\"loginForm.password\" type=\"password\" placeholder=\"请输入密码\" @keyup.enter=\"handleLogin\" />\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button type=\"primary\" @click=\"handleLogin\" :loading=\"loginLoading\">\n            登录\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n\n    <!-- 表设计弹窗 -->\n    <el-dialog v-model=\"showTableDesignModal\" :title=\"currentTableDesign.id ? '编辑数据表' : '创建数据表'\" width=\"85%\"\n      :close-on-click-modal=\"false\" :before-close=\"closeTableDesignModal\" class=\"table-design-dialog\" top=\"5vh\"\n      destroy-on-close>\n      <div class=\"table-design-container\">\n        <!-- 自定义Tab导航 -->\n        <div class=\"custom-tabs\">\n          <div class=\"tab-nav\">\n            <div class=\"tab-item\" :class=\"{ active: currentTableTab === 'table-settings' }\"\n              @click=\"switchTableTab('table-settings')\">\n              <el-icon class=\"tab-icon\">\n                <Setting />\n              </el-icon>\n              <span class=\"tab-text\">表设置</span>\n            </div>\n            <div class=\"tab-item\" :class=\"{ active: currentTableTab === 'field-design' }\"\n              @click=\"switchTableTab('field-design')\">\n              <el-icon class=\"tab-icon\">\n                <DataBoard />\n              </el-icon>\n              <span class=\"tab-text\">字段设计</span>\n            </div>\n          </div>\n\n          <!-- Tab内容 -->\n          <div class=\"tab-content-wrapper\">\n            <!-- 表设置内容 -->\n            <div v-show=\"currentTableTab === 'table-settings'\" class=\"tab-content\">\n              <!-- 基本信息 -->\n              <div class=\"basic-info-section\">\n                <el-form :model=\"currentTableDesign\" label-width=\"100px\" class=\"design-form\" size=\"default\">\n                  <el-row :gutter=\"16\">\n                    <el-col :span=\"6\">\n                      <el-form-item label=\"表中文名称\" required>\n                        <el-input v-model=\"currentTableDesign.chineseName\" placeholder=\"请输入表的中文名称\" clearable />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"6\">\n                      <el-form-item label=\"表英文名称\" required>\n                        <el-input v-model=\"currentTableDesign.englishName\" placeholder=\"请输入表的英文名称\" clearable />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"6\">\n                      <el-form-item label=\"菜单显示顺序\">\n                        <el-input-number v-model=\"currentTableDesign.menuOrder\" :min=\"1\" :max=\"999\" placeholder=\"1\" style=\"width: 100%;\" />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"6\">\n                      <el-form-item label=\"生成数据条数\">\n                        <el-input-number v-model=\"currentTableDesign.generateDataCount\" :min=\"0\" :max=\"1000\"\n                          placeholder=\"生成数据条数\" style=\"width: 100%;\" />\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n\n                  <el-row :gutter=\"16\">\n                    <el-col :span=\"24\">\n                      <el-form-item label=\"AI助手\">\n                        <el-button type=\"primary\" @click=\"showAiGeneratorModal('table')\" style=\"width: 100%;\">\n                          <el-icon>\n                            <Cpu />\n                          </el-icon>\n                          AI生成表结构\n                        </el-button>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                </el-form>\n              </div>\n\n              <!-- 功能选择 -->\n              <div class=\"function-selection-section\">\n                <h4 style=\"margin: 20px 0 15px 0; color: #2c3e50; font-size: 16px;\">功能选择</h4>\n                <el-row :gutter=\"20\">\n                  <!-- 后台功能 -->\n                  <el-col :span=\"8\">\n                    <div class=\"function-group\">\n                      <h5>后台功能</h5>\n                      <div class=\"function-checkboxes\">\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendAdd\">后台添加</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendEdit\">后台修改</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendDelete\">后台删除</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendDetail\">后台详情</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendList\">后台列表</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.batchImport\">批量导入</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.batchExport\">批量导出</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendLogin\">后台登录</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendRegister\">后台注册</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendProfile\">后台个人信息</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendPassword\">后台修改密码</el-checkbox>\n                      </div>\n                    </div>\n                  </el-col>\n\n                  <!-- 前台功能 -->\n                  <el-col :span=\"8\">\n                    <div class=\"function-group\">\n                      <h5>前台功能</h5>\n                      <div class=\"function-checkboxes\">\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendAdd\">前台添加</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendEdit\">前台修改</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendDelete\">前台删除</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendDetail\">前台详情</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendList\">前台列表</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendLogin\">前台个人信息和密码</el-checkbox>\n                      </div>\n                    </div>\n                  </el-col>\n\n                  <!-- 小程序功能 -->\n                  <el-col :span=\"8\">\n                    <div class=\"function-group\">\n                      <h5>小程序功能</h5>\n                      <div class=\"function-checkboxes\">\n                        <el-checkbox v-model=\"currentTableDesign.functions.miniAdd\">小程序添加</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.miniEdit\">小程序编辑</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.miniDelete\">小程序删除</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.miniDetail\">小程序详情</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.miniList\">小程序List</el-checkbox>\n                      </div>\n                    </div>\n                  </el-col>\n                </el-row>\n              </div>\n            </div>\n\n            <!-- 字段设计内容 -->\n            <div v-show=\"currentTableTab === 'field-design'\" class=\"tab-content\">\n              <!-- 工具栏 -->\n              <div class=\"field-toolbar-compact\">\n                <el-space size=\"default\" wrap>\n                  <el-button type=\"success\" @click=\"addNewFieldToDesign\">\n                    <el-icon>\n                      <Plus />\n                    </el-icon>\n                    添加字段\n                  </el-button>\n                  <el-button type=\"warning\" @click=\"resetFields\">\n                    <el-icon>\n                      <Refresh />\n                    </el-icon>\n                    重置字段\n                  </el-button>\n                  <el-button type=\"danger\" @click=\"clearAllFields\">\n                    <el-icon>\n                      <Delete />\n                    </el-icon>\n                    清空字段\n                  </el-button>\n                  <el-divider direction=\"vertical\" />\n                  <el-text type=\"info\">\n                    当前字段数量: {{ currentTableDesign.fields?.length || 0 }}\n                  </el-text>\n                </el-space>\n              </div>\n\n              <!-- 字段表格 -->\n              <div class=\"field-table-section\">\n                <el-table :data=\"currentTableDesign.fields\" class=\"field-table\" border stripe\n                  empty-text=\"暂无字段，请点击添加字段按钮\" size=\"small\">\n                  <el-table-column label=\"序号\" type=\"index\" width=\"60\" align=\"center\" />\n\n                  <el-table-column label=\"中文名称\" min-width=\"100\">\n                    <template #default=\"{ row }\">\n                      <el-input v-model=\"row.chineseName\" placeholder=\"请输入中文名称\" size=\"small\" clearable />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"英文名称\" min-width=\"100\">\n                    <template #default=\"{ row }\">\n                      <el-input v-model=\"row.englishName\" placeholder=\"请输入英文名称\" size=\"small\" clearable />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"字段类型\" min-width=\"80\">\n                    <template #default=\"{ row }\">\n                      <el-select v-model=\"row.type\" placeholder=\"选择类型\" size=\"small\" style=\"width: 100%\">\n                        <el-option label=\"int\" value=\"int\" />\n                        <el-option label=\"varchar(50)\" value=\"varchar(50)\" />\n                        <el-option label=\"varchar(100)\" value=\"varchar(100)\" />\n                        <el-option label=\"varchar(200)\" value=\"varchar(200)\" />\n                        <el-option label=\"varchar(500)\" value=\"varchar(500)\" />\n                        <el-option label=\"text\" value=\"text\" />\n                        <el-option label=\"datetime\" value=\"datetime\" />\n                        <el-option label=\"decimal(10,2)\" value=\"decimal(10,2)\" />\n                      </el-select>\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"控件类型\" min-width=\"100\">\n                    <template #default=\"{ row }\">\n                      <el-select v-model=\"row.controlType\" placeholder=\"选择控件\" size=\"small\" style=\"width: 100%\">\n                        <el-option label=\"文本框\" value=\"文本框\" />\n                        <el-option label=\"多行文本\" value=\"多行文本\" />\n                        <el-option label=\"下拉框\" value=\"下拉框\" />\n                        <el-option label=\"单选按钮\" value=\"单选按钮\" />\n                        <el-option label=\"复选框\" value=\"复选框\" />\n                        <el-option label=\"日期选择\" value=\"日期选择\" />\n                        <el-option label=\"时间选择\" value=\"时间选择\" />\n                        <el-option label=\"文件上传\" value=\"文件上传\" />\n                        <el-option label=\"图片上传\" value=\"图片上传\" />\n                        <el-option label=\"编辑器\" value=\"编辑器\" />\n                        <el-option label=\"自动当前时间\" value=\"自动当前时间\" />\n                      </el-select>\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"必填\" width=\"60\" align=\"center\">\n                    <template #default=\"{ row }\">\n                      <el-checkbox v-model=\"row.required\" />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"搜索\" width=\"60\" align=\"center\">\n                    <template #default=\"{ row }\">\n                      <el-checkbox v-model=\"row.searchable\" />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"显示\" width=\"60\" align=\"center\">\n                    <template #default=\"{ row }\">\n                      <el-checkbox v-model=\"row.visible\" />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"存在\" width=\"60\" align=\"center\">\n                    <template #default=\"{ row }\">\n                      <el-checkbox v-model=\"row.existsCheck\" />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"操作\" width=\"160\" align=\"center\" fixed=\"right\">\n                    <template #default=\"{ row, $index }\">\n                      <div class=\"field-actions\">\n                        <el-button size=\"small\" type=\"primary\" @click=\"moveFieldUp($index)\" :disabled=\"$index === 0\"\n                          circle>\n                          <el-icon>\n                            <ArrowUp />\n                          </el-icon>\n                        </el-button>\n                        <el-button size=\"small\" type=\"primary\" @click=\"moveFieldDown($index)\"\n                          :disabled=\"$index === currentTableDesign.fields.length - 1\" circle>\n                          <el-icon>\n                            <ArrowDown />\n                          </el-icon>\n                        </el-button>\n                        <el-button size=\"small\" type=\"warning\" @click=\"editFieldSettings(row, $index)\" circle>\n                          <el-icon>\n                            <Edit />\n                          </el-icon>\n                        </el-button>\n                        <el-button size=\"small\" type=\"danger\" @click=\"deleteFieldFromDesign($index)\" circle>\n                          <el-icon>\n                            <Delete />\n                          </el-icon>\n                        </el-button>\n                      </div>\n                    </template>\n                  </el-table-column>\n                </el-table>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-space size=\"large\">\n            <el-button @click=\"closeTableDesignModal\" size=\"large\">\n              <el-icon>\n                <Close />\n              </el-icon>\n              <span>取消</span>\n            </el-button>\n            <el-button type=\"primary\" @click=\"saveTableDesign\" size=\"large\">\n              <el-icon>\n                <Check />\n              </el-icon>\n              <span>保存表设计</span>\n            </el-button>\n          </el-space>\n        </div>\n      </template>\n    </el-dialog>\n\n    <!-- AI生成器弹窗 -->\n    <el-dialog v-model=\"showAiGeneratorDialog\" title=\"AI生成表结构\" width=\"600px\" :close-on-click-modal=\"false\"\n      :before-close=\"hideAiGeneratorModal\" class=\"ai-generator-dialog\" destroy-on-close>\n      <div class=\"ai-generator-container\">\n        <div class=\"ai-generator-header\">\n          <h4>🤖 AI智能生成表结构</h4>\n          <p>描述您要生成的表结构，AI将自动为您创建完整的数据表设计</p>\n        </div>\n\n        <div class=\"ai-generator-body\">\n          <el-form label-width=\"100px\">\n            <el-form-item label=\"表描述\">\n              <el-input v-model=\"aiGeneratorInput\" type=\"textarea\" :rows=\"4\"\n                placeholder=\"请输入表的描述，例如：学生信息管理表、商品管理系统、订单管理表等...\" class=\"ai-generator-input\"\n                :disabled=\"aiGenerationInProgress\" />\n            </el-form-item>\n          </el-form>\n\n          <div class=\"ai-generator-example\">\n            <h5>💡 示例：</h5>\n            <div class=\"example-list\">\n              <div class=\"example-item\">\n                <strong>表结构描述：</strong>\"学生信息管理表\" 或 \"商品管理系统\" 或 \"订单管理表\"\n              </div>\n              <div class=\"example-item\">\n                <strong>功能说明：</strong>AI会根据您的描述自动生成包含合适字段的完整表结构\n              </div>\n            </div>\n          </div>\n        </div>\n\n      </div>\n\n      <template #footer>\n        <div class=\"ai-generator-footer\">\n          <el-button @click=\"hideAiGeneratorModal\" :disabled=\"aiGenerationInProgress\">取消</el-button>\n          <el-button type=\"primary\" @click=\"confirmAiGeneration\" :loading=\"aiGenerationInProgress\">\n            <el-icon v-if=\"!aiGenerationInProgress\">\n              <Cpu />\n            </el-icon>\n            {{ aiGenerationInProgress ? '生成中...' : '🚀 生成' }}\n          </el-button>\n        </div>\n      </template>\n    </el-dialog>\n\n    <!-- 后台模板选择弹窗 -->\n    <el-dialog v-model=\"showBackendTemplateDialog\" title=\"选择后台模板\" width=\"1000px\" :close-on-click-modal=\"false\"\n      class=\"template-dialog\" destroy-on-close>\n      <div class=\"template-container\">\n        <div class=\"template-grid-4col\" v-loading=\"templatesLoading\">\n          <div v-for=\"template in backendTemplates\" :key=\"template.sid\" class=\"template-item-4col\">\n            <div class=\"template-image\" @click=\"selectBackendTemplate(template)\">\n              <img v-if=\"template.memo4\" :src=\"getTemplateImageUrl(template.memo4)\" :alt=\"template.sname\"\n                @error=\"handleImageError\" />\n              <div v-else class=\"no-image\">\n                <el-icon>\n                  <Picture />\n                </el-icon>\n                <span>暂无预览</span>\n              </div>\n            </div>\n            <div class=\"template-info\">\n              <h4 @click=\"selectBackendTemplate(template)\">{{ template.sname }}</h4>\n              <p class=\"template-id\">模板ID: {{ template.sid }}</p>\n              <div class=\"template-actions\">\n                <el-button type=\"primary\" size=\"small\" @click=\"selectBackendTemplate(template)\">\n                  选择模板\n                </el-button>\n                <el-button type=\"info\" size=\"small\" @click=\"viewTemplateDetail(template)\">\n                  查看详情\n                </el-button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-dialog>\n\n    <!-- 前台模板选择弹窗 -->\n    <el-dialog v-model=\"showFrontendTemplateDialog\" title=\"选择前台模板\" width=\"1000px\" :close-on-click-modal=\"false\"\n      class=\"template-dialog\" destroy-on-close>\n      <div class=\"template-container\">\n        <div class=\"template-grid-4col\" v-loading=\"templatesLoading\">\n          <div v-for=\"template in frontendTemplates\" :key=\"template.sid\" class=\"template-item-4col\">\n            <div class=\"template-image\" @click=\"selectFrontendTemplate(template)\">\n              <img v-if=\"template.memo4\" :src=\"getTemplateImageUrl(template.memo4)\" :alt=\"template.sname\"\n                @error=\"handleImageError\" />\n              <div v-else class=\"no-image\">\n                <el-icon>\n                  <Picture />\n                </el-icon>\n                <span>暂无预览</span>\n              </div>\n            </div>\n            <div class=\"template-info\">\n              <h4 @click=\"selectFrontendTemplate(template)\">{{ template.sname }}</h4>\n              <p class=\"template-id\">模板ID: {{ template.sid }}</p>\n              <div class=\"template-actions\">\n                <el-button type=\"primary\" size=\"small\" @click=\"selectFrontendTemplate(template)\">\n                  选择模板\n                </el-button>\n                <el-button type=\"info\" size=\"small\" @click=\"viewTemplateDetail(template)\">\n                  查看详情\n                </el-button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-dialog>\n\n    <!-- 模板详情查看弹窗 -->\n    <el-dialog v-model=\"showTemplateDetailDialog\" title=\"模板详情\" width=\"90%\" :close-on-click-modal=\"false\"\n      class=\"template-detail-dialog\" destroy-on-close top=\"5vh\">\n      <div class=\"template-detail-container\" v-if=\"currentTemplateDetail\">\n        <div class=\"template-detail-header\">\n          <h3>{{ currentTemplateDetail.sname }}</h3>\n          <p class=\"template-detail-id\">模板ID: {{ currentTemplateDetail.sid }}</p>\n        </div>\n        <div class=\"template-detail-image-container\">\n          <div v-if=\"currentTemplateDetail.memo4\" class=\"template-detail-image\">\n            <el-image\n              :src=\"getTemplateImageUrl(currentTemplateDetail.memo4)\"\n              :alt=\"currentTemplateDetail.sname\"\n              fit=\"contain\"\n              :preview-src-list=\"[getTemplateImageUrl(currentTemplateDetail.memo4)]\"\n              :initial-index=\"0\"\n              preview-teleported\n              class=\"template-preview-image\"\n            />\n          </div>\n          <div v-else class=\"no-image-large\">\n            <el-icon>\n              <Picture />\n            </el-icon>\n            <span>暂无预览图片</span>\n          </div>\n        </div>\n        <div class=\"template-detail-tips\">\n          <el-alert\n            title=\"提示\"\n            type=\"info\"\n            :closable=\"false\"\n            show-icon>\n            <template #default>\n              点击图片可以放大查看，支持缩放和拖拽操作\n            </template>\n          </el-alert>\n        </div>\n      </div>\n      <template #footer>\n        <div class=\"template-detail-footer\">\n          <el-button @click=\"showTemplateDetailDialog = false\" size=\"large\">关闭</el-button>\n        </div>\n      </template>\n    </el-dialog>\n\n    <!-- 字段设置弹窗 -->\n    <el-dialog v-model=\"showFieldSettingsDialog\" title=\"字段设置\" width=\"600px\" :close-on-click-modal=\"false\"\n      class=\"field-settings-dialog\" destroy-on-close>\n      <div class=\"field-settings-container\" v-if=\"currentFieldSettings\">\n        <el-form :model=\"currentFieldSettings\" label-width=\"100px\" size=\"default\">\n          <el-form-item label=\"字段名称\">\n            <el-input v-model=\"currentFieldSettings.chineseName\" readonly />\n          </el-form-item>\n\n          <el-form-item label=\"关联表\">\n            <el-input v-model=\"currentFieldSettings.relatedTable\"\n              placeholder=\"例如：doro,dbid,dormitory,1\"\n              @click=\"showRelatedTableSelector\"\n              readonly\n              style=\"cursor: pointer;\">\n              <template #suffix>\n                <el-icon style=\"cursor: pointer;\">\n                  <View />\n                </el-icon>\n              </template>\n            </el-input>\n          </el-form-item>\n\n          <el-form-item label=\"是否必填\">\n            <el-checkbox v-model=\"showInSearchList\">搜索列表显示</el-checkbox>\n          </el-form-item>\n\n          <el-form-item label=\"自定义选项\">\n            <el-input v-model=\"currentFieldSettings.customOptions\"\n              type=\"textarea\"\n              :rows=\"6\"\n              placeholder=\"一行一个选项，例如：&#10;选项1&#10;选项2&#10;选项3\" />\n          </el-form-item>\n        </el-form>\n      </div>\n\n      <template #footer>\n        <div class=\"field-settings-footer\">\n          <el-button @click=\"closeFieldSettingsDialog\">取消</el-button>\n          <el-button type=\"primary\" @click=\"saveFieldSettings\">保存</el-button>\n        </div>\n      </template>\n    </el-dialog>\n\n    <!-- 关联表选择弹窗 -->\n    <el-dialog v-model=\"showRelatedTableDialog\" title=\"选择关联表\" width=\"800px\" :close-on-click-modal=\"false\"\n      class=\"related-table-dialog\" destroy-on-close>\n      <div class=\"related-table-container\">\n        <el-table :data=\"availableRelatedTables\" border stripe size=\"small\" max-height=\"400\">\n          <el-table-column label=\"主键ID\" prop=\"primaryKey\" width=\"80\" align=\"center\" />\n          <el-table-column label=\"名称\" prop=\"displayName\" width=\"120\" />\n          <el-table-column label=\"表名称\" prop=\"tableName\" width=\"150\" />\n          <el-table-column label=\"联动\" width=\"100\" align=\"center\">\n            <template #default=\"{ row }\">\n              <el-input v-model=\"row.linkValue\" size=\"small\" style=\"width: 60px;\" />\n            </template>\n          </el-table-column>\n          <el-table-column label=\"选择\" width=\"80\" align=\"center\">\n            <template #default=\"{ row }\">\n              <el-checkbox v-model=\"row.selected\" />\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n\n      <template #footer>\n        <div class=\"related-table-footer\">\n          <el-button @click=\"closeRelatedTableDialog\">取消</el-button>\n          <el-button type=\"primary\" @click=\"confirmRelatedTableSelection\">确定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<style scoped>\n  @import '../../styles/CodeGenerator.css';\n\n  /* 生成进度样式 */\n  .generation-progress {\n    margin-top: 20px;\n    padding: 20px;\n    background: #f8f9fa;\n    border-radius: 8px;\n    text-align: center;\n  }\n\n  .progress-text {\n    margin-top: 10px;\n    color: #606266;\n    font-size: 14px;\n  }\n\n  /* 生成结果样式 */\n  .generation-result {\n    margin-top: 30px;\n    padding: 20px;\n    border: 1px solid #e4e7ed;\n    border-radius: 8px;\n    background: #fff;\n  }\n\n  .generation-status {\n    display: flex;\n    align-items: center;\n    margin-bottom: 20px;\n    padding: 15px;\n    border-radius: 6px;\n  }\n\n  .generation-status.success {\n    background: #f0f9ff;\n    border: 1px solid #67c23a;\n    color: #67c23a;\n  }\n\n  .generation-status.error {\n    background: #fef0f0;\n    border: 1px solid #f56c6c;\n    color: #f56c6c;\n  }\n\n  .status-icon {\n    margin-right: 10px;\n    font-size: 18px;\n  }\n\n  .status-text {\n    font-weight: 500;\n    font-size: 16px;\n  }\n\n  /* 文件列表样式 */\n  .file-list-section {\n    margin-top: 20px;\n  }\n\n  .file-list {\n    list-style: none;\n    padding: 0;\n    margin: 0;\n  }\n\n  .file-list li {\n    display: flex;\n    align-items: center;\n    padding: 8px 12px;\n    margin-bottom: 4px;\n    border-radius: 4px;\n    background: #f8f9fa;\n  }\n\n  .file-list.success li {\n    background: #f0f9ff;\n    border-left: 3px solid #67c23a;\n  }\n\n  .file-list.error li {\n    background: #fef0f0;\n    border-left: 3px solid #f56c6c;\n  }\n\n  .file-icon {\n    margin-right: 8px;\n    color: #909399;\n  }\n\n  .file-name {\n    flex: 1;\n    font-family: 'Courier New', monospace;\n    font-size: 13px;\n  }\n\n  .file-error {\n    color: #f56c6c;\n    font-size: 12px;\n    margin-left: 10px;\n  }\n\n  /* 压缩结果样式 */\n  .compression-result {\n    margin-top: 20px;\n    padding: 15px;\n    border: 1px solid #e4e7ed;\n    border-radius: 6px;\n    background: #fafafa;\n  }\n\n  .compression-info {\n    display: flex;\n    align-items: center;\n    flex-wrap: wrap;\n  }\n\n  .compression-text {\n    margin-left: 10px;\n    font-weight: 500;\n  }\n\n  .compression-details {\n    width: 100%;\n    margin-top: 15px;\n    padding-top: 15px;\n    border-top: 1px solid #e4e7ed;\n  }\n\n  .compression-details p {\n    margin: 5px 0;\n    color: #606266;\n  }\n</style>\n\n<script>\n  import { ref, reactive, onMounted, nextTick, computed } from 'vue'\n  import { ElMessage, ElMessageBox } from 'element-plus'\n  import request, { base } from \"../../../utils/http\"\n  import {\n    Setting, Folder, Edit, Cpu, DataBoard, DocumentCopy, Warning,\n    Plus, View, Download, Delete, Document, Monitor, Box, Refresh,\n    User, ArrowDown, ArrowUp, Loading, Close, Check, Picture\n  } from '@element-plus/icons-vue'\n\n  export default {\n    name: 'CodeGenerator',\n    components: {\n      Setting, Folder, Edit, Cpu, DataBoard, DocumentCopy, Warning,\n      Plus, View, Download, Delete, Document, Monitor, Box, Refresh,\n      User, ArrowDown, ArrowUp, Loading, Close, Check, Picture\n    },\n    setup() {\n      const activeTab = ref('project')\n      const logLevel = ref('all')\n\n      // 登录相关状态\n      const isLoggedIn = ref(false)\n      const loginDialogVisible = ref(false)\n      const loginLoading = ref(false)\n      const loginFormRef = ref(null)\n\n      // 用户信息\n      const userInfo = reactive({\n        username: '',\n        loginTime: ''\n      })\n\n      // 登录表单\n      const loginForm = reactive({\n        username: '',\n        password: ''\n      })\n\n      // 登录表单验证规则\n      const loginRules = {\n        username: [\n          { required: true, message: '请输入用户名', trigger: 'blur' }\n        ],\n        password: [\n          { required: true, message: '请输入密码', trigger: 'blur' }\n        ]\n      }\n\n      // 项目选择和配置\n      const selectedProject = ref('')\n      const projectsLoading = ref(false)\n      const availableDatabases = ref([\n        { value: '', text: '请选择数据库' }\n      ])\n\n      const projectForm = reactive({\n        databaseType: 'mysql',\n        databaseMode: 'new',\n        projectCode: '',\n        databaseName: '',\n        selectedDatabase: '',\n        name: '',\n        packageName: 'com',\n        backendTemplate: '',\n        frontendTemplate: '',\n        layer: '否',\n        charts: '否',\n        schoolName: '',\n        adminId: '',\n        adminName: '',\n        adminRole: '',\n        adminLoginName: '',\n        copyProject: ''\n      })\n\n      // 项目表单相关\n      const currentProjectInfo = ref(null)\n      const projectTables = ref([])\n      const projectTablesLoading = ref(false)\n\n      const formFields = ref([\n        { name: 'id', type: 'Long' },\n        { name: 'name', type: 'String' },\n        { name: 'email', type: 'String' },\n        { name: 'createTime', type: 'Date' }\n      ])\n\n      const tableData = ref([\n        { name: 'user', fields: 8, status: '已配置', updateTime: '2024-01-15 10:30:00' },\n        { name: 'role', fields: 5, status: '未配置', updateTime: '2024-01-15 09:15:00' },\n        { name: 'permission', fields: 6, status: '已配置', updateTime: '2024-01-14 16:45:00' }\n      ])\n\n      const sqlContent = ref(``)\n\n      const logs = ref([\n        { time: '2024-01-15 10:30:15', level: 'info', message: '开始生成代码...' },\n        { time: '2024-01-15 10:30:16', level: 'success', message: '实体类生成成功' },\n        { time: '2024-01-15 10:30:17', level: 'warning', message: '字段名称建议使用驼峰命名' },\n        { time: '2024-01-15 10:30:18', level: 'error', message: '数据库连接失败，请检查配置' }\n      ])\n\n      // Cookie操作工具函数\n      const setCookie = (name, value, days) => {\n        const expires = new Date()\n        expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000))\n        document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`\n      }\n\n      const getCookie = (name) => {\n        const nameEQ = name + \"=\"\n        const ca = document.cookie.split(';')\n        for (let i = 0; i < ca.length; i++) {\n          let c = ca[i]\n          while (c.charAt(0) === ' ') c = c.substring(1, c.length)\n          if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length)\n        }\n        return null\n      }\n\n      const deleteCookie = (name) => {\n        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`\n      }\n\n      // 检查登录状态\n      const checkLoginStatus = () => {\n        // 首先检查sessionStorage中的用户信息\n        const sessionUser = sessionStorage.getItem(\"user\")\n        const sessionUserLname = sessionStorage.getItem(\"userLname\")\n\n        if (sessionUser && sessionUserLname) {\n          try {\n            JSON.parse(sessionUser) // 验证JSON格式\n            userInfo.username = sessionUserLname\n            userInfo.loginTime = new Date().toLocaleString()\n            isLoggedIn.value = true\n            return\n          } catch (error) {\n            console.error('解析sessionStorage用户信息失败:', error)\n          }\n        }\n\n        // 如果sessionStorage中没有，再检查Cookie\n        const savedUser = getCookie('codeGeneratorUser')\n        if (savedUser) {\n          try {\n            const userData = JSON.parse(decodeURIComponent(savedUser))\n            userInfo.username = userData.username\n            userInfo.loginTime = userData.loginTime\n            isLoggedIn.value = true\n          } catch (error) {\n            console.error('解析Cookie用户信息失败:', error)\n            deleteCookie('codeGeneratorUser')\n          }\n        } else {\n          loginDialogVisible.value = true\n        }\n      }\n\n      // 处理登录\n      const handleLogin = async () => {\n        if (!loginFormRef.value) return\n\n        // 基本验证\n        if (!loginForm.username) {\n          ElMessage.warning('请输入用户名')\n          return\n        }\n        if (!loginForm.password) {\n          ElMessage.warning('请输入密码')\n          return\n        }\n\n        try {\n          loginLoading.value = true\n\n          // 调用登录API\n          const url = base + \"/admin/login\"\n          const loginData = {\n            lname: loginForm.username,\n            pwd: loginForm.password\n          }\n\n          const res = await request.post(url, loginData)\n          loginLoading.value = false\n\n          if (res.code == 200) {\n            console.log('登录成功:', JSON.stringify(res.resdata))\n\n            // 保存用户信息到sessionStorage（参考管理员登录）\n            sessionStorage.setItem(\"user\", JSON.stringify(res.resdata))\n            sessionStorage.setItem(\"userLname\", res.resdata.lname)\n            sessionStorage.setItem(\"role\", \"管理员\")\n\n            // 更新本地状态\n            userInfo.username = res.resdata.lname\n            userInfo.loginTime = new Date().toLocaleString()\n\n            // 保存到Cookie，有效期15天\n            const userData = {\n              username: res.resdata.lname,\n              loginTime: userInfo.loginTime,\n              userId: res.resdata.id\n            }\n            setCookie('codeGeneratorUser', encodeURIComponent(JSON.stringify(userData)), 15)\n\n            isLoggedIn.value = true\n            loginDialogVisible.value = false\n\n            // 重置表单\n            loginForm.username = ''\n            loginForm.password = ''\n\n            ElMessage.success('登录成功！')\n          } else {\n            ElMessage.error(res.msg || '登录失败')\n          }\n        } catch (error) {\n          loginLoading.value = false\n          console.error('登录失败:', error)\n          ElMessage.error('登录失败，请检查网络连接')\n        }\n      }\n\n      // 处理用户下拉菜单命令\n      const handleUserCommand = (command) => {\n        if (command === 'logout') {\n          handleLogout()\n        }\n      }\n\n      // 退出登录\n      const handleLogout = () => {\n        // 清除Cookie\n        deleteCookie('codeGeneratorUser')\n\n        // 清除sessionStorage\n        sessionStorage.removeItem(\"user\")\n        sessionStorage.removeItem(\"userLname\")\n        sessionStorage.removeItem(\"role\")\n\n        // 重置状态\n        isLoggedIn.value = false\n        userInfo.username = ''\n        userInfo.loginTime = ''\n        loginDialogVisible.value = true\n\n        ElMessage.success('已退出登录')\n      }\n\n      // 项目类型选择\n      const selectProject = (projectType) => {\n        selectedProject.value = projectType\n        console.log('选择项目类型:', projectType)\n      }\n\n      // 获取项目名称\n      const getProjectName = (projectType) => {\n        const projectNames = {\n          'springboot-thymeleaf': 'SpringBoot + Thymeleaf',\n          'springboot-miniprogram': 'SpringBoot + 小程序',\n          'springboot-vue': 'SpringBoot + Vue',\n          'ssm-vue': 'SSM + Vue'\n        }\n        return projectNames[projectType] || projectType\n      }\n\n      // 打开模板选择弹窗\n      const openTemplateModal = () => {\n        console.log('打开后台模板选择弹窗')\n        showBackendTemplateSelector()\n      }\n\n      const openFrontTemplateModal = () => {\n        console.log('打开前台模板选择弹窗')\n        showFrontendTemplateSelector()\n      }\n\n      // 处理数据库模式变化\n      const handleDatabaseModeChange = (mode) => {\n        if (mode === 'existing') {\n          loadProjects()\n        } else {\n          // 清空已有数据库选项\n          availableDatabases.value = [{ value: '', text: '请选择数据库' }]\n          projectForm.selectedDatabase = ''\n        }\n      }\n\n      // 加载项目列表\n      const loadProjects = async () => {\n        try {\n          projectsLoading.value = true\n          const url = base + \"/projects/list?currentPage=1&pageSize=1000\"\n          const params = {\n\n          }\n\n          const res = await request.post(url, { params })\n\n          if (res.code === 200) {\n            const projects = res.resdata || []\n            availableDatabases.value = [\n              { value: '', text: '请选择数据库' },\n              ...projects.map(project => ({\n                value: project.pid,\n                text: `${project.pno}--${project.daname} (${project.pname})`\n              }))\n            ]\n          } else {\n            ElMessage.error('加载项目列表失败')\n          }\n        } catch (error) {\n          console.error('加载项目列表失败:', error)\n          ElMessage.error('加载项目列表失败，请检查网络连接')\n        } finally {\n          projectsLoading.value = false\n        }\n      }\n\n      // 处理项目选择\n      const handleProjectSelect = async (projectId) => {\n        if (!projectId) return\n\n        try {\n          const url = base + \"/projects/get?id=\" + projectId;\n\n\n          const res = await request.post(url, {})\n\n          if (res.code === 200) {\n            const project = res.resdata\n            // 初始化表单数据\n            projectForm.projectCode = project.pno || ''\n            projectForm.databaseName = project.daname || ''\n            projectForm.name = project.pname || ''\n            projectForm.databaseType = project.dtype || 'mysql'\n            projectForm.backendTemplate = project.by1 || ''\n            projectForm.frontendTemplate = project.by2 || ''\n            projectForm.layer = project.by4 || '否'\n            projectForm.charts = project.by5 || '否'\n            projectForm.schoolName = project.by6 || ''\n\n            // 解析Session信息\n            if (project.by3) {\n              const sessionInfo = project.by3.split(',')\n              projectForm.adminId = sessionInfo[0] || ''\n              projectForm.adminName = sessionInfo[1] || ''\n              projectForm.adminRole = sessionInfo[2] || ''\n              projectForm.adminLoginName = sessionInfo[3] || ''\n            }\n\n            ElMessage.success('项目信息加载成功')\n          } else {\n            ElMessage.error('加载项目详情失败')\n          }\n        } catch (error) {\n          console.error('加载项目详情失败:', error)\n          ElMessage.error('加载项目详情失败，请检查网络连接')\n        }\n      }\n\n      // 保存或更新项目到数据库\n      const saveOrUpdateProject = async () => {\n        try {\n          // 构建Session信息\n          const sessionInfo = [\n            projectForm.adminId,\n            projectForm.adminName,\n            projectForm.adminRole,\n            projectForm.adminLoginName\n          ].join(',')\n\n          const projectData = {\n            ptype: selectedProject.value,\n            dtype: projectForm.databaseType,\n            pflag: projectForm.databaseMode === 'new' ? '1' : '2',\n            pno: projectForm.projectCode,\n            daname: projectForm.databaseName,\n            pname: projectForm.name,\n            by1: projectForm.backendTemplate,\n            by2: projectForm.frontendTemplate,\n            by3: sessionInfo,\n            by4: projectForm.layer,\n            by5: projectForm.charts,\n            by6: projectForm.schoolName,\n            by7: projectForm.copyProject,\n            lname: userInfo.username\n          }\n\n          // 判断是新建还是更新\n          const isUpdate = currentProjectInfo.value && currentProjectInfo.value.pid\n          let url, res\n\n          if (isUpdate) {\n            // 更新项目\n            projectData.pid = currentProjectInfo.value.pid\n            url = base + \"/projects/update\"\n            res = await request.post(url, projectData)\n          } else {\n            // 新建项目\n            url = base + \"/projects/add\"\n            res = await request.post(url, projectData)\n          }\n\n          if (res.code === 200) {\n            const message = isUpdate ? '项目更新成功' : '项目保存成功'\n            ElMessage.success(message)\n\n            // 如果是新建项目，保存返回的项目ID\n            if (!isUpdate && res.resdata && res.resdata.pid) {\n              currentProjectInfo.value = {\n                ...currentProjectInfo.value,\n                pid: res.resdata.pid\n              }\n            }\n\n            return { success: true, projectId: res.resdata?.pid || currentProjectInfo.value?.pid }\n          } else {\n            ElMessage.error(res.msg || '项目保存失败')\n            return { success: false }\n          }\n        } catch (error) {\n          console.error('保存项目失败:', error)\n          ElMessage.error('保存项目失败，请检查网络连接')\n          return { success: false }\n        }\n      }\n\n      // 加载项目表单列表\n      const loadProjectTables = async (projectId) => {\n        try {\n          projectTablesLoading.value = true\n          const url = base + \"/tables/list?currentPage=1&pageSize=1000\"\n          const params = {\n            pid: projectId\n          }\n\n          const res = await request.post(url, params)\n\n          if (res.code === 200) {\n            projectTables.value = res.resdata || []\n\n            // 为每个表加载字段数据\n            for (let table of projectTables.value) {\n              try {\n                const fieldsUrl = base + \"/mores/list?currentPage=1&pageSize=100\"\n                const fieldsRes = await request.post(fieldsUrl, { tid: table.tid })\n\n                if (fieldsRes.code === 200 && fieldsRes.resdata) {\n                  // 将字段数据转换为前端格式\n                  table.fields = fieldsRes.resdata.map(field => ({\n                    id: field.mid,\n                    tid: field.tid,\n                    chineseName: field.mozname,\n                    englishName: field.moname,\n                    type: field.motype,\n                    controlType: field.moflag,\n                    required: field.moyz === '1',\n                    searchable: field.mobt === '1',\n                    visible: field.by1 === '1',\n                    existsCheck: field.by2 === '1',\n                    relatedTable: field.by3 || '', // 关联表\n                    customOptions: field.by4 || '' // 自定义选项\n                  }))\n\n                  // 设置表的生成数据条数\n                  table.generateDataCount = parseInt(table.by1 || '0')\n                } else {\n                  table.fields = []\n                }\n              } catch (error) {\n                console.warn('加载表字段失败:', table.tname, error)\n                table.fields = []\n              }\n            }\n\n            console.log('加载项目表单成功:', projectTables.value)\n          } else {\n            ElMessage.error('加载项目表单失败')\n          }\n        } catch (error) {\n          console.error('加载项目表单失败:', error)\n          ElMessage.error('加载项目表单失败，请检查网络连接')\n        } finally {\n          projectTablesLoading.value = false\n        }\n      }\n\n      // 表设计相关\n      const showTableDesignModal = ref(false)\n      const currentTableTab = ref('table-settings')\n      const currentTableDesign = ref({\n        id: null,\n        chineseName: '',\n        englishName: '',\n        menuOrder: 1,\n        generateData: '0',\n        generateDataCount: 0,\n        functions: {\n          backendAdd: true, backendEdit: true, backendDelete: true,\n          backendDetail: true, backendList: false, batchImport: false,\n          batchExport: false, backendLogin: false, backendRegister: false,\n          backendProfile: false, backendPassword: false,\n          frontendAdd: false, frontendEdit: false, frontendDelete: false,\n          frontendDetail: false, frontendList: false, frontendLogin: false,\n          miniAdd: false, miniEdit: false, miniDelete: false,\n          miniDetail: false, miniList: false\n        },\n        fields: [\n          {\n            id: 1,\n            chineseName: '主键ID',\n            englishName: 'id',\n            type: 'int',\n            controlType: '文本框',\n            required: true,\n            searchable: false,\n            visible: true,\n            existsCheck: false,\n            relatedTable: '',\n            customOptions: ''\n          }\n        ]\n      })\n\n      // 功能选择的响应式数组\n      const backendFunctions = ref([])\n      const frontendFunctions = ref([])\n      const miniFunctions = ref([])\n\n      // 重置字段\n      const resetFields = () => {\n        currentTableDesign.value.fields = [\n          {\n            id: 1,\n            chineseName: '主键ID',\n            englishName: 'id',\n            type: 'int',\n            controlType: '文本框',\n            required: true,\n            searchable: false,\n            visible: true,\n            existsCheck: false\n          }\n        ]\n        ElMessage.success('字段已重置')\n      }\n\n      // AI生成器相关状态\n      const showAiGeneratorDialog = ref(false)\n      const aiGeneratorInput = ref('')\n      const aiGenerationInProgress = ref(false)\n\n      // 模板选择相关状态\n      const showBackendTemplateDialog = ref(false)\n      const showFrontendTemplateDialog = ref(false)\n      const showTemplateDetailDialog = ref(false)\n      const backendTemplates = ref([])\n      const frontendTemplates = ref([])\n      const templatesLoading = ref(false)\n      const currentTemplateDetail = ref(null)\n\n      // 项目生成相关状态\n      const generationInProgress = ref(false)\n      const generationResult = ref(null)\n\n      // SQL脚本生成相关状态\n      const sqlGenerationInProgress = ref(false)\n\n      // 数据脚本生成相关状态\n      const dataGenerationInProgress = ref(false)\n      const dataContent = ref('')\n\n      // 字段设置相关状态\n      const showFieldSettingsDialog = ref(false)\n      const currentFieldSettings = ref(null)\n      const currentFieldIndex = ref(-1)\n      const showInSearchList = ref(false)\n\n      // 关联表选择相关状态\n      const showRelatedTableDialog = ref(false)\n      const availableRelatedTables = ref([])\n\n      // 显示AI生成器弹窗\n      const showAiGeneratorModal = () => {\n        aiGeneratorInput.value = ''\n        showAiGeneratorDialog.value = true\n        nextTick(() => {\n          // 聚焦到输入框\n          const inputElement = document.querySelector('.ai-generator-input')\n          if (inputElement) {\n            inputElement.focus()\n          }\n        })\n      }\n\n      // 隐藏AI生成器弹窗\n      const hideAiGeneratorModal = () => {\n        showAiGeneratorDialog.value = false\n        aiGeneratorInput.value = ''\n      }\n\n      // 确认AI生成\n      const confirmAiGeneration = async () => {\n        const description = aiGeneratorInput.value.trim()\n        if (!description) {\n          ElMessage.warning('请输入描述内容')\n          return\n        }\n\n        // 隐藏弹窗\n        hideAiGeneratorModal()\n\n        // 调用AI生成\n        await doubaoGenerate(description)\n      }\n\n      // 调用豆包AI生成表单\n      const doubaoGenerate = async (str) => {\n        if (aiGenerationInProgress.value) {\n          ElMessage.warning('AI正在生成中，请稍候...')\n          return\n        }\n\n        // 构建prompt\n        const input_text = \"用户:users\\n\" +\n          \"aid|lname|password|role\\n\" +\n          \"用户id|用户名|密码|身份\\n\" +\n          \"int|varchar(50)|varchar(50)|int\\n\" +\n          \"\\n\" +\n          \"学习上面的格式。格式说明如下。\\n\" +\n          \"第1行表中文名称:表英文名称。\\n\" +\n          \"第2行字段列表，字段简写\\n\" +\n          \"第3行字段对应中文\\n\" +\n          \"第4行字段类型。如果是字符型加上长度。\\n\" +\n          \"\\n\" +\n          \"按上面的格式生成下面的内容。不要注释，只返回格式的内容\\n\" + str\n\n        console.log('发送给豆包AI的内容:', input_text)\n\n        const settings = {\n          url: \"https://ark.cn-beijing.volces.com/api/v3/chat/completions\",\n          method: \"POST\",\n          timeout: 30000,\n          headers: {\n            \"Authorization\": \"Bearer 8d71b27a-b4c9-484e-896b-247f7dda5412\",\n            \"Content-Type\": \"application/json\"\n          },\n          data: JSON.stringify({\n            \"model\": \"doubao-1.5-pro-32k-250115\",\n            \"messages\": [\n              {\n                \"role\": \"system\",\n                \"content\": \"你是一个数据库设计专家，专门帮助用户设计数据表结构。请严格按照指定的格式返回结果，不要添加任何额外的说明或注释，表名和字段中不要用下划线，不要大写字母。不要用关键字和保留字\"\n              },\n              {\n                \"role\": \"user\",\n                \"content\": input_text\n              }\n            ]\n          })\n        }\n\n        // 显示生成中状态\n        aiGenerationInProgress.value = true\n        ElMessage.info('正在调用豆包AI生成表结构，请稍候...')\n\n        try {\n          const response = await fetch(settings.url, {\n            method: settings.method,\n            headers: settings.headers,\n            body: settings.data\n          })\n\n          if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`)\n          }\n\n          const result = await response.json()\n          aiGenerationInProgress.value = false\n\n          // 从豆包AI响应中提取内容\n          const generatedContent = result.choices[0].message.content\n          console.log('豆包AI生成的原始内容:', generatedContent)\n\n          // 清理返回内容\n          const cleanedContent = generatedContent\n            .replace(/```[\\s\\S]*?\\n/, '') // 移除开头的markdown代码块标记\n            .replace(/\\n```[\\s\\S]*?$/, '') // 移除结尾的markdown代码块标记\n            .replace(/^\\s+|\\s+$/g, '') // 移除首尾空白\n            .replace(/\\r\\n/g, '\\n') // 统一换行符\n            .replace(/\\r/g, '\\n') // 处理Mac格式换行符\n\n          console.log('清理后的内容:', cleanedContent)\n\n          // 检查内容是否为空\n          if (!cleanedContent || cleanedContent.trim() === '') {\n            throw new Error('豆包AI返回的内容为空')\n          }\n\n          createFormFromAI(cleanedContent)\n          ElMessage.success('AI生成成功！')\n\n        } catch (error) {\n          aiGenerationInProgress.value = false\n          console.error('处理豆包AI响应时出错:', error)\n          ElMessage.error('AI生成失败：' + error.message)\n        }\n      }\n\n      // 解析AI生成的内容并创建表单\n      const createFormFromAI = (content) => {\n        try {\n          console.log('开始解析AI生成的内容:', content)\n\n          // 按行分割内容，移除空行\n          const allLines = content.split('\\n')\n          const lines = allLines.map(line => line.trim()).filter(line => line !== '')\n          console.log('过滤后的有效行:', lines)\n\n          if (lines.length < 4) {\n            throw new Error(`豆包AI返回的格式不完整，需要4行内容，实际只有${lines.length}行`)\n          }\n\n          // 解析第1行：表名\n          const tableNameLine = lines[0]\n          const tableNameMatch = tableNameLine.match(/^(.+):(.+)$/)\n          if (!tableNameMatch) {\n            throw new Error('表名格式不正确，应为：中文名:英文名，实际为：' + tableNameLine)\n          }\n\n          const chineseName = tableNameMatch[1].trim()\n          const englishName = tableNameMatch[2].trim()\n\n          // 解析第2行：英文字段名\n          const englishFields = lines[1].split('|').map(field => field.trim()).filter(field => field !== '')\n\n          // 解析第3行：中文字段名\n          const chineseFields = lines[2].split('|').map(field => field.trim()).filter(field => field !== '')\n\n          // 解析第4行：字段类型\n          const fieldTypes = lines[3].split('|').map(type => type.trim()).filter(type => type !== '')\n\n          // 验证字段数量一致性\n          if (englishFields.length !== chineseFields.length || englishFields.length !== fieldTypes.length) {\n            throw new Error(`字段数量不匹配：英文名${englishFields.length}个，中文名${chineseFields.length}个，类型${fieldTypes.length}个`)\n          }\n\n          // 构建字段数据\n          const fields = []\n          for (let i = 0; i < englishFields.length; i++) {\n            const field = {\n              id: Date.now() + i,\n              chineseName: chineseFields[i],\n              englishName: englishFields[i],\n              type: fieldTypes[i],\n              controlType: inferControlType(fieldTypes[i]),\n              required: true, // AI生成的字段默认必填项选中\n              searchable: i > 0 && i < 3, // 前几个字段设为可搜索\n              visible: true,\n              existsCheck: false\n            }\n            fields.push(field)\n          }\n\n          // 完整替换表结构\n          currentTableDesign.value.chineseName = chineseName\n          currentTableDesign.value.englishName = englishName\n          currentTableDesign.value.fields = fields\n\n          // 确保functions对象存在\n          if (!currentTableDesign.value.functions) {\n            currentTableDesign.value.functions = {}\n          }\n\n          // 默认选中后台功能\n          currentTableDesign.value.functions.backendAdd = true\n          currentTableDesign.value.functions.backendEdit = true\n          currentTableDesign.value.functions.backendDelete = true\n          currentTableDesign.value.functions.backendDetail = true\n          currentTableDesign.value.functions.backendList = false\n\n          console.log('表单创建成功:', {\n            chineseName: chineseName,\n            englishName: englishName,\n            fieldsCount: fields.length\n          })\n\n        } catch (error) {\n          console.error('解析AI内容失败:', error)\n          ElMessage.error('解析AI生成的内容失败：' + error.message)\n        }\n      }\n\n      // 根据字段类型推断控件类型\n      const inferControlType = (fieldType) => {\n        const type = fieldType.toLowerCase()\n\n        if (type.includes('int') || type.includes('bigint')) {\n          return '文本框'\n        } else if (type.includes('decimal') || type.includes('float') || type.includes('double')) {\n          return '文本框'\n        } else if (type.includes('text') || type.includes('longtext')) {\n          return '多行文本'\n        } else if (type.includes('datetime') || type.includes('timestamp')) {\n          return '日期时间'\n        } else if (type.includes('date')) {\n          return '日期选择'\n        } else if (type.includes('varchar') && type.includes('200')) {\n          return '多行文本'\n        } else if (type.includes('varchar')) {\n          return '文本框'\n        } else {\n          return '文本框'\n        }\n      }\n\n      // 模板选择相关函数\n\n      // 显示后台模板选择弹窗\n      const showBackendTemplateSelector = async () => {\n        showBackendTemplateDialog.value = true\n        await loadBackendTemplates()\n      }\n\n      // 显示前台模板选择弹窗\n      const showFrontendTemplateSelector = async () => {\n        showFrontendTemplateDialog.value = true\n        await loadFrontendTemplates()\n      }\n\n      // 加载后台模板\n      const loadBackendTemplates = async () => {\n        try {\n          templatesLoading.value = true\n          const response = await fetch(base+'/small/backend-templates')\n          const result = await response.json()\n          if (result.code === 200) {\n            backendTemplates.value = result.resdata || []\n          } else {\n            ElMessage.error('加载后台模板失败')\n          }\n        } catch (error) {\n          console.error('加载后台模板失败:', error)\n          ElMessage.error('加载后台模板失败')\n        } finally {\n          templatesLoading.value = false\n        }\n      }\n\n      // 加载前台模板\n      const loadFrontendTemplates = async () => {\n        try {\n          templatesLoading.value = true\n          const response = await fetch(base+'/small/frontend-templates')\n          const result = await response.json()\n          if (result.code === 200) {\n            frontendTemplates.value = result.resdata || []\n          } else {\n            ElMessage.error('加载前台模板失败')\n          }\n        } catch (error) {\n          console.error('加载前台模板失败:', error)\n          ElMessage.error('加载前台模板失败')\n        } finally {\n          templatesLoading.value = false\n        }\n      }\n\n      // 选择后台模板\n      const selectBackendTemplate = (template) => {\n        // 设置到项目表单的后台模板字段 - 保存模板ID\n        projectForm.backendTemplate = template.sid\n        showBackendTemplateDialog.value = false\n        ElMessage.success(`已选择后台模板: ${template.sname} (ID: ${template.sid})`)\n      }\n\n      // 选择前台模板\n      const selectFrontendTemplate = (template) => {\n        // 设置到项目表单的前台模板字段 - 保存模板ID\n        projectForm.frontendTemplate = template.sid\n        showFrontendTemplateDialog.value = false\n        ElMessage.success(`已选择前台模板: ${template.sname} (ID: ${template.sid})`)\n      }\n\n      // 查看模板详情\n      const viewTemplateDetail = (template) => {\n        currentTemplateDetail.value = template\n        showTemplateDetailDialog.value = true\n      }\n\n      // 计算属性：是否可以生成项目\n      const canGenerate = computed(() => {\n        return projectForm.name &&\n               projectForm.databaseName &&\n               projectForm.projectCode &&\n               projectTables.value.length > 0 &&\n               !generationInProgress.value\n      })\n\n      // 生成项目\n      const generateProject = async () => {\n        if (!canGenerate.value) {\n          ElMessage.warning('请完善项目配置信息')\n          return\n        }\n\n        try {\n          generationInProgress.value = true\n          generationResult.value = null\n\n          // 构建项目数据\n          const projectData = {\n            projectNumber: projectForm.projectCode,\n            databaseName: projectForm.databaseName,\n            projectName: projectForm.name,\n            packageName: projectForm.packageName || 'com',\n            databaseType: projectForm.databaseType || 'mysql',\n            backendTemplate: projectForm.backendTemplate,\n            frontendTemplate: projectForm.frontendTemplate,\n            tables: projectTables.value.map(table => ({\n              id: table.tid,\n              chineseName: table.tword,\n              englishName: table.tname,\n              functions: table.tgn ? JSON.parse(table.tgn) : {},\n              fields: table.fields || []\n            }))\n          }\n\n          console.log('开始生成项目:', projectData)\n\n          // 调用Java后端API生成项目\n          const response = await fetch(base + '/projects/generate', {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(projectData)\n          })\n\n          const result = await response.json()\n\n          if (result.code === 200) {\n            // 适配后端返回的数据结构\n            const filesData = result.resdata.files && result.resdata.files.data ? result.resdata.files.data : {}\n            const compressionData = result.resdata.compression || null\n\n            generationResult.value = {\n              status: 'success',\n              message: '项目生成成功！',\n              files: filesData,\n              compression: compressionData\n            }\n\n            // 生成项目成功后，自动生成SQL脚本和数据脚本\n            await generateSqlScript()\n            await generateDataScript()\n\n            ElMessage.success('项目生成成功！')\n          } else {\n            throw new Error(result.msg || '项目生成失败')\n          }\n\n        } catch (error) {\n          console.error('项目生成失败:', error)\n          generationResult.value = {\n            status: 'error',\n            message: '项目生成失败：' + error.message,\n            files: null,\n            compression: null\n          }\n          ElMessage.error('项目生成失败：' + error.message)\n        } finally {\n          generationInProgress.value = false\n        }\n      }\n\n      // 生成SQL脚本\n      const generateSqlScript = async () => {\n        if (!currentProjectInfo.value || !projectTables.value || projectTables.value.length === 0) {\n          ElMessage.warning('请先配置项目和数据表')\n          return\n        }\n\n        try {\n          sqlGenerationInProgress.value = true\n\n          // 根据数据库类型生成对应的SQL脚本\n          const databaseType = projectForm.databaseType || 'mysql'\n          let script = ''\n\n          if (databaseType === 'mysql') {\n            script = generateMySqlScript()\n          } else if (databaseType === 'sqlserver') {\n            script = generateSqlServerScript()\n          }\n\n          sqlContent.value = script\n          ElMessage.success('SQL脚本生成成功！')\n\n        } catch (error) {\n          console.error('生成SQL脚本失败:', error)\n          ElMessage.error('生成SQL脚本失败：' + error.message)\n        } finally {\n          sqlGenerationInProgress.value = false\n        }\n      }\n\n      // 生成MySQL脚本\n      const generateMySqlScript = () => {\n        let script = `-- ${projectForm.name || '项目'} 数据库脚本 (MySQL)\\n`\n        script += `-- 数据库名称: ${projectForm.databaseName}\\n`\n        script += `-- 生成时间: ${new Date().toLocaleString()}\\n\\n`\n\n        // 创建数据库\n        script += `CREATE DATABASE IF NOT EXISTS \\`${projectForm.databaseName}\\` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\\n`\n        script += `USE \\`${projectForm.databaseName}\\`;\\n\\n`\n\n        // 为每个表生成建表语句\n        projectTables.value.forEach(table => {\n          script += generateMySqlTableScript(table)\n          script += '\\n'\n        })\n\n        return script\n      }\n\n      // 生成SQL Server脚本\n      const generateSqlServerScript = () => {\n        let script = `-- ${projectForm.name || '项目'} 数据库脚本 (SQL Server)\\n`\n        script += `-- 数据库名称: ${projectForm.databaseName}\\n`\n        script += `-- 生成时间: ${new Date().toLocaleString()}\\n\\n`\n\n        // 创建数据库\n        script += `IF NOT EXISTS (SELECT * FROM sys.databases WHERE name = '${projectForm.databaseName}')\\n`\n        script += `CREATE DATABASE [${projectForm.databaseName}];\\n`\n        script += `GO\\n\\n`\n        script += `USE [${projectForm.databaseName}];\\n`\n        script += `GO\\n\\n`\n\n        // 为每个表生成建表语句\n        projectTables.value.forEach(table => {\n          script += generateSqlServerTableScript(table)\n          script += '\\n'\n        })\n\n        return script\n      }\n\n      // 生成MySQL表脚本\n      const generateMySqlTableScript = (table) => {\n        let script = `-- 表: ${table.tword || table.tname}\\n`\n        script += `DROP TABLE IF EXISTS \\`${table.tname}\\`;\\n`\n        script += `CREATE TABLE \\`${table.tname}\\` (\\n`\n\n        const fields = table.fields || []\n        const fieldScripts = fields.map((field, index) => {\n          let fieldScript = `  \\`${field.englishName}\\` ${convertToMySqlType(field.type)}`\n\n          // 主键处理 - 修复第一个字段的AUTO_INCREMENT问题\n          if (field.englishName.toLowerCase() === 'id' || index === 0) {\n            // 只有int类型的字段才能使用AUTO_INCREMENT\n            if (field.type && field.type.toLowerCase() === 'int') {\n              fieldScript += ' AUTO_INCREMENT NOT NULL'\n            } else {\n              fieldScript += ' NOT NULL'\n            }\n          } else if (field.required) {\n            fieldScript += ' NOT NULL'\n          }\n\n          // 注释\n          if (field.chineseName) {\n            fieldScript += ` COMMENT '${field.chineseName}'`\n          }\n\n          return fieldScript\n        })\n\n        script += fieldScripts.join(',\\n')\n\n        // 主键约束\n        if (fields.length > 0) {\n          const primaryKey = fields[0].englishName\n          script += `,\\n  PRIMARY KEY (\\`${primaryKey}\\`)`\n        }\n\n        script += `\\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='${table.tword || table.tname}';\\n\\n`\n\n        return script\n      }\n\n      // 生成SQL Server表脚本\n      const generateSqlServerTableScript = (table) => {\n        let script = `-- 表: ${table.tword || table.tname}\\n`\n        script += `IF OBJECT_ID('[${table.tname}]', 'U') IS NOT NULL DROP TABLE [${table.tname}];\\n`\n        script += `CREATE TABLE [${table.tname}] (\\n`\n\n        const fields = table.fields || []\n        const fieldScripts = fields.map((field, index) => {\n          let fieldScript = `  [${field.englishName}] ${convertToSqlServerType(field.type)}`\n\n          // 主键处理\n          if (field.englishName.toLowerCase() === 'id' || index === 0) {\n            fieldScript += ' IDENTITY(1,1)'\n          }\n\n          // 非空约束\n          if (field.required) {\n            fieldScript += ' NOT NULL'\n          }\n\n          return fieldScript\n        })\n\n        script += fieldScripts.join(',\\n')\n\n        // 主键约束\n        if (fields.length > 0) {\n          const primaryKey = fields[0].englishName\n          script += `,\\n  CONSTRAINT [PK_${table.tname}] PRIMARY KEY ([${primaryKey}])`\n        }\n\n        script += `\\n);\\n`\n\n        // 添加表注释\n        if (table.tword) {\n          script += `EXEC sp_addextendedproperty 'MS_Description', '${table.tword}', 'SCHEMA', 'dbo', 'TABLE', '${table.tname}';\\n`\n        }\n\n        // 添加字段注释\n        fields.forEach(field => {\n          if (field.chineseName) {\n            script += `EXEC sp_addextendedproperty 'MS_Description', '${field.chineseName}', 'SCHEMA', 'dbo', 'TABLE', '${table.tname}', 'COLUMN', '${field.englishName}';\\n`\n          }\n        })\n\n        script += `GO\\n\\n`\n\n        return script\n      }\n\n      // 转换为MySQL数据类型\n      const convertToMySqlType = (type) => {\n        if (!type) return 'VARCHAR(100)'\n\n        const lowerType = type.toLowerCase()\n        if (lowerType === 'int') return 'INT'\n        if (lowerType.includes('varchar')) return type.toUpperCase()\n        if (lowerType === 'text') return 'TEXT'\n        if (lowerType === 'datetime') return 'DATETIME'\n        if (lowerType.includes('decimal')) return type.toUpperCase()\n\n        return type.toUpperCase()\n      }\n\n      // 转换为SQL Server数据类型\n      const convertToSqlServerType = (type) => {\n        if (!type) return 'NVARCHAR(100)'\n\n        const lowerType = type.toLowerCase()\n        if (lowerType === 'int') return 'INT'\n        if (lowerType.includes('varchar')) {\n          // 将varchar转换为nvarchar\n          return type.replace(/varchar/i, 'NVARCHAR')\n        }\n        if (lowerType === 'text') return 'NTEXT'\n        if (lowerType === 'datetime') return 'DATETIME'\n        if (lowerType.includes('decimal')) return type.toUpperCase()\n\n        return type.toUpperCase()\n      }\n\n      // 导出SQL脚本\n      const exportSqlScript = () => {\n        if (!sqlContent.value || sqlContent.value.trim() === '') {\n          ElMessage.warning('请先生成SQL脚本')\n          return\n        }\n\n        try {\n          const blob = new Blob([sqlContent.value], { type: 'text/plain;charset=utf-8' })\n          const url = window.URL.createObjectURL(blob)\n          const link = document.createElement('a')\n          link.href = url\n\n          const databaseType = projectForm.databaseType || 'mysql'\n          const fileName = `${projectForm.databaseName || 'database'}_${databaseType}.sql`\n          link.download = fileName\n\n          document.body.appendChild(link)\n          link.click()\n          document.body.removeChild(link)\n          window.URL.revokeObjectURL(url)\n\n          ElMessage.success('SQL脚本导出成功！')\n        } catch (error) {\n          console.error('导出SQL脚本失败:', error)\n          ElMessage.error('导出SQL脚本失败：' + error.message)\n        }\n      }\n\n      // 复制SQL脚本到剪切板\n      const copySqlScript = async () => {\n        if (!sqlContent.value || sqlContent.value.trim() === '') {\n          ElMessage.warning('请先生成SQL脚本')\n          return\n        }\n\n        try {\n          await navigator.clipboard.writeText(sqlContent.value)\n          ElMessage.success('SQL脚本已复制到剪切板！')\n        } catch (error) {\n          console.error('复制到剪切板失败:', error)\n          // 降级方案：使用传统的复制方法\n          try {\n            const textArea = document.createElement('textarea')\n            textArea.value = sqlContent.value\n            document.body.appendChild(textArea)\n            textArea.select()\n            document.execCommand('copy')\n            document.body.removeChild(textArea)\n            ElMessage.success('SQL脚本已复制到剪切板！')\n          } catch (fallbackError) {\n            console.error('降级复制方法也失败:', fallbackError)\n            ElMessage.error('复制到剪切板失败，请手动复制')\n          }\n        }\n      }\n\n      // 生成数据脚本\n      const generateDataScript = async () => {\n        if (!currentProjectInfo.value || !projectTables.value || projectTables.value.length === 0) {\n          ElMessage.warning('请先配置项目和数据表')\n          return\n        }\n\n        try {\n          dataGenerationInProgress.value = true\n\n          // 筛选出需要生成数据的表（生成数据条数大于0）\n          const tablesWithData = projectTables.value.filter(table => {\n            const dataCount = parseInt(table.by1 || '0')\n            return dataCount > 0\n          })\n\n          if (tablesWithData.length === 0) {\n            ElMessage.warning('没有设置生成数据条数的表，请先在表设计中设置生成数据条数')\n            return\n          }\n\n          let script = ''\n\n          // 为每个需要生成数据的表生成建表语句和插入数据\n          for (const table of tablesWithData) {\n            script += generateTableWithDataScript(table)\n            script += '\\n'\n          }\n\n          // 添加生成要求说明\n          script += `项目名称是：${projectForm.name || '智慧社区网格化管理系统'}\\n`\n          script += `按要求生成的条数，生成上面所有的数据，数据内容要多一些，数据模拟真实的数据\\n`\n          script += `如果有密码，密码为123456。\\n`\n          script += `时间字段为当前时间\\n`\n          script += `生成的数据为中文，只生成insert into数据，不要注释说明\\n`\n\n          dataContent.value = script\n          ElMessage.success('数据脚本生成成功！')\n\n        } catch (error) {\n          console.error('生成数据脚本失败:', error)\n          ElMessage.error('生成数据脚本失败：' + error.message)\n        } finally {\n          dataGenerationInProgress.value = false\n        }\n      }\n\n      // 生成单个表的建表语句和数据\n      const generateTableWithDataScript = (table) => {\n        const fields = table.fields || []\n        const dataCount = parseInt(table.by1 || '0')\n\n        let script = `create table if NOT EXISTS ${table.tname} \\n(\\n`\n\n        // 生成字段定义\n        const fieldScripts = fields.map((field, index) => {\n          let fieldScript = `${field.englishName}   ${convertToMySqlType(field.type)}`\n\n          // 主键处理 - 修复第一个字段的AUTO_INCREMENT问题\n          if (field.englishName.toLowerCase() === 'id' || index === 0) {\n            // 只有int类型的字段才能使用AUTO_INCREMENT\n            if (field.type && field.type.toLowerCase() === 'int') {\n              fieldScript += ' auto_increment  primary key'\n            } else {\n              fieldScript += ' not null    primary key'\n            }\n          } else if (field.englishName.toLowerCase().includes('account') ||\n                     field.englishName.toLowerCase().includes('username')) {\n            fieldScript += ' not null    primary key'\n          } else if (field.required) {\n            fieldScript += ' not null   '\n          } else {\n            fieldScript += '  null   '\n          }\n\n          // 注释\n          if (field.chineseName) {\n            fieldScript += ` comment '${field.chineseName}'`\n          }\n\n          return fieldScript\n        })\n\n        script += fieldScripts.join(' ,\\n') + ' \\n'\n        script += `) ENGINE=MyISAM AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;\\n\\n`\n\n        // 添加生成数据条数说明\n        if (dataCount > 0) {\n          script += `生成${dataCount}条insert into数据\\n\\n`\n        }\n\n        return script\n      }\n\n      // 复制数据脚本到剪切板\n      const copyDataScript = async () => {\n        if (!dataContent.value || dataContent.value.trim() === '') {\n          ElMessage.warning('请先生成数据脚本')\n          return\n        }\n\n        try {\n          await navigator.clipboard.writeText(dataContent.value)\n          ElMessage.success('数据脚本已复制到剪切板！')\n        } catch (error) {\n          console.error('复制到剪切板失败:', error)\n          // 降级方案：使用传统的复制方法\n          try {\n            const textArea = document.createElement('textarea')\n            textArea.value = dataContent.value\n            document.body.appendChild(textArea)\n            textArea.select()\n            document.execCommand('copy')\n            document.body.removeChild(textArea)\n            ElMessage.success('数据脚本已复制到剪切板！')\n          } catch (fallbackError) {\n            console.error('降级复制方法也失败:', fallbackError)\n            ElMessage.error('复制到剪切板失败，请手动复制')\n          }\n        }\n      }\n\n      // 下载项目文件\n      const downloadProject = (filePath) => {\n        if (filePath) {\n          // 从filePath中提取文件名\n          const fileName = filePath.split('/').pop()\n          // 使用新的下载接口\n          const downloadUrl = `${base}/projects/download?fileName=${encodeURIComponent(fileName)}`\n          window.open(downloadUrl, '_blank')\n        } else {\n          ElMessage.error('下载链接无效')\n        }\n      }\n\n      // 获取模板图片URL\n      const getTemplateImageUrl = (memo4) => {\n        if (!memo4) return ''\n\n        // 从HTML中提取图片URL\n        const imgMatch = memo4.match(/<img[^>]+src=\"([^\"]+)\"/)\n        if (imgMatch && imgMatch[1]) {\n          return imgMatch[1]\n        }\n\n        return ''\n      }\n\n      // 处理图片加载错误\n      const handleImageError = (event) => {\n        event.target.style.display = 'none'\n        const parent = event.target.parentElement\n        if (parent) {\n          parent.innerHTML = '<div class=\"no-image\"><span>图片加载失败</span></div>'\n        }\n      }\n\n      // 获取表格字段数量\n      const getTableFieldCount = (table) => {\n        // 如果有字段数据，返回字段数量\n        if (table.fields && Array.isArray(table.fields)) {\n          return table.fields.length\n        }\n        // 否则返回0\n        return 0\n      }\n\n      // 获取表格功能列表\n      const getTableFunctions = (table) => {\n        if (!table.tgn) return []\n\n        try {\n          const functions = JSON.parse(table.tgn)\n          const activeFunctions = []\n\n          // 检查各种功能是否启用\n          if (functions.backendAdd) activeFunctions.push('后台添加')\n          if (functions.backendEdit) activeFunctions.push('后台修改')\n          if (functions.backendDelete) activeFunctions.push('后台删除')\n          if (functions.backendDetail) activeFunctions.push('后台详情')\n          if (functions.backendList) activeFunctions.push('后台列表')\n          if (functions.frontendAdd) activeFunctions.push('前台添加')\n          if (functions.frontendEdit) activeFunctions.push('前台修改')\n          if (functions.frontendDelete) activeFunctions.push('前台删除')\n          if (functions.miniAdd) activeFunctions.push('小程序添加')\n\n          return activeFunctions.slice(0, 3) // 只显示前3个功能\n        } catch (error) {\n          console.error('解析表功能配置失败:', error)\n          return []\n        }\n      }\n\n      // 打开表设计弹窗\n      const openTableDesignModal = (table = null) => {\n        if (table) {\n          // 编辑现有表\n          currentTableDesign.value = {\n            id: table.tid,\n            chineseName: table.tword || '',\n            englishName: table.tname || '',\n            menuOrder: parseInt(table.by2 || '1'),\n            generateData: '0',\n            generateDataCount: parseInt(table.by1 || '0'),\n            functions: table.tgn ? JSON.parse(table.tgn) : {\n              backendAdd: true, backendEdit: true, backendDelete: true,\n              backendDetail: true, backendList: false, batchImport: false,\n              batchExport: false, backendLogin: false, backendRegister: false,\n              backendProfile: false, backendPassword: false,\n              frontendAdd: false, frontendEdit: false, frontendDelete: false,\n              frontendDetail: false, frontendList: false, frontendLogin: false,\n              miniAdd: false, miniEdit: false, miniDelete: false,\n              miniDetail: false, miniList: false\n            },\n            fields: table.fields || [\n              {\n                id: 1,\n                chineseName: '主键ID',\n                englishName: 'id',\n                type: 'int',\n                controlType: '文本框',\n                required: true,\n                searchable: false,\n                visible: true,\n                existsCheck: false\n              }\n            ]\n          }\n        } else {\n          // 创建新表\n          resetTableDesign()\n        }\n        showTableDesignModal.value = true\n      }\n\n      // 重置表设计数据\n      const resetTableDesign = () => {\n        currentTableDesign.value = {\n          id: null,\n          chineseName: '',\n          englishName: '',\n          menuOrder: 1,\n          generateData: '0',\n          generateDataCount: 0,\n          functions: {\n            backendAdd: true, backendEdit: true, backendDelete: true,\n            backendDetail: true, backendList: false, batchImport: false,\n            batchExport: false, backendLogin: false, backendRegister: false,\n            backendProfile: false, backendPassword: false,\n            frontendAdd: false, frontendEdit: false, frontendDelete: false,\n            frontendDetail: false, frontendList: false, frontendLogin: false,\n            miniAdd: false, miniEdit: false, miniDelete: false,\n            miniDetail: false, miniList: false\n          },\n          fields: [\n            {\n              id: 1,\n              chineseName: '主键ID',\n              englishName: 'id',\n              type: 'int',\n              controlType: '文本框',\n              required: true,\n              searchable: false,\n              visible: true,\n              existsCheck: false\n            }\n          ]\n        }\n      }\n\n      // 关闭表设计弹窗\n      const closeTableDesignModal = () => {\n        showTableDesignModal.value = false\n        currentTableTab.value = 'table-settings'\n      }\n\n      // 表设计弹窗Tab切换功能\n      const switchTableTab = (tabId) => {\n        currentTableTab.value = tabId\n      }\n\n      // 添加字段到设计中\n      const addNewFieldToDesign = () => {\n        const newField = {\n          id: Date.now(),\n          chineseName: '',\n          englishName: '',\n          type: 'varchar(100)',\n          controlType: '文本框',\n          required: true, // 默认必填项选中\n          searchable: false,\n          visible: true,\n          existsCheck: false,\n          relatedTable: '', // 关联表\n          customOptions: '' // 自定义选项\n        }\n        currentTableDesign.value.fields.push(newField)\n      }\n\n      // 清空所有字段\n      const clearAllFields = async () => {\n        try {\n          await ElMessageBox.confirm(\n            '确定要清空所有字段吗？此操作不可撤销。',\n            '清空字段',\n            {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              type: 'warning',\n            }\n          )\n\n          currentTableDesign.value.fields = []\n          ElMessage.success('字段清空成功')\n        } catch (error) {\n          if (error !== 'cancel') {\n            console.error('清空字段失败:', error)\n          }\n        }\n      }\n\n      // 删除设计中的字段\n      const deleteFieldFromDesign = async (index) => {\n        try {\n          await ElMessageBox.confirm(\n            '确定要删除这个字段吗？此操作不可撤销。',\n            '删除字段',\n            {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              type: 'warning',\n            }\n          )\n\n          currentTableDesign.value.fields.splice(index, 1)\n          ElMessage.success('字段删除成功')\n        } catch (error) {\n          if (error !== 'cancel') {\n            console.error('删除字段失败:', error)\n          }\n        }\n      }\n\n      // 上移字段\n      const moveFieldUp = (index) => {\n        if (index > 0) {\n          const field = currentTableDesign.value.fields.splice(index, 1)[0]\n          currentTableDesign.value.fields.splice(index - 1, 0, field)\n        }\n      }\n\n      // 下移字段\n      const moveFieldDown = (index) => {\n        if (index < currentTableDesign.value.fields.length - 1) {\n          const field = currentTableDesign.value.fields.splice(index, 1)[0]\n          currentTableDesign.value.fields.splice(index + 1, 0, field)\n        }\n      }\n\n      // 编辑字段设置\n      const editFieldSettings = (field, index) => {\n        currentFieldSettings.value = { ...field }\n        currentFieldIndex.value = index\n        showInSearchList.value = field.searchable || false\n        showFieldSettingsDialog.value = true\n      }\n\n      // 关闭字段设置弹窗\n      const closeFieldSettingsDialog = () => {\n        showFieldSettingsDialog.value = false\n        currentFieldSettings.value = null\n        currentFieldIndex.value = -1\n      }\n\n      // 保存字段设置\n      const saveFieldSettings = () => {\n        if (currentFieldIndex.value >= 0 && currentFieldSettings.value) {\n          // 更新字段数据\n          const field = currentTableDesign.value.fields[currentFieldIndex.value]\n          field.relatedTable = currentFieldSettings.value.relatedTable\n          field.customOptions = currentFieldSettings.value.customOptions\n          field.searchable = showInSearchList.value\n\n          ElMessage.success('字段设置保存成功')\n          closeFieldSettingsDialog()\n        }\n      }\n\n      // 显示关联表选择器\n      const showRelatedTableSelector = async () => {\n        if (!currentProjectInfo.value || !currentProjectInfo.value.pid) {\n          ElMessage.warning('请先保存项目信息')\n          return\n        }\n\n        try {\n          // 加载项目的所有表\n          await loadAvailableRelatedTables()\n          showRelatedTableDialog.value = true\n        } catch (error) {\n          console.error('加载关联表失败:', error)\n          ElMessage.error('加载关联表失败')\n        }\n      }\n\n      // 加载可用的关联表\n      const loadAvailableRelatedTables = async () => {\n        try {\n          const url = base + \"/tables/list?currentPage=1&pageSize=100\"\n          const res = await request.post(url, { pid: currentProjectInfo.value.pid })\n\n          if (res.code === 200 && res.resdata) {\n            availableRelatedTables.value = res.resdata.map(table => {\n              // 获取表的第二个字段作为显示名称，如果第二个字段是密码则使用第三个字段\n              let displayName = table.tword || table.tname\n              if (table.fields && table.fields.length > 1) {\n                const secondField = table.fields[1]\n                if (secondField && secondField.chineseName && !secondField.chineseName.includes('密码')) {\n                  displayName = secondField.chineseName\n                } else if (table.fields.length > 2) {\n                  const thirdField = table.fields[2]\n                  if (thirdField && thirdField.chineseName) {\n                    displayName = thirdField.chineseName\n                  }\n                }\n              }\n\n              return {\n                primaryKey: table.fields && table.fields.length > 0 ? table.fields[0].englishName : 'id',\n                displayName: displayName,\n                tableName: table.tname,\n                linkValue: '1',\n                selected: false\n              }\n            })\n          }\n        } catch (error) {\n          console.error('加载关联表失败:', error)\n          throw error\n        }\n      }\n\n      // 关闭关联表选择弹窗\n      const closeRelatedTableDialog = () => {\n        showRelatedTableDialog.value = false\n        availableRelatedTables.value = []\n      }\n\n      // 确认关联表选择\n      const confirmRelatedTableSelection = () => {\n        const selectedTables = availableRelatedTables.value.filter(table => table.selected)\n        if (selectedTables.length === 0) {\n          ElMessage.warning('请至少选择一个关联表')\n          return\n        }\n\n        // 构建关联表字符串，格式：doro,dbid,dormitory,1\n        const relatedTableStr = selectedTables.map(table =>\n          `${table.primaryKey},${table.displayName},${table.tableName},${table.linkValue}`\n        ).join(';')\n\n        if (currentFieldSettings.value) {\n          currentFieldSettings.value.relatedTable = relatedTableStr\n        }\n\n        closeRelatedTableDialog()\n        ElMessage.success('关联表设置成功')\n      }\n\n      // 保存表设计\n      const saveTableDesign = async () => {\n        // 验证必填字段\n        if (!currentTableDesign.value.chineseName.trim()) {\n          ElMessage.warning('请输入表的中文名称')\n          return\n        }\n\n        if (!currentTableDesign.value.englishName.trim()) {\n          ElMessage.warning('请输入表的英文名称')\n          return\n        }\n\n        // 验证字段\n        for (let field of currentTableDesign.value.fields) {\n          if (!field.chineseName.trim() || !field.englishName.trim()) {\n            ElMessage.warning('请完善所有字段的中文名称和英文名称')\n            return\n          }\n        }\n\n        // 确保有项目ID\n        if (!currentProjectInfo.value || !currentProjectInfo.value.pid) {\n          ElMessage.error('项目信息缺失，请重新配置项目')\n          return\n        }\n\n        try {\n          // 准备表数据\n          const tableData = {\n            pid: currentProjectInfo.value.pid,\n            tword: currentTableDesign.value.chineseName,\n            tname: currentTableDesign.value.englishName,\n            tgn: JSON.stringify(currentTableDesign.value.functions),\n            tlist: '',\n            vlist: '',\n            by1: (currentTableDesign.value.generateDataCount || 0).toString(),\n            by2: (currentTableDesign.value.menuOrder || 1).toString()\n          }\n\n          // 如果是编辑模式，添加tid\n          if (currentTableDesign.value.id) {\n            tableData.tid = currentTableDesign.value.id\n          }\n\n          // 保存表信息\n          const isUpdate = currentTableDesign.value.id\n          const url = base + (isUpdate ? \"/tables/update\" : \"/tables/add\")\n          const res = await request.post(url, tableData)\n\n          if (res.code === 200) {\n            let tableId = currentTableDesign.value.id\n            if (!isUpdate && res.resdata) {\n              // 后端返回的是新创建的表ID\n              tableId = res.resdata\n              currentTableDesign.value.id = tableId\n            }\n\n            // 如果有字段，保存字段信息\n            if (currentTableDesign.value.fields.length > 0) {\n              // 先删除原有字段（如果是更新模式）\n              if (isUpdate) {\n                try {\n                  await request.post(base + \"/mores/deleteByTid\", { tid: tableId })\n                } catch (error) {\n                  console.warn('删除原有字段失败:', error)\n                }\n              }\n\n              // 保存新字段\n              for (let field of currentTableDesign.value.fields) {\n                const fieldData = {\n                  tid: tableId,\n                  moname: field.englishName,\n                  mozname: field.chineseName,\n                  motype: field.type,\n                  moflag: field.controlType,\n                  molong: '',\n                  moyz: field.required ? '1' : '0',\n                  mobt: field.searchable ? '1' : '0',\n                  by1: field.visible ? '1' : '0',\n                  by2: field.existsCheck ? '1' : '0',\n                  by3: field.relatedTable || '',\n                  by4: field.customOptions || '',\n                  by5: '',\n                  by6: ''\n                }\n\n                try {\n                  await request.post(base + \"/mores/add\", fieldData)\n                } catch (error) {\n                  console.error('保存字段失败:', field, error)\n                }\n              }\n            }\n\n            ElMessage.success('表设计保存成功')\n            closeTableDesignModal()\n            // 重新加载项目表单列表\n            if (currentProjectInfo.value.pid) {\n              loadProjectTables(currentProjectInfo.value.pid)\n            }\n          } else {\n            ElMessage.error(res.msg || '表保存失败')\n          }\n        } catch (error) {\n          console.error('保存表设计失败:', error)\n          ElMessage.error('保存表设计失败，请检查网络连接')\n        }\n      }\n\n      // 删除表\n      const deleteTable = async (table) => {\n        try {\n          await ElMessageBox.confirm(\n            `确定要删除表 \"${table.tword || table.tname}\" 吗？`,\n            '确认删除',\n            {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              type: 'warning',\n            }\n          )\n\n          const url = base + \"/tables/del?id=\" + table.tid\n          const res = await request.post(url, {})\n\n          if (res.code === 200) {\n            ElMessage.success('删除成功')\n            // 重新加载表单列表\n            if (currentProjectInfo.value && currentProjectInfo.value.pid) {\n              loadProjectTables(currentProjectInfo.value.pid)\n            }\n          } else {\n            ElMessage.error(res.msg || '删除失败')\n          }\n        } catch (error) {\n          if (error !== 'cancel') {\n            console.error('删除表失败:', error)\n            ElMessage.error('删除失败，请检查网络连接')\n          }\n        }\n      }\n\n      // 下一步操作\n      const nextStep = async () => {\n        if (activeTab.value === 'project') {\n          // 从项目配置到表单设计\n          if (!selectedProject.value) {\n            ElMessage.warning('请先选择项目类型')\n            return\n          }\n          if (!projectForm.name) {\n            ElMessage.warning('请输入项目中文名称')\n            return\n          }\n\n          // 验证必填字段\n          if (projectForm.databaseMode === 'new') {\n            if (!projectForm.projectCode) {\n              ElMessage.warning('请输入项目编号')\n              return\n            }\n            if (!projectForm.databaseName) {\n              ElMessage.warning('请输入数据库名称')\n              return\n            }\n          } else if (projectForm.databaseMode === 'existing') {\n            if (!projectForm.selectedDatabase) {\n              ElMessage.warning('请选择已有数据库')\n              return\n            }\n          }\n\n          // 设置当前项目信息\n          currentProjectInfo.value = {\n            name: projectForm.name,\n            projectCode: projectForm.projectCode,\n            databaseName: projectForm.databaseName,\n            pid: projectForm.selectedDatabase || null\n          }\n\n          // 保存或更新项目到数据库\n          const result = await saveOrUpdateProject()\n          if (!result.success) return\n\n          // 更新项目ID\n          if (result.projectId) {\n            currentProjectInfo.value.pid = result.projectId\n          }\n\n          // 如果是已有数据库模式，加载项目表单\n          if (projectForm.databaseMode === 'existing' && projectForm.selectedDatabase) {\n            loadProjectTables(projectForm.selectedDatabase)\n          } else if (currentProjectInfo.value.pid) {\n            // 新建项目也加载表单（可能为空）\n            loadProjectTables(currentProjectInfo.value.pid)\n          }\n\n          activeTab.value = 'form'\n          ElMessage.success('项目配置保存成功，请继续设计表单')\n        } else if (activeTab.value === 'form') {\n          // 从表单设计到项目生成\n          if (!projectTables.value || projectTables.value.length === 0) {\n            ElMessage.warning('请先设计数据表，不能为空')\n            return\n          }\n          activeTab.value = 'generate'\n          ElMessage.success('表单设计完成，请生成项目')\n        }\n      }\n\n      const handleTabClick = (tab) => {\n        console.log('切换到标签页:', tab.props.name)\n      }\n\n      // 组件挂载时检查登录状态\n      onMounted(() => {\n        checkLoginStatus()\n      })\n\n      return {\n        activeTab,\n        logLevel,\n        isLoggedIn,\n        loginDialogVisible,\n        loginLoading,\n        loginFormRef,\n        userInfo,\n        loginForm,\n        loginRules,\n        selectedProject,\n        projectsLoading,\n        availableDatabases,\n        projectForm,\n        currentProjectInfo,\n        projectTables,\n        projectTablesLoading,\n        showTableDesignModal,\n        currentTableTab,\n        currentTableDesign,\n        formFields,\n        tableData,\n        sqlContent,\n        logs,\n        handleTabClick,\n        handleLogin,\n        handleUserCommand,\n        handleLogout,\n        selectProject,\n        getProjectName,\n        openTemplateModal,\n        openFrontTemplateModal,\n        handleDatabaseModeChange,\n        handleProjectSelect,\n        loadProjectTables,\n        openTableDesignModal,\n        closeTableDesignModal,\n        switchTableTab,\n        addNewFieldToDesign,\n        clearAllFields,\n        deleteFieldFromDesign,\n        moveFieldUp,\n        moveFieldDown,\n        saveTableDesign,\n        deleteTable,\n        nextStep,\n        getTableFieldCount,\n        getTableFunctions,\n        backendFunctions,\n        frontendFunctions,\n        miniFunctions,\n        resetFields,\n        showAiGeneratorModal,\n\n        // AI生成器相关\n        showAiGeneratorDialog,\n        aiGeneratorInput,\n        aiGenerationInProgress,\n        hideAiGeneratorModal,\n        confirmAiGeneration,\n        doubaoGenerate,\n        createFormFromAI,\n        inferControlType,\n\n        // 模板选择相关\n        showBackendTemplateDialog,\n        showFrontendTemplateDialog,\n        showTemplateDetailDialog,\n        backendTemplates,\n        frontendTemplates,\n        templatesLoading,\n        currentTemplateDetail,\n        showBackendTemplateSelector,\n        showFrontendTemplateSelector,\n        selectBackendTemplate,\n        selectFrontendTemplate,\n        viewTemplateDetail,\n        getTemplateImageUrl,\n        handleImageError,\n\n        // 项目生成相关\n        generationInProgress,\n        generationResult,\n        canGenerate,\n        generateProject,\n        downloadProject,\n\n        // SQL脚本生成相关\n        sqlGenerationInProgress,\n        generateSqlScript,\n        exportSqlScript,\n        copySqlScript,\n\n        // 数据脚本生成相关\n        dataGenerationInProgress,\n        dataContent,\n        generateDataScript,\n        copyDataScript,\n\n        // 字段设置相关\n        showFieldSettingsDialog,\n        currentFieldSettings,\n        showInSearchList,\n        editFieldSettings,\n        closeFieldSettingsDialog,\n        saveFieldSettings,\n        showRelatedTableSelector,\n\n        // 关联表选择相关\n        showRelatedTableDialog,\n        availableRelatedTables,\n        closeRelatedTableDialog,\n        confirmRelatedTableSelection\n      }\n    }\n  }\n</script>"]}]}