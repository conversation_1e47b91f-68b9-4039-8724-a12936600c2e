{"remainingRequest": "J:\\auto2025\\auto2025-web\\node_modules\\babel-loader\\lib\\index.js!J:\\auto2025\\auto2025-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!J:\\auto2025\\auto2025-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!J:\\auto2025\\auto2025-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!J:\\auto2025\\auto2025-web\\src\\views\\web\\CodeGenerator.vue?vue&type=template&id=c61fc0be&scoped=true", "dependencies": [{"path": "J:\\auto2025\\auto2025-web\\src\\views\\web\\CodeGenerator.vue", "mtime": 1749465006927}, {"path": "J:\\auto2025\\auto2025-web\\babel.config.js", "mtime": 1748614864000}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748675476717}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748675484569}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1748675491877}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748675476717}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1748675485115}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "style", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "$setup", "isLoggedIn", "_hoisted_2", "_createVNode", "_component_el_dropdown", "onCommand", "handleUserCommand", "dropdown", "_withCtx", "_component_el_dropdown_menu", "_component_el_dropdown_item", "command", "_cache", "_createElementVNode", "_hoisted_3", "_component_el_icon", "_component_User", "_toDisplayString", "userInfo", "username", "_component_ArrowDown", "_hoisted_4", "_component_el_tabs", "activeTab", "$event", "type", "onTabClick", "handleTabClick", "_component_el_tab_pane", "label", "name", "_hoisted_5", "_component_Folder", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_component_Setting", "_hoisted_10", "_hoisted_11", "_normalizeClass", "active", "selectedProject", "onClick", "selectProject", "_hoisted_12", "_hoisted_13", "getProjectName", "_hoisted_14", "_component_el_form", "model", "projectForm", "_component_el_row", "gutter", "_component_el_col", "span", "_component_el_form_item", "_component_el_select", "databaseType", "placeholder", "_component_el_option", "value", "databaseMode", "onChange", "handleDatabaseModeChange", "_component_el_input", "_createBlock", "projectCode", "databaseName", "schoolName", "selectedDatabase", "handleProjectSelect", "loading", "projectsLoading", "_Fragment", "_renderList", "availableDatabases", "db", "key", "text", "disabled", "length", "_hoisted_15", "backendTemplate", "readonly", "openTemplateModal", "frontendTemplate", "openFrontTemplateModal", "copyProject", "layer", "charts", "adminId", "admin<PERSON>ame", "adminRole", "adminLoginName", "_hoisted_16", "_component_el_button", "nextStep", "size", "_hoisted_17", "_component_Edit", "_hoisted_18", "_hoisted_19", "currentProjectInfo", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27", "icon", "_ctx", "Plus", "openTableDesignModal", "projectTablesLoading", "_hoisted_28", "_component_Loading", "projectTables", "_hoisted_29", "_component_el_empty", "description", "_hoisted_30", "table", "tid", "_hoisted_32", "tword", "tname", "_hoisted_33", "Edit", "_withModifiers", "Delete", "deleteTable", "_hoisted_34", "getTableFieldCount", "tgn", "_hoisted_35", "getTableFunctions", "func", "_hoisted_36", "_hoisted_37", "_component_Cpu", "_hoisted_38", "_hoisted_39", "_hoisted_40", "_hoisted_41", "_hoisted_42", "_hoisted_43", "_hoisted_44", "_hoisted_45", "_hoisted_46", "_hoisted_47", "_hoisted_48", "_hoisted_49", "_hoisted_50", "_hoisted_51", "_hoisted_52", "_hoisted_53", "generateProject", "generationInProgress", "canGenerate", "_hoisted_54", "_component_el_progress", "percentage", "status", "indeterminate", "generationResult", "_hoisted_55", "_component_Check", "_component_Close", "_hoisted_56", "message", "files", "_hoisted_57", "savedCount", "totalCount", "savedFiles", "_hoisted_58", "_component_el_scrollbar", "_hoisted_59", "file", "_component_Document", "_hoisted_60", "failedFiles", "_hoisted_61", "_hoisted_62", "fileName", "_component_Warning", "_hoisted_63", "_hoisted_64", "error", "compression", "_hoisted_65", "_component_Box", "_hoisted_66", "data", "_hoisted_67", "zipFileName", "fileSize", "downloadProject", "zipFilePath", "_component_Download", "_hoisted_68", "_component_DataBoard", "_hoisted_69", "_hoisted_70", "_hoisted_71", "_hoisted_72", "generateDataScript", "dataGenerationInProgress", "DocumentCopy", "copyDataScript", "_hoisted_73", "dataContent", "rows", "_hoisted_74", "_component_DocumentCopy", "_hoisted_75", "_hoisted_76", "_hoisted_77", "_hoisted_78", "generateSqlScript", "sqlGenerationInProgress", "Download", "exportSqlScript", "copySqlScript", "_hoisted_79", "sqlContent", "_hoisted_80", "_hoisted_81", "_hoisted_82", "_hoisted_83", "_hoisted_84", "Refresh", "logLevel", "_hoisted_85", "logs", "log", "index", "level", "_hoisted_86", "time", "_hoisted_87", "toUpperCase", "_hoisted_88", "_component_el_dialog", "loginDialogVisible", "title", "width", "footer", "_hoisted_89", "handleLogin", "loginLoading", "loginForm", "rules", "loginRules", "ref", "prop", "password", "onKeyup", "_with<PERSON><PERSON><PERSON>", "showTableDesignModal", "currentTableDesign", "id", "closeTableDesignModal", "top", "_hoisted_107", "_component_el_space", "saveTableDesign", "_hoisted_90", "_hoisted_91", "_hoisted_92", "currentTableTab", "switchTableTab", "_hoisted_93", "_hoisted_94", "_hoisted_95", "required", "chineseName", "clearable", "englishName", "_component_el_input_number", "menuOrder", "min", "max", "generateDataCount", "showAiGeneratorModal", "_hoisted_96", "_hoisted_97", "_hoisted_98", "_component_el_checkbox", "functions", "backendAdd", "backendEdit", "backendDelete", "backendDetail", "backendList", "batchImport", "batchExport", "backend<PERSON><PERSON><PERSON>", "backendRegister", "backendProfile", "backendPassword", "_hoisted_99", "_hoisted_100", "frontendAdd", "frontendEdit", "frontendDelete", "frontendDetail", "frontendList", "frontend<PERSON><PERSON><PERSON>", "_hoisted_101", "_hoisted_102", "miniAdd", "miniEdit", "miniDelete", "miniDetail", "miniList", "_hoisted_103", "_hoisted_104", "wrap", "addNewFieldToDesign", "_component_Plus", "resetFields", "_component_Refresh", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_component_Delete", "_component_el_divider", "direction", "_component_el_text", "_$setup$currentTableD", "fields", "_hoisted_105", "_component_el_table", "border", "stripe", "_component_el_table_column", "align", "default", "row", "controlType", "searchable", "visible", "<PERSON><PERSON><PERSON><PERSON>", "fixed", "$index", "_hoisted_106", "moveFieldUp", "circle", "_component_ArrowUp", "moveFieldDown", "editFieldSettings", "deleteFieldFromDesign", "showAiGeneratorDialog", "hideAiGeneratorModal", "_hoisted_110", "aiGenerationInProgress", "confirmAiGeneration", "_hoisted_108", "_hoisted_109", "aiGeneratorInput", "showBackendTemplateDialog", "_hoisted_111", "_hoisted_112", "backendTemplates", "template", "sid", "selectBackendTemplate", "memo4", "src", "getTemplateImageUrl", "alt", "sname", "onError", "args", "handleImageError", "_hoisted_115", "_component_Picture", "_hoisted_116", "_hoisted_117", "_hoisted_118", "_hoisted_119", "viewTemplateDetail", "templatesLoading", "showFrontendTemplateDialog", "_hoisted_120", "_hoisted_121", "frontendTemplates", "selectFrontendTemplate", "_hoisted_124", "_hoisted_125", "_hoisted_126", "_hoisted_127", "_hoisted_128", "showTemplateDetailDialog", "_hoisted_136", "currentTemplateDetail", "_hoisted_129", "_hoisted_130", "_hoisted_131", "_hoisted_132", "_hoisted_133", "_component_el_image", "fit", "_hoisted_134", "_hoisted_135", "_component_el_alert", "closable", "showFieldSettingsDialog", "_hoisted_138", "closeFieldSettingsDialog", "saveFieldSettings", "currentFieldSettings", "_hoisted_137", "relatedTable", "showRelatedTableSelector", "suffix", "_component_View", "showInSearchList", "customOptions", "showRelatedTableDialog", "_hoisted_140", "closeRelatedTableDialog", "confirmRelatedTableSelection", "_hoisted_139", "availableRelatedTables", "linkValue", "selected"], "sources": ["J:\\auto2025\\auto2025-web\\src\\views\\web\\CodeGenerator.vue"], "sourcesContent": ["<template>\n  <div class=\"code-generator-container\">\n    <!-- 用户信息浮动显示 -->\n    <div v-if=\"isLoggedIn\" class=\"user-info-float\">\n      <el-dropdown @command=\"handleUserCommand\">\n        <span class=\"user-info-trigger\">\n          <el-icon>\n            <User />\n          </el-icon>\n          {{ userInfo.username }}\n          <el-icon class=\"el-icon--right\">\n            <ArrowDown />\n          </el-icon>\n        </span>\n        <template #dropdown>\n          <el-dropdown-menu>\n            <el-dropdown-item command=\"logout\">退出登录</el-dropdown-item>\n          </el-dropdown-menu>\n        </template>\n      </el-dropdown>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"main-content\">\n      <el-tabs v-model=\"activeTab\" type=\"card\" class=\"generator-tabs\" @tab-click=\"handleTabClick\">\n        <!-- 设置项目 -->\n        <el-tab-pane label=\"设置项目\" name=\"project\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <Folder />\n              </el-icon>\n              设置项目\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <!-- 页面标题 -->\n            <div class=\"page-header\">\n              <div class=\"header-content\">\n                <h1 class=\"page-title\">\n                  <el-icon class=\"title-icon\">\n                    <Setting />\n                  </el-icon>\n                  代码生成器\n                </h1>\n                <p class=\"page-description\">快速生成高质量的代码，提升开发效率</p>\n              </div>\n            </div>\n\n            <!-- 项目类型选择 -->\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>支持的项目类型</h3>\n                <p>选择您需要的技术栈，快速生成项目模板</p>\n              </div>\n\n              <!-- 功能卡片网格 -->\n              <div class=\"features-grid\">\n                <div class=\"feature-card\" @click=\"selectProject('springboot-thymeleaf')\"\n                  :class=\"{ active: selectedProject === 'springboot-thymeleaf' }\">\n                  <h3>🍃 SpringBoot + Thymeleaf</h3>\n                  <p>传统的服务端渲染架构，适合企业级应用开发，集成Thymeleaf模板引擎</p>\n                </div>\n\n                <div class=\"feature-card\" @click=\"selectProject('springboot-miniprogram')\"\n                  :class=\"{ active: selectedProject === 'springboot-miniprogram' }\">\n                  <h3>📱 SpringBoot + 小程序</h3>\n                  <p>微信小程序后端API开发，提供完整的用户认证和数据管理功能</p>\n                </div>\n\n                <div class=\"feature-card\" @click=\"selectProject('springboot-vue')\"\n                  :class=\"{ active: selectedProject === 'springboot-vue' }\">\n                  <h3>⚡ SpringBoot + Vue</h3>\n                  <p>现代化前后端分离架构，Vue.js前端 + SpringBoot后端API</p>\n                </div>\n\n                <div class=\"feature-card\" @click=\"selectProject('ssm-vue')\"\n                  :class=\"{ active: selectedProject === 'ssm-vue' }\">\n                  <h3>🔧 SSM + Vue</h3>\n                  <p>经典的SSM框架（Spring + SpringMVC + MyBatis）配合Vue.js前端</p>\n                </div>\n              </div>\n            </div>\n\n            <!-- 项目配置区域 -->\n            <div v-if=\"selectedProject\" class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>项目配置 - {{ getProjectName(selectedProject) }}</h3>\n                <p>配置项目基本信息和生成参数</p>\n              </div>\n              <div class=\"form-section\">\n                <el-form :model=\"projectForm\" label-width=\"120px\" class=\"project-form\">\n                  <el-row :gutter=\"20\">\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"数据库类型\">\n                        <el-select v-model=\"projectForm.databaseType\" placeholder=\"请选择数据库类型\" style=\"width: 100%\">\n                          <el-option label=\"MySQL\" value=\"mysql\" />\n                          <el-option label=\"SQL Server\" value=\"sqlserver\" />\n                        </el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"数据库模式\">\n                        <el-select v-model=\"projectForm.databaseMode\" placeholder=\"请选择数据库模式\" style=\"width: 100%\"\n                          @change=\"handleDatabaseModeChange\">\n                          <el-option label=\"新建数据库\" value=\"new\" />\n                          <el-option label=\"已有数据库\" value=\"existing\" />\n                        </el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"项目中文名称\">\n                        <el-input v-model=\"projectForm.name\" placeholder=\"请输入项目中文名称\" />\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n\n                  <el-row :gutter=\"20\" v-if=\"projectForm.databaseMode === 'new'\">\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"项目编号\">\n                        <el-input v-model=\"projectForm.projectCode\" placeholder=\"项目编号\" />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"数据库名称\">\n                        <el-input v-model=\"projectForm.databaseName\" placeholder=\"请输入数据库名称\" />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"学校名称\">\n                        <el-input v-model=\"projectForm.schoolName\" placeholder=\"请输入学校名称\" />\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n\n                  <el-row :gutter=\"20\" v-if=\"projectForm.databaseMode === 'existing'\">\n                    <el-col :span=\"24\">\n                      <el-form-item label=\"选择数据库\">\n                        <el-select v-model=\"projectForm.selectedDatabase\" placeholder=\"请选择数据库\" style=\"width: 100%\"\n                          @change=\"handleProjectSelect\" :loading=\"projectsLoading\">\n                          <el-option v-for=\"db in availableDatabases\" :key=\"db.value\" :label=\"db.text\" :value=\"db.value\"\n                            :disabled=\"!db.value\" />\n                        </el-select>\n                        <div class=\"form-text\" v-if=\"availableDatabases.length > 0\">\n                          格式：项目编号--数据库名称 (项目中文名称)\n                        </div>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n\n                  <el-row :gutter=\"20\">\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"后台模板\">\n                        <el-input v-model=\"projectForm.backendTemplate\" placeholder=\"点击选择后台模板\" readonly\n                          @click=\"openTemplateModal\" />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"前台模板\">\n                        <el-input v-model=\"projectForm.frontendTemplate\" placeholder=\"点击选择前台模板\" readonly\n                          @click=\"openFrontTemplateModal\" />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"复制项目\">\n                        <el-input v-model=\"projectForm.copyProject\" placeholder=\"请输入要复制的项目\" />\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n\n                  <el-row :gutter=\"20\">\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"layer弹出层\">\n                        <el-select v-model=\"projectForm.layer\" style=\"width: 100%\">\n                          <el-option label=\"否\" value=\"否\" />\n                          <el-option label=\"是\" value=\"是\" />\n                        </el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"统计图表\">\n                        <el-select v-model=\"projectForm.charts\" style=\"width: 100%\">\n                          <el-option label=\"否\" value=\"否\" />\n                          <el-option label=\"是\" value=\"是\" />\n                        </el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <!-- 空列，保持布局平衡 -->\n                    </el-col>\n                  </el-row>\n\n                  <el-form-item label=\"Session信息\">\n                    <el-row :gutter=\"10\">\n                      <el-col :span=\"6\">\n                        <el-input v-model=\"projectForm.adminId\" placeholder=\"管理员ID\" />\n                      </el-col>\n                      <el-col :span=\"6\">\n                        <el-input v-model=\"projectForm.adminName\" placeholder=\"管理员姓名\" />\n                      </el-col>\n                      <el-col :span=\"6\">\n                        <el-input v-model=\"projectForm.adminRole\" placeholder=\"管理员角色\" />\n                      </el-col>\n                      <el-col :span=\"6\">\n                        <el-input v-model=\"projectForm.adminLoginName\" placeholder=\"登录名\" />\n                      </el-col>\n                    </el-row>\n                  </el-form-item>\n                </el-form>\n\n                <div class=\"form-actions\">\n                  <el-button type=\"primary\" @click=\"nextStep\" size=\"large\">\n                    🎯 下一步：设计表单\n                  </el-button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 设计表单 -->\n        <el-tab-pane label=\"设计表单\" name=\"form\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <Edit />\n              </el-icon>\n              设计表单\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>数据表管理</h3>\n                <p>设计您的数据库表结构和表单字段</p>\n              </div>\n\n              <!-- 项目信息显示 -->\n              <div v-if=\"currentProjectInfo\" class=\"project-info-section\">\n                <div class=\"project-info-card\">\n                  <h4>当前项目：{{ currentProjectInfo.name }}</h4>\n                  <div class=\"project-details\">\n                    <span class=\"project-detail-item\">\n                      <strong>项目编号：</strong>{{ currentProjectInfo.projectCode }}\n                    </span>\n                    <span class=\"project-detail-item\">\n                      <strong>数据库：</strong>{{ currentProjectInfo.databaseName }}\n                    </span>\n                    <span class=\"project-detail-item\">\n                      <strong>类型：</strong>{{ getProjectName(selectedProject) }}\n                    </span>\n                  </div>\n                </div>\n              </div>\n\n              <!-- 表单列表 -->\n              <div class=\"table-designer\">\n                <div class=\"table-list-header\">\n                  <h4>数据表列表</h4>\n                  <el-button type=\"primary\" :icon=\"Plus\" @click=\"openTableDesignModal()\">\n                    添加新表\n                  </el-button>\n                </div>\n\n                <div v-if=\"projectTablesLoading\" class=\"loading-section\">\n                  <el-icon class=\"is-loading\">\n                    <Loading />\n                  </el-icon>\n                  <span>正在加载表单数据...</span>\n                </div>\n\n                <div v-else-if=\"projectTables.length === 0\" class=\"empty-section\">\n                  <el-empty description=\"暂无数据表\">\n                    <el-button type=\"primary\" @click=\"openTableDesignModal()\">创建第一个表</el-button>\n                  </el-empty>\n                </div>\n\n                <div v-else class=\"table-list\">\n                  <div class=\"table-item\" v-for=\"table in projectTables\" :key=\"table.tid\"\n                    @click=\"openTableDesignModal(table)\">\n                    <div class=\"table-item-header\">\n                      <h5>{{ table.tword || table.tname }}</h5>\n                      <div class=\"table-item-actions\">\n                        <el-button size=\"small\" :icon=\"Edit\" @click.stop=\"openTableDesignModal(table)\">编辑</el-button>\n                        <el-button size=\"small\" type=\"danger\" :icon=\"Delete\"\n                          @click.stop=\"deleteTable(table)\">删除</el-button>\n                      </div>\n                    </div>\n                    <p class=\"table-item-description\">\n                      {{ table.tname }} ({{ getTableFieldCount(table) }}个字段)\n                    </p>\n                    <div class=\"table-item-functions\" v-if=\"table.tgn\">\n                      <span class=\"function-tag\" v-for=\"func in getTableFunctions(table)\" :key=\"func\">{{ func }}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"form-actions\" style=\"margin-top: 30px;\">\n                <el-button @click=\"activeTab = 'project'\" size=\"large\">\n                  ← 上一步：项目配置\n                </el-button>\n                <el-button type=\"primary\" @click=\"nextStep\" size=\"large\">\n                  下一步：生成项目 →\n                </el-button>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 生成项目 -->\n        <el-tab-pane label=\"生成项目\" name=\"generate\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <Cpu />\n              </el-icon>\n              生成项目\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>生成项目</h3>\n                <p>确认配置信息并生成您的项目</p>\n              </div>\n\n              <!-- 项目配置摘要 -->\n              <div class=\"generation-summary\">\n                <h4>项目配置摘要</h4>\n                <el-row :gutter=\"20\">\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">项目名称:</span>\n                      <span class=\"summary-value\">{{ projectForm.name || '未设置' }}</span>\n                    </div>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">数据库名称:</span>\n                      <span class=\"summary-value\">{{ projectForm.databaseName || '未设置' }}</span>\n                    </div>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">项目编号:</span>\n                      <span class=\"summary-value\">{{ projectForm.projectCode || '未设置' }}</span>\n                    </div>\n                  </el-col>\n                </el-row>\n                <el-row :gutter=\"20\" style=\"margin-top: 15px;\">\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">数据表数量:</span>\n                      <span class=\"summary-value\">{{ projectTables.length }} 个</span>\n                    </div>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">后台模板:</span>\n                      <span class=\"summary-value\">{{ projectForm.backendTemplate || '未选择' }}</span>\n                    </div>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">前台模板:</span>\n                      <span class=\"summary-value\">{{ projectForm.frontendTemplate || '未选择' }}</span>\n                    </div>\n                  </el-col>\n                </el-row>\n              </div>\n\n              <!-- 生成操作区域 -->\n              <div class=\"generation-actions\">\n                <el-button\n                  type=\"primary\"\n                  size=\"large\"\n                  @click=\"generateProject\"\n                  :loading=\"generationInProgress\"\n                  :disabled=\"!canGenerate || generationInProgress\">\n                  <el-icon v-if=\"!generationInProgress\">\n                    <Cpu />\n                  </el-icon>\n                  <el-icon v-else>\n                    <Loading />\n                  </el-icon>\n                  {{ generationInProgress ? '正在生成项目，请稍候...' : '🚀 生成项目' }}\n                </el-button>\n\n                <!-- 生成进度提示 -->\n                <div v-if=\"generationInProgress\" class=\"generation-progress\">\n                  <el-progress :percentage=\"100\" :show-text=\"false\" status=\"success\" :indeterminate=\"true\" />\n                  <p class=\"progress-text\">正在生成代码文件和项目结构...</p>\n                </div>\n              </div>\n\n              <!-- 项目生成结果显示区域 -->\n              <div v-if=\"generationResult\" class=\"generation-result\">\n                <h4>项目生成结果</h4>\n\n                <!-- 生成状态 -->\n                <div class=\"generation-status\" :class=\"generationResult.status\">\n                  <el-icon class=\"status-icon\">\n                    <Check v-if=\"generationResult.status === 'success'\" />\n                    <Close v-else />\n                  </el-icon>\n                  <span class=\"status-text\">{{ generationResult.message }}</span>\n                </div>\n\n                <!-- 文件列表 -->\n                <div v-if=\"generationResult.files\" class=\"file-list-section\">\n                  <h5>生成的文件列表 ({{ generationResult.files.savedCount }}/{{ generationResult.files.totalCount }})</h5>\n\n                  <!-- 成功文件列表 -->\n                  <div v-if=\"generationResult.files.savedFiles && generationResult.files.savedFiles.length > 0\" class=\"success-files\">\n                    <h6>✅ 成功生成的文件:</h6>\n                    <el-scrollbar max-height=\"200px\">\n                      <ul class=\"file-list success\">\n                        <li v-for=\"file in generationResult.files.savedFiles\" :key=\"file\">\n                          <el-icon class=\"file-icon\"><Document /></el-icon>\n                          <span class=\"file-name\">{{ file }}</span>\n                        </li>\n                      </ul>\n                    </el-scrollbar>\n                  </div>\n\n                  <!-- 失败文件列表 -->\n                  <div v-if=\"generationResult.files.failedFiles && generationResult.files.failedFiles.length > 0\" class=\"failed-files\">\n                    <h6>❌ 生成失败的文件:</h6>\n                    <el-scrollbar max-height=\"200px\">\n                      <ul class=\"file-list error\">\n                        <li v-for=\"file in generationResult.files.failedFiles\" :key=\"file.fileName\">\n                          <el-icon class=\"file-icon\"><Warning /></el-icon>\n                          <span class=\"file-name\">{{ file.fileName }}</span>\n                          <span class=\"file-error\">{{ file.error }}</span>\n                        </li>\n                      </ul>\n                    </el-scrollbar>\n                  </div>\n                </div>\n\n                <!-- 压缩结果 -->\n                <div v-if=\"generationResult.compression\" class=\"compression-result\">\n                  <h5>项目压缩结果</h5>\n                  <div class=\"compression-info\" :class=\"generationResult.compression.status\">\n                    <el-icon class=\"status-icon\">\n                      <Box v-if=\"generationResult.compression.status === 'success'\" />\n                      <Close v-else />\n                    </el-icon>\n                    <span class=\"compression-text\">{{ generationResult.compression.message }}</span>\n                    <div v-if=\"generationResult.compression.data\" class=\"compression-details\">\n                      <p>文件名: {{ generationResult.compression.data.zipFileName }}</p>\n                      <p>文件大小: {{ generationResult.compression.data.fileSize }}</p>\n                      <el-button type=\"success\" @click=\"downloadProject(generationResult.compression.data.zipFilePath)\">\n                        <el-icon><Download /></el-icon>\n                        下载项目文件\n                      </el-button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 数据 -->\n        <el-tab-pane label=\"数据\" name=\"data\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <DataBoard />\n              </el-icon>\n              数据\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>数据脚本管理</h3>\n                <p>生成和管理数据库建表及插入数据脚本</p>\n              </div>\n              <div class=\"data-section\">\n                <div class=\"data-toolbar\">\n                  <el-button type=\"primary\" :icon=\"Plus\" @click=\"generateDataScript\" :loading=\"dataGenerationInProgress\">生成数据脚本</el-button>\n                  <el-button :icon=\"DocumentCopy\" @click=\"copyDataScript\">复制脚本</el-button>\n                </div>\n                <div class=\"data-editor\">\n                  <el-input v-model=\"dataContent\" type=\"textarea\" :rows=\"15\" placeholder=\"数据脚本将在这里显示...\"\n                    class=\"data-textarea\" />\n                </div>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- SQL脚本 -->\n        <el-tab-pane label=\"SQL脚本\" name=\"sql\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <DocumentCopy />\n              </el-icon>\n              SQL脚本\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>SQL脚本管理</h3>\n                <p>生成和管理数据库脚本</p>\n              </div>\n              <div class=\"sql-section\">\n                <div class=\"sql-toolbar\">\n                  <el-button type=\"primary\" :icon=\"Plus\" @click=\"generateSqlScript\" :loading=\"sqlGenerationInProgress\">生成建表脚本</el-button>\n                  <el-button :icon=\"Download\" @click=\"exportSqlScript\">导出脚本</el-button>\n                  <el-button :icon=\"DocumentCopy\" @click=\"copySqlScript\">复制脚本</el-button>\n                </div>\n                <div class=\"sql-editor\">\n                  <el-input v-model=\"sqlContent\" type=\"textarea\" :rows=\"25\" placeholder=\"SQL脚本将在这里显示...\"\n                    class=\"sql-textarea\" />\n                </div>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 错误日志 -->\n        <el-tab-pane label=\"错误日志\" name=\"logs\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <Warning />\n              </el-icon>\n              错误日志\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>错误日志</h3>\n                <p>查看生成过程中的错误和警告信息</p>\n              </div>\n              <div class=\"logs-section\">\n                <div class=\"logs-toolbar\">\n                  <el-button :icon=\"Refresh\">刷新日志</el-button>\n                  <el-button :icon=\"Delete\">清空日志</el-button>\n                  <el-select v-model=\"logLevel\" placeholder=\"日志级别\" style=\"width: 120px\">\n                    <el-option label=\"全部\" value=\"all\" />\n                    <el-option label=\"错误\" value=\"error\" />\n                    <el-option label=\"警告\" value=\"warning\" />\n                    <el-option label=\"信息\" value=\"info\" />\n                  </el-select>\n                </div>\n                <div class=\"logs-content\">\n                  <div class=\"log-item\" v-for=\"(log, index) in logs\" :key=\"index\" :class=\"log.level\">\n                    <div class=\"log-time\">{{ log.time }}</div>\n                    <div class=\"log-level\">{{ log.level.toUpperCase() }}</div>\n                    <div class=\"log-message\">{{ log.message }}</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n      </el-tabs>\n    </div>\n\n    <!-- 登录对话框 -->\n    <el-dialog v-model=\"loginDialogVisible\" title=\"用户登录\" width=\"400px\" :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\" :show-close=\"false\">\n      <el-form :model=\"loginForm\" :rules=\"loginRules\" ref=\"loginFormRef\" label-width=\"80px\">\n        <el-form-item label=\"用户名\" prop=\"username\">\n          <el-input v-model=\"loginForm.username\" placeholder=\"请输入用户名\" />\n        </el-form-item>\n        <el-form-item label=\"密码\" prop=\"password\">\n          <el-input v-model=\"loginForm.password\" type=\"password\" placeholder=\"请输入密码\" @keyup.enter=\"handleLogin\" />\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button type=\"primary\" @click=\"handleLogin\" :loading=\"loginLoading\">\n            登录\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n\n    <!-- 表设计弹窗 -->\n    <el-dialog v-model=\"showTableDesignModal\" :title=\"currentTableDesign.id ? '编辑数据表' : '创建数据表'\" width=\"85%\"\n      :close-on-click-modal=\"false\" :before-close=\"closeTableDesignModal\" class=\"table-design-dialog\" top=\"5vh\"\n      destroy-on-close>\n      <div class=\"table-design-container\">\n        <!-- 自定义Tab导航 -->\n        <div class=\"custom-tabs\">\n          <div class=\"tab-nav\">\n            <div class=\"tab-item\" :class=\"{ active: currentTableTab === 'table-settings' }\"\n              @click=\"switchTableTab('table-settings')\">\n              <el-icon class=\"tab-icon\">\n                <Setting />\n              </el-icon>\n              <span class=\"tab-text\">表设置</span>\n            </div>\n            <div class=\"tab-item\" :class=\"{ active: currentTableTab === 'field-design' }\"\n              @click=\"switchTableTab('field-design')\">\n              <el-icon class=\"tab-icon\">\n                <DataBoard />\n              </el-icon>\n              <span class=\"tab-text\">字段设计</span>\n            </div>\n          </div>\n\n          <!-- Tab内容 -->\n          <div class=\"tab-content-wrapper\">\n            <!-- 表设置内容 -->\n            <div v-show=\"currentTableTab === 'table-settings'\" class=\"tab-content\">\n              <!-- 基本信息 -->\n              <div class=\"basic-info-section\">\n                <el-form :model=\"currentTableDesign\" label-width=\"100px\" class=\"design-form\" size=\"default\">\n                  <el-row :gutter=\"16\">\n                    <el-col :span=\"6\">\n                      <el-form-item label=\"表中文名称\" required>\n                        <el-input v-model=\"currentTableDesign.chineseName\" placeholder=\"请输入表的中文名称\" clearable />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"6\">\n                      <el-form-item label=\"表英文名称\" required>\n                        <el-input v-model=\"currentTableDesign.englishName\" placeholder=\"请输入表的英文名称\" clearable />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"6\">\n                      <el-form-item label=\"菜单显示顺序\">\n                        <el-input-number v-model=\"currentTableDesign.menuOrder\" :min=\"1\" :max=\"999\" placeholder=\"1\" style=\"width: 100%;\" />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"6\">\n                      <el-form-item label=\"生成数据条数\">\n                        <el-input-number v-model=\"currentTableDesign.generateDataCount\" :min=\"0\" :max=\"1000\"\n                          placeholder=\"生成数据条数\" style=\"width: 100%;\" />\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n\n                  <el-row :gutter=\"16\">\n                    <el-col :span=\"24\">\n                      <el-form-item label=\"AI助手\">\n                        <el-button type=\"primary\" @click=\"showAiGeneratorModal('table')\" style=\"width: 100%;\">\n                          <el-icon>\n                            <Cpu />\n                          </el-icon>\n                          AI生成表结构\n                        </el-button>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                </el-form>\n              </div>\n\n              <!-- 功能选择 -->\n              <div class=\"function-selection-section\">\n                <h4 style=\"margin: 20px 0 15px 0; color: #2c3e50; font-size: 16px;\">功能选择</h4>\n                <el-row :gutter=\"20\">\n                  <!-- 后台功能 -->\n                  <el-col :span=\"8\">\n                    <div class=\"function-group\">\n                      <h5>后台功能</h5>\n                      <div class=\"function-checkboxes\">\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendAdd\">后台添加</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendEdit\">后台修改</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendDelete\">后台删除</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendDetail\">后台详情</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendList\">后台列表</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.batchImport\">批量导入</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.batchExport\">批量导出</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendLogin\">后台登录</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendRegister\">后台注册</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendProfile\">后台个人信息</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendPassword\">后台修改密码</el-checkbox>\n                      </div>\n                    </div>\n                  </el-col>\n\n                  <!-- 前台功能 -->\n                  <el-col :span=\"8\">\n                    <div class=\"function-group\">\n                      <h5>前台功能</h5>\n                      <div class=\"function-checkboxes\">\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendAdd\">前台添加</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendEdit\">前台修改</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendDelete\">前台删除</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendDetail\">前台详情</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendList\">前台列表</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendLogin\">前台个人信息和密码</el-checkbox>\n                      </div>\n                    </div>\n                  </el-col>\n\n                  <!-- 小程序功能 -->\n                  <el-col :span=\"8\">\n                    <div class=\"function-group\">\n                      <h5>小程序功能</h5>\n                      <div class=\"function-checkboxes\">\n                        <el-checkbox v-model=\"currentTableDesign.functions.miniAdd\">小程序添加</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.miniEdit\">小程序编辑</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.miniDelete\">小程序删除</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.miniDetail\">小程序详情</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.miniList\">小程序List</el-checkbox>\n                      </div>\n                    </div>\n                  </el-col>\n                </el-row>\n              </div>\n            </div>\n\n            <!-- 字段设计内容 -->\n            <div v-show=\"currentTableTab === 'field-design'\" class=\"tab-content\">\n              <!-- 工具栏 -->\n              <div class=\"field-toolbar-compact\">\n                <el-space size=\"default\" wrap>\n                  <el-button type=\"success\" @click=\"addNewFieldToDesign\">\n                    <el-icon>\n                      <Plus />\n                    </el-icon>\n                    添加字段\n                  </el-button>\n                  <el-button type=\"warning\" @click=\"resetFields\">\n                    <el-icon>\n                      <Refresh />\n                    </el-icon>\n                    重置字段\n                  </el-button>\n                  <el-button type=\"danger\" @click=\"clearAllFields\">\n                    <el-icon>\n                      <Delete />\n                    </el-icon>\n                    清空字段\n                  </el-button>\n                  <el-divider direction=\"vertical\" />\n                  <el-text type=\"info\">\n                    当前字段数量: {{ currentTableDesign.fields?.length || 0 }}\n                  </el-text>\n                </el-space>\n              </div>\n\n              <!-- 字段表格 -->\n              <div class=\"field-table-section\">\n                <el-table :data=\"currentTableDesign.fields\" class=\"field-table\" border stripe\n                  empty-text=\"暂无字段，请点击添加字段按钮\" size=\"small\">\n                  <el-table-column label=\"序号\" type=\"index\" width=\"60\" align=\"center\" />\n\n                  <el-table-column label=\"中文名称\" min-width=\"100\">\n                    <template #default=\"{ row }\">\n                      <el-input v-model=\"row.chineseName\" placeholder=\"请输入中文名称\" size=\"small\" clearable />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"英文名称\" min-width=\"100\">\n                    <template #default=\"{ row }\">\n                      <el-input v-model=\"row.englishName\" placeholder=\"请输入英文名称\" size=\"small\" clearable />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"字段类型\" min-width=\"80\">\n                    <template #default=\"{ row }\">\n                      <el-select v-model=\"row.type\" placeholder=\"选择类型\" size=\"small\" style=\"width: 100%\">\n                        <el-option label=\"int\" value=\"int\" />\n                        <el-option label=\"varchar(50)\" value=\"varchar(50)\" />\n                        <el-option label=\"varchar(100)\" value=\"varchar(100)\" />\n                        <el-option label=\"varchar(200)\" value=\"varchar(200)\" />\n                        <el-option label=\"varchar(500)\" value=\"varchar(500)\" />\n                        <el-option label=\"text\" value=\"text\" />\n                        <el-option label=\"datetime\" value=\"datetime\" />\n                        <el-option label=\"decimal(10,2)\" value=\"decimal(10,2)\" />\n                      </el-select>\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"控件类型\" min-width=\"100\">\n                    <template #default=\"{ row }\">\n                      <el-select v-model=\"row.controlType\" placeholder=\"选择控件\" size=\"small\" style=\"width: 100%\">\n                        <el-option label=\"文本框\" value=\"文本框\" />\n                        <el-option label=\"多行文本\" value=\"多行文本\" />\n                        <el-option label=\"下拉框\" value=\"下拉框\" />\n                        <el-option label=\"单选按钮\" value=\"单选按钮\" />\n                        <el-option label=\"复选框\" value=\"复选框\" />\n                        <el-option label=\"日期选择\" value=\"日期选择\" />\n                        <el-option label=\"时间选择\" value=\"时间选择\" />\n                        <el-option label=\"文件上传\" value=\"文件上传\" />\n                        <el-option label=\"图片上传\" value=\"图片上传\" />\n                        <el-option label=\"编辑器\" value=\"编辑器\" />\n                        <el-option label=\"自动当前时间\" value=\"自动当前时间\" />\n                      </el-select>\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"必填\" width=\"60\" align=\"center\">\n                    <template #default=\"{ row }\">\n                      <el-checkbox v-model=\"row.required\" />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"搜索\" width=\"60\" align=\"center\">\n                    <template #default=\"{ row }\">\n                      <el-checkbox v-model=\"row.searchable\" />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"显示\" width=\"60\" align=\"center\">\n                    <template #default=\"{ row }\">\n                      <el-checkbox v-model=\"row.visible\" />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"存在\" width=\"60\" align=\"center\">\n                    <template #default=\"{ row }\">\n                      <el-checkbox v-model=\"row.existsCheck\" />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"操作\" width=\"160\" align=\"center\" fixed=\"right\">\n                    <template #default=\"{ row, $index }\">\n                      <div class=\"field-actions\">\n                        <el-button size=\"small\" type=\"primary\" @click=\"moveFieldUp($index)\" :disabled=\"$index === 0\"\n                          circle>\n                          <el-icon>\n                            <ArrowUp />\n                          </el-icon>\n                        </el-button>\n                        <el-button size=\"small\" type=\"primary\" @click=\"moveFieldDown($index)\"\n                          :disabled=\"$index === currentTableDesign.fields.length - 1\" circle>\n                          <el-icon>\n                            <ArrowDown />\n                          </el-icon>\n                        </el-button>\n                        <el-button size=\"small\" type=\"warning\" @click=\"editFieldSettings(row, $index)\" circle>\n                          <el-icon>\n                            <Edit />\n                          </el-icon>\n                        </el-button>\n                        <el-button size=\"small\" type=\"danger\" @click=\"deleteFieldFromDesign($index)\" circle>\n                          <el-icon>\n                            <Delete />\n                          </el-icon>\n                        </el-button>\n                      </div>\n                    </template>\n                  </el-table-column>\n                </el-table>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-space size=\"large\">\n            <el-button @click=\"closeTableDesignModal\" size=\"large\">\n              <el-icon>\n                <Close />\n              </el-icon>\n              <span>取消</span>\n            </el-button>\n            <el-button type=\"primary\" @click=\"saveTableDesign\" size=\"large\">\n              <el-icon>\n                <Check />\n              </el-icon>\n              <span>保存表设计</span>\n            </el-button>\n          </el-space>\n        </div>\n      </template>\n    </el-dialog>\n\n    <!-- AI生成器弹窗 -->\n    <el-dialog v-model=\"showAiGeneratorDialog\" title=\"AI生成表结构\" width=\"600px\" :close-on-click-modal=\"false\"\n      :before-close=\"hideAiGeneratorModal\" class=\"ai-generator-dialog\" destroy-on-close>\n      <div class=\"ai-generator-container\">\n        <div class=\"ai-generator-header\">\n          <h4>🤖 AI智能生成表结构</h4>\n          <p>描述您要生成的表结构，AI将自动为您创建完整的数据表设计</p>\n        </div>\n\n        <div class=\"ai-generator-body\">\n          <el-form label-width=\"100px\">\n            <el-form-item label=\"表描述\">\n              <el-input v-model=\"aiGeneratorInput\" type=\"textarea\" :rows=\"4\"\n                placeholder=\"请输入表的描述，例如：学生信息管理表、商品管理系统、订单管理表等...\" class=\"ai-generator-input\"\n                :disabled=\"aiGenerationInProgress\" />\n            </el-form-item>\n          </el-form>\n\n          <div class=\"ai-generator-example\">\n            <h5>💡 示例：</h5>\n            <div class=\"example-list\">\n              <div class=\"example-item\">\n                <strong>表结构描述：</strong>\"学生信息管理表\" 或 \"商品管理系统\" 或 \"订单管理表\"\n              </div>\n              <div class=\"example-item\">\n                <strong>功能说明：</strong>AI会根据您的描述自动生成包含合适字段的完整表结构\n              </div>\n            </div>\n          </div>\n        </div>\n\n      </div>\n\n      <template #footer>\n        <div class=\"ai-generator-footer\">\n          <el-button @click=\"hideAiGeneratorModal\" :disabled=\"aiGenerationInProgress\">取消</el-button>\n          <el-button type=\"primary\" @click=\"confirmAiGeneration\" :loading=\"aiGenerationInProgress\">\n            <el-icon v-if=\"!aiGenerationInProgress\">\n              <Cpu />\n            </el-icon>\n            {{ aiGenerationInProgress ? '生成中...' : '🚀 生成' }}\n          </el-button>\n        </div>\n      </template>\n    </el-dialog>\n\n    <!-- 后台模板选择弹窗 -->\n    <el-dialog v-model=\"showBackendTemplateDialog\" title=\"选择后台模板\" width=\"1000px\" :close-on-click-modal=\"false\"\n      class=\"template-dialog\" destroy-on-close>\n      <div class=\"template-container\">\n        <div class=\"template-grid-4col\" v-loading=\"templatesLoading\">\n          <div v-for=\"template in backendTemplates\" :key=\"template.sid\" class=\"template-item-4col\">\n            <div class=\"template-image\" @click=\"selectBackendTemplate(template)\">\n              <img v-if=\"template.memo4\" :src=\"getTemplateImageUrl(template.memo4)\" :alt=\"template.sname\"\n                @error=\"handleImageError\" />\n              <div v-else class=\"no-image\">\n                <el-icon>\n                  <Picture />\n                </el-icon>\n                <span>暂无预览</span>\n              </div>\n            </div>\n            <div class=\"template-info\">\n              <h4 @click=\"selectBackendTemplate(template)\">{{ template.sname }}</h4>\n              <p class=\"template-id\">模板ID: {{ template.sid }}</p>\n              <div class=\"template-actions\">\n                <el-button type=\"primary\" size=\"small\" @click=\"selectBackendTemplate(template)\">\n                  选择模板\n                </el-button>\n                <el-button type=\"info\" size=\"small\" @click=\"viewTemplateDetail(template)\">\n                  查看详情\n                </el-button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-dialog>\n\n    <!-- 前台模板选择弹窗 -->\n    <el-dialog v-model=\"showFrontendTemplateDialog\" title=\"选择前台模板\" width=\"1000px\" :close-on-click-modal=\"false\"\n      class=\"template-dialog\" destroy-on-close>\n      <div class=\"template-container\">\n        <div class=\"template-grid-4col\" v-loading=\"templatesLoading\">\n          <div v-for=\"template in frontendTemplates\" :key=\"template.sid\" class=\"template-item-4col\">\n            <div class=\"template-image\" @click=\"selectFrontendTemplate(template)\">\n              <img v-if=\"template.memo4\" :src=\"getTemplateImageUrl(template.memo4)\" :alt=\"template.sname\"\n                @error=\"handleImageError\" />\n              <div v-else class=\"no-image\">\n                <el-icon>\n                  <Picture />\n                </el-icon>\n                <span>暂无预览</span>\n              </div>\n            </div>\n            <div class=\"template-info\">\n              <h4 @click=\"selectFrontendTemplate(template)\">{{ template.sname }}</h4>\n              <p class=\"template-id\">模板ID: {{ template.sid }}</p>\n              <div class=\"template-actions\">\n                <el-button type=\"primary\" size=\"small\" @click=\"selectFrontendTemplate(template)\">\n                  选择模板\n                </el-button>\n                <el-button type=\"info\" size=\"small\" @click=\"viewTemplateDetail(template)\">\n                  查看详情\n                </el-button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-dialog>\n\n    <!-- 模板详情查看弹窗 -->\n    <el-dialog v-model=\"showTemplateDetailDialog\" title=\"模板详情\" width=\"90%\" :close-on-click-modal=\"false\"\n      class=\"template-detail-dialog\" destroy-on-close top=\"5vh\">\n      <div class=\"template-detail-container\" v-if=\"currentTemplateDetail\">\n        <div class=\"template-detail-header\">\n          <h3>{{ currentTemplateDetail.sname }}</h3>\n          <p class=\"template-detail-id\">模板ID: {{ currentTemplateDetail.sid }}</p>\n        </div>\n        <div class=\"template-detail-image-container\">\n          <div v-if=\"currentTemplateDetail.memo4\" class=\"template-detail-image\">\n            <el-image\n              :src=\"getTemplateImageUrl(currentTemplateDetail.memo4)\"\n              :alt=\"currentTemplateDetail.sname\"\n              fit=\"contain\"\n              :preview-src-list=\"[getTemplateImageUrl(currentTemplateDetail.memo4)]\"\n              :initial-index=\"0\"\n              preview-teleported\n              class=\"template-preview-image\"\n            />\n          </div>\n          <div v-else class=\"no-image-large\">\n            <el-icon>\n              <Picture />\n            </el-icon>\n            <span>暂无预览图片</span>\n          </div>\n        </div>\n        <div class=\"template-detail-tips\">\n          <el-alert\n            title=\"提示\"\n            type=\"info\"\n            :closable=\"false\"\n            show-icon>\n            <template #default>\n              点击图片可以放大查看，支持缩放和拖拽操作\n            </template>\n          </el-alert>\n        </div>\n      </div>\n      <template #footer>\n        <div class=\"template-detail-footer\">\n          <el-button @click=\"showTemplateDetailDialog = false\" size=\"large\">关闭</el-button>\n        </div>\n      </template>\n    </el-dialog>\n\n    <!-- 字段设置弹窗 -->\n    <el-dialog v-model=\"showFieldSettingsDialog\" title=\"字段设置\" width=\"600px\" :close-on-click-modal=\"false\"\n      class=\"field-settings-dialog\" destroy-on-close>\n      <div class=\"field-settings-container\" v-if=\"currentFieldSettings\">\n        <el-form :model=\"currentFieldSettings\" label-width=\"100px\" size=\"default\">\n          <el-form-item label=\"字段名称\">\n            <el-input v-model=\"currentFieldSettings.chineseName\" readonly />\n          </el-form-item>\n\n          <el-form-item label=\"关联表\">\n            <el-input v-model=\"currentFieldSettings.relatedTable\"\n              placeholder=\"例如：doro,dbid,dormitory,1\"\n              @click=\"showRelatedTableSelector\"\n              readonly\n              style=\"cursor: pointer;\">\n              <template #suffix>\n                <el-icon style=\"cursor: pointer;\">\n                  <View />\n                </el-icon>\n              </template>\n            </el-input>\n          </el-form-item>\n\n          <el-form-item label=\"是否必填\">\n            <el-checkbox v-model=\"showInSearchList\">搜索列表显示</el-checkbox>\n          </el-form-item>\n\n          <el-form-item label=\"自定义选项\">\n            <el-input v-model=\"currentFieldSettings.customOptions\"\n              type=\"textarea\"\n              :rows=\"6\"\n              placeholder=\"一行一个选项，例如：&#10;选项1&#10;选项2&#10;选项3\" />\n          </el-form-item>\n        </el-form>\n      </div>\n\n      <template #footer>\n        <div class=\"field-settings-footer\">\n          <el-button @click=\"closeFieldSettingsDialog\">取消</el-button>\n          <el-button type=\"primary\" @click=\"saveFieldSettings\">保存</el-button>\n        </div>\n      </template>\n    </el-dialog>\n\n    <!-- 关联表选择弹窗 -->\n    <el-dialog v-model=\"showRelatedTableDialog\" title=\"选择关联表\" width=\"800px\" :close-on-click-modal=\"false\"\n      class=\"related-table-dialog\" destroy-on-close>\n      <div class=\"related-table-container\">\n        <el-table :data=\"availableRelatedTables\" border stripe size=\"small\" max-height=\"400\">\n          <el-table-column label=\"主键ID\" prop=\"primaryKey\" width=\"80\" align=\"center\" />\n          <el-table-column label=\"名称\" prop=\"displayName\" width=\"120\" />\n          <el-table-column label=\"表名称\" prop=\"tableName\" width=\"150\" />\n          <el-table-column label=\"联动\" width=\"100\" align=\"center\">\n            <template #default=\"{ row }\">\n              <el-input v-model=\"row.linkValue\" size=\"small\" style=\"width: 60px;\" />\n            </template>\n          </el-table-column>\n          <el-table-column label=\"选择\" width=\"80\" align=\"center\">\n            <template #default=\"{ row }\">\n              <el-checkbox v-model=\"row.selected\" />\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n\n      <template #footer>\n        <div class=\"related-table-footer\">\n          <el-button @click=\"closeRelatedTableDialog\">取消</el-button>\n          <el-button type=\"primary\" @click=\"confirmRelatedTableSelection\">确定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<style scoped>\n  @import '../../styles/CodeGenerator.css';\n\n  /* 生成进度样式 */\n  .generation-progress {\n    margin-top: 20px;\n    padding: 20px;\n    background: #f8f9fa;\n    border-radius: 8px;\n    text-align: center;\n  }\n\n  .progress-text {\n    margin-top: 10px;\n    color: #606266;\n    font-size: 14px;\n  }\n\n  /* 生成结果样式 */\n  .generation-result {\n    margin-top: 30px;\n    padding: 20px;\n    border: 1px solid #e4e7ed;\n    border-radius: 8px;\n    background: #fff;\n  }\n\n  .generation-status {\n    display: flex;\n    align-items: center;\n    margin-bottom: 20px;\n    padding: 15px;\n    border-radius: 6px;\n  }\n\n  .generation-status.success {\n    background: #f0f9ff;\n    border: 1px solid #67c23a;\n    color: #67c23a;\n  }\n\n  .generation-status.error {\n    background: #fef0f0;\n    border: 1px solid #f56c6c;\n    color: #f56c6c;\n  }\n\n  .status-icon {\n    margin-right: 10px;\n    font-size: 18px;\n  }\n\n  .status-text {\n    font-weight: 500;\n    font-size: 16px;\n  }\n\n  /* 文件列表样式 */\n  .file-list-section {\n    margin-top: 20px;\n  }\n\n  .file-list {\n    list-style: none;\n    padding: 0;\n    margin: 0;\n  }\n\n  .file-list li {\n    display: flex;\n    align-items: center;\n    padding: 8px 12px;\n    margin-bottom: 4px;\n    border-radius: 4px;\n    background: #f8f9fa;\n  }\n\n  .file-list.success li {\n    background: #f0f9ff;\n    border-left: 3px solid #67c23a;\n  }\n\n  .file-list.error li {\n    background: #fef0f0;\n    border-left: 3px solid #f56c6c;\n  }\n\n  .file-icon {\n    margin-right: 8px;\n    color: #909399;\n  }\n\n  .file-name {\n    flex: 1;\n    font-family: 'Courier New', monospace;\n    font-size: 13px;\n  }\n\n  .file-error {\n    color: #f56c6c;\n    font-size: 12px;\n    margin-left: 10px;\n  }\n\n  /* 压缩结果样式 */\n  .compression-result {\n    margin-top: 20px;\n    padding: 15px;\n    border: 1px solid #e4e7ed;\n    border-radius: 6px;\n    background: #fafafa;\n  }\n\n  .compression-info {\n    display: flex;\n    align-items: center;\n    flex-wrap: wrap;\n  }\n\n  .compression-text {\n    margin-left: 10px;\n    font-weight: 500;\n  }\n\n  .compression-details {\n    width: 100%;\n    margin-top: 15px;\n    padding-top: 15px;\n    border-top: 1px solid #e4e7ed;\n  }\n\n  .compression-details p {\n    margin: 5px 0;\n    color: #606266;\n  }\n</style>\n\n<script>\n  import { ref, reactive, onMounted, nextTick, computed } from 'vue'\n  import { ElMessage, ElMessageBox } from 'element-plus'\n  import request, { base } from \"../../../utils/http\"\n  import {\n    Setting, Folder, Edit, Cpu, DataBoard, DocumentCopy, Warning,\n    Plus, View, Download, Delete, Document, Monitor, Box, Refresh,\n    User, ArrowDown, ArrowUp, Loading, Close, Check, Picture\n  } from '@element-plus/icons-vue'\n\n  export default {\n    name: 'CodeGenerator',\n    components: {\n      Setting, Folder, Edit, Cpu, DataBoard, DocumentCopy, Warning,\n      Plus, View, Download, Delete, Document, Monitor, Box, Refresh,\n      User, ArrowDown, ArrowUp, Loading, Close, Check, Picture\n    },\n    setup() {\n      const activeTab = ref('project')\n      const logLevel = ref('all')\n\n      // 登录相关状态\n      const isLoggedIn = ref(false)\n      const loginDialogVisible = ref(false)\n      const loginLoading = ref(false)\n      const loginFormRef = ref(null)\n\n      // 用户信息\n      const userInfo = reactive({\n        username: '',\n        loginTime: ''\n      })\n\n      // 登录表单\n      const loginForm = reactive({\n        username: '',\n        password: ''\n      })\n\n      // 登录表单验证规则\n      const loginRules = {\n        username: [\n          { required: true, message: '请输入用户名', trigger: 'blur' }\n        ],\n        password: [\n          { required: true, message: '请输入密码', trigger: 'blur' }\n        ]\n      }\n\n      // 项目选择和配置\n      const selectedProject = ref('')\n      const projectsLoading = ref(false)\n      const availableDatabases = ref([\n        { value: '', text: '请选择数据库' }\n      ])\n\n      const projectForm = reactive({\n        databaseType: 'mysql',\n        databaseMode: 'new',\n        projectCode: '',\n        databaseName: '',\n        selectedDatabase: '',\n        name: '',\n        packageName: 'com',\n        backendTemplate: '',\n        frontendTemplate: '',\n        layer: '否',\n        charts: '否',\n        schoolName: '',\n        adminId: '',\n        adminName: '',\n        adminRole: '',\n        adminLoginName: '',\n        copyProject: ''\n      })\n\n      // 项目表单相关\n      const currentProjectInfo = ref(null)\n      const projectTables = ref([])\n      const projectTablesLoading = ref(false)\n\n      const formFields = ref([\n        { name: 'id', type: 'Long' },\n        { name: 'name', type: 'String' },\n        { name: 'email', type: 'String' },\n        { name: 'createTime', type: 'Date' }\n      ])\n\n      const tableData = ref([\n        { name: 'user', fields: 8, status: '已配置', updateTime: '2024-01-15 10:30:00' },\n        { name: 'role', fields: 5, status: '未配置', updateTime: '2024-01-15 09:15:00' },\n        { name: 'permission', fields: 6, status: '已配置', updateTime: '2024-01-14 16:45:00' }\n      ])\n\n      const sqlContent = ref(``)\n\n      const logs = ref([\n        { time: '2024-01-15 10:30:15', level: 'info', message: '开始生成代码...' },\n        { time: '2024-01-15 10:30:16', level: 'success', message: '实体类生成成功' },\n        { time: '2024-01-15 10:30:17', level: 'warning', message: '字段名称建议使用驼峰命名' },\n        { time: '2024-01-15 10:30:18', level: 'error', message: '数据库连接失败，请检查配置' }\n      ])\n\n      // Cookie操作工具函数\n      const setCookie = (name, value, days) => {\n        const expires = new Date()\n        expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000))\n        document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`\n      }\n\n      const getCookie = (name) => {\n        const nameEQ = name + \"=\"\n        const ca = document.cookie.split(';')\n        for (let i = 0; i < ca.length; i++) {\n          let c = ca[i]\n          while (c.charAt(0) === ' ') c = c.substring(1, c.length)\n          if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length)\n        }\n        return null\n      }\n\n      const deleteCookie = (name) => {\n        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`\n      }\n\n      // 检查登录状态\n      const checkLoginStatus = () => {\n        // 首先检查sessionStorage中的用户信息\n        const sessionUser = sessionStorage.getItem(\"user\")\n        const sessionUserLname = sessionStorage.getItem(\"userLname\")\n\n        if (sessionUser && sessionUserLname) {\n          try {\n            JSON.parse(sessionUser) // 验证JSON格式\n            userInfo.username = sessionUserLname\n            userInfo.loginTime = new Date().toLocaleString()\n            isLoggedIn.value = true\n            return\n          } catch (error) {\n            console.error('解析sessionStorage用户信息失败:', error)\n          }\n        }\n\n        // 如果sessionStorage中没有，再检查Cookie\n        const savedUser = getCookie('codeGeneratorUser')\n        if (savedUser) {\n          try {\n            const userData = JSON.parse(decodeURIComponent(savedUser))\n            userInfo.username = userData.username\n            userInfo.loginTime = userData.loginTime\n            isLoggedIn.value = true\n          } catch (error) {\n            console.error('解析Cookie用户信息失败:', error)\n            deleteCookie('codeGeneratorUser')\n          }\n        } else {\n          loginDialogVisible.value = true\n        }\n      }\n\n      // 处理登录\n      const handleLogin = async () => {\n        if (!loginFormRef.value) return\n\n        // 基本验证\n        if (!loginForm.username) {\n          ElMessage.warning('请输入用户名')\n          return\n        }\n        if (!loginForm.password) {\n          ElMessage.warning('请输入密码')\n          return\n        }\n\n        try {\n          loginLoading.value = true\n\n          // 调用登录API\n          const url = base + \"/admin/login\"\n          const loginData = {\n            lname: loginForm.username,\n            pwd: loginForm.password\n          }\n\n          const res = await request.post(url, loginData)\n          loginLoading.value = false\n\n          if (res.code == 200) {\n            console.log('登录成功:', JSON.stringify(res.resdata))\n\n            // 保存用户信息到sessionStorage（参考管理员登录）\n            sessionStorage.setItem(\"user\", JSON.stringify(res.resdata))\n            sessionStorage.setItem(\"userLname\", res.resdata.lname)\n            sessionStorage.setItem(\"role\", \"管理员\")\n\n            // 更新本地状态\n            userInfo.username = res.resdata.lname\n            userInfo.loginTime = new Date().toLocaleString()\n\n            // 保存到Cookie，有效期15天\n            const userData = {\n              username: res.resdata.lname,\n              loginTime: userInfo.loginTime,\n              userId: res.resdata.id\n            }\n            setCookie('codeGeneratorUser', encodeURIComponent(JSON.stringify(userData)), 15)\n\n            isLoggedIn.value = true\n            loginDialogVisible.value = false\n\n            // 重置表单\n            loginForm.username = ''\n            loginForm.password = ''\n\n            ElMessage.success('登录成功！')\n          } else {\n            ElMessage.error(res.msg || '登录失败')\n          }\n        } catch (error) {\n          loginLoading.value = false\n          console.error('登录失败:', error)\n          ElMessage.error('登录失败，请检查网络连接')\n        }\n      }\n\n      // 处理用户下拉菜单命令\n      const handleUserCommand = (command) => {\n        if (command === 'logout') {\n          handleLogout()\n        }\n      }\n\n      // 退出登录\n      const handleLogout = () => {\n        // 清除Cookie\n        deleteCookie('codeGeneratorUser')\n\n        // 清除sessionStorage\n        sessionStorage.removeItem(\"user\")\n        sessionStorage.removeItem(\"userLname\")\n        sessionStorage.removeItem(\"role\")\n\n        // 重置状态\n        isLoggedIn.value = false\n        userInfo.username = ''\n        userInfo.loginTime = ''\n        loginDialogVisible.value = true\n\n        ElMessage.success('已退出登录')\n      }\n\n      // 项目类型选择\n      const selectProject = (projectType) => {\n        selectedProject.value = projectType\n        console.log('选择项目类型:', projectType)\n      }\n\n      // 获取项目名称\n      const getProjectName = (projectType) => {\n        const projectNames = {\n          'springboot-thymeleaf': 'SpringBoot + Thymeleaf',\n          'springboot-miniprogram': 'SpringBoot + 小程序',\n          'springboot-vue': 'SpringBoot + Vue',\n          'ssm-vue': 'SSM + Vue'\n        }\n        return projectNames[projectType] || projectType\n      }\n\n      // 打开模板选择弹窗\n      const openTemplateModal = () => {\n        console.log('打开后台模板选择弹窗')\n        showBackendTemplateSelector()\n      }\n\n      const openFrontTemplateModal = () => {\n        console.log('打开前台模板选择弹窗')\n        showFrontendTemplateSelector()\n      }\n\n      // 处理数据库模式变化\n      const handleDatabaseModeChange = (mode) => {\n        if (mode === 'existing') {\n          loadProjects()\n        } else {\n          // 清空已有数据库选项\n          availableDatabases.value = [{ value: '', text: '请选择数据库' }]\n          projectForm.selectedDatabase = ''\n        }\n      }\n\n      // 加载项目列表\n      const loadProjects = async () => {\n        try {\n          projectsLoading.value = true\n          const url = base + \"/projects/list?currentPage=1&pageSize=1000\"\n          const params = {\n\n          }\n\n          const res = await request.post(url, { params })\n\n          if (res.code === 200) {\n            const projects = res.resdata || []\n            availableDatabases.value = [\n              { value: '', text: '请选择数据库' },\n              ...projects.map(project => ({\n                value: project.pid,\n                text: `${project.pno}--${project.daname} (${project.pname})`\n              }))\n            ]\n          } else {\n            ElMessage.error('加载项目列表失败')\n          }\n        } catch (error) {\n          console.error('加载项目列表失败:', error)\n          ElMessage.error('加载项目列表失败，请检查网络连接')\n        } finally {\n          projectsLoading.value = false\n        }\n      }\n\n      // 处理项目选择\n      const handleProjectSelect = async (projectId) => {\n        if (!projectId) return\n\n        try {\n          const url = base + \"/projects/get?id=\" + projectId;\n\n\n          const res = await request.post(url, {})\n\n          if (res.code === 200) {\n            const project = res.resdata\n            // 初始化表单数据\n            projectForm.projectCode = project.pno || ''\n            projectForm.databaseName = project.daname || ''\n            projectForm.name = project.pname || ''\n            projectForm.databaseType = project.dtype || 'mysql'\n            projectForm.backendTemplate = project.by1 || ''\n            projectForm.frontendTemplate = project.by2 || ''\n            projectForm.layer = project.by4 || '否'\n            projectForm.charts = project.by5 || '否'\n            projectForm.schoolName = project.by6 || ''\n\n            // 解析Session信息\n            if (project.by3) {\n              const sessionInfo = project.by3.split(',')\n              projectForm.adminId = sessionInfo[0] || ''\n              projectForm.adminName = sessionInfo[1] || ''\n              projectForm.adminRole = sessionInfo[2] || ''\n              projectForm.adminLoginName = sessionInfo[3] || ''\n            }\n\n            ElMessage.success('项目信息加载成功')\n          } else {\n            ElMessage.error('加载项目详情失败')\n          }\n        } catch (error) {\n          console.error('加载项目详情失败:', error)\n          ElMessage.error('加载项目详情失败，请检查网络连接')\n        }\n      }\n\n      // 保存或更新项目到数据库\n      const saveOrUpdateProject = async () => {\n        try {\n          // 构建Session信息\n          const sessionInfo = [\n            projectForm.adminId,\n            projectForm.adminName,\n            projectForm.adminRole,\n            projectForm.adminLoginName\n          ].join(',')\n\n          const projectData = {\n            ptype: selectedProject.value,\n            dtype: projectForm.databaseType,\n            pflag: projectForm.databaseMode === 'new' ? '1' : '2',\n            pno: projectForm.projectCode,\n            daname: projectForm.databaseName,\n            pname: projectForm.name,\n            by1: projectForm.backendTemplate,\n            by2: projectForm.frontendTemplate,\n            by3: sessionInfo,\n            by4: projectForm.layer,\n            by5: projectForm.charts,\n            by6: projectForm.schoolName,\n            by7: projectForm.copyProject,\n            lname: userInfo.username\n          }\n\n          // 判断是新建还是更新\n          const isUpdate = currentProjectInfo.value && currentProjectInfo.value.pid\n          let url, res\n\n          if (isUpdate) {\n            // 更新项目\n            projectData.pid = currentProjectInfo.value.pid\n            url = base + \"/projects/update\"\n            res = await request.post(url, projectData)\n          } else {\n            // 新建项目\n            url = base + \"/projects/add\"\n            res = await request.post(url, projectData)\n          }\n\n          if (res.code === 200) {\n            const message = isUpdate ? '项目更新成功' : '项目保存成功'\n            ElMessage.success(message)\n\n            // 如果是新建项目，保存返回的项目ID\n            if (!isUpdate && res.resdata && res.resdata.pid) {\n              currentProjectInfo.value = {\n                ...currentProjectInfo.value,\n                pid: res.resdata.pid\n              }\n            }\n\n            return { success: true, projectId: res.resdata?.pid || currentProjectInfo.value?.pid }\n          } else {\n            ElMessage.error(res.msg || '项目保存失败')\n            return { success: false }\n          }\n        } catch (error) {\n          console.error('保存项目失败:', error)\n          ElMessage.error('保存项目失败，请检查网络连接')\n          return { success: false }\n        }\n      }\n\n      // 加载项目表单列表\n      const loadProjectTables = async (projectId) => {\n        try {\n          projectTablesLoading.value = true\n          const url = base + \"/tables/list?currentPage=1&pageSize=1000\"\n          const params = {\n            pid: projectId\n          }\n\n          const res = await request.post(url, params)\n\n          if (res.code === 200) {\n            projectTables.value = res.resdata || []\n\n            // 为每个表加载字段数据\n            for (let table of projectTables.value) {\n              try {\n                const fieldsUrl = base + \"/mores/list?currentPage=1&pageSize=100\"\n                const fieldsRes = await request.post(fieldsUrl, { tid: table.tid })\n\n                if (fieldsRes.code === 200 && fieldsRes.resdata) {\n                  // 将字段数据转换为前端格式\n                  table.fields = fieldsRes.resdata.map(field => ({\n                    id: field.mid,\n                    tid: field.tid,\n                    chineseName: field.mozname,\n                    englishName: field.moname,\n                    type: field.motype,\n                    controlType: field.moflag,\n                    required: field.moyz === '1',\n                    searchable: field.mobt === '1',\n                    visible: field.by1 === '1',\n                    existsCheck: field.by2 === '1',\n                    relatedTable: field.by3 || '', // 关联表\n                    customOptions: field.by4 || '' // 自定义选项\n                  }))\n\n                  // 设置表的生成数据条数\n                  table.generateDataCount = parseInt(table.by1 || '0')\n                } else {\n                  table.fields = []\n                }\n              } catch (error) {\n                console.warn('加载表字段失败:', table.tname, error)\n                table.fields = []\n              }\n            }\n\n            console.log('加载项目表单成功:', projectTables.value)\n          } else {\n            ElMessage.error('加载项目表单失败')\n          }\n        } catch (error) {\n          console.error('加载项目表单失败:', error)\n          ElMessage.error('加载项目表单失败，请检查网络连接')\n        } finally {\n          projectTablesLoading.value = false\n        }\n      }\n\n      // 表设计相关\n      const showTableDesignModal = ref(false)\n      const currentTableTab = ref('table-settings')\n      const currentTableDesign = ref({\n        id: null,\n        chineseName: '',\n        englishName: '',\n        menuOrder: 1,\n        generateData: '0',\n        generateDataCount: 0,\n        functions: {\n          backendAdd: true, backendEdit: true, backendDelete: true,\n          backendDetail: true, backendList: false, batchImport: false,\n          batchExport: false, backendLogin: false, backendRegister: false,\n          backendProfile: false, backendPassword: false,\n          frontendAdd: false, frontendEdit: false, frontendDelete: false,\n          frontendDetail: false, frontendList: false, frontendLogin: false,\n          miniAdd: false, miniEdit: false, miniDelete: false,\n          miniDetail: false, miniList: false\n        },\n        fields: [\n          {\n            id: 1,\n            chineseName: '主键ID',\n            englishName: 'id',\n            type: 'int',\n            controlType: '文本框',\n            required: true,\n            searchable: false,\n            visible: true,\n            existsCheck: false,\n            relatedTable: '',\n            customOptions: ''\n          }\n        ]\n      })\n\n      // 功能选择的响应式数组\n      const backendFunctions = ref([])\n      const frontendFunctions = ref([])\n      const miniFunctions = ref([])\n\n      // 重置字段\n      const resetFields = () => {\n        currentTableDesign.value.fields = [\n          {\n            id: 1,\n            chineseName: '主键ID',\n            englishName: 'id',\n            type: 'int',\n            controlType: '文本框',\n            required: true,\n            searchable: false,\n            visible: true,\n            existsCheck: false\n          }\n        ]\n        ElMessage.success('字段已重置')\n      }\n\n      // AI生成器相关状态\n      const showAiGeneratorDialog = ref(false)\n      const aiGeneratorInput = ref('')\n      const aiGenerationInProgress = ref(false)\n\n      // 模板选择相关状态\n      const showBackendTemplateDialog = ref(false)\n      const showFrontendTemplateDialog = ref(false)\n      const showTemplateDetailDialog = ref(false)\n      const backendTemplates = ref([])\n      const frontendTemplates = ref([])\n      const templatesLoading = ref(false)\n      const currentTemplateDetail = ref(null)\n\n      // 项目生成相关状态\n      const generationInProgress = ref(false)\n      const generationResult = ref(null)\n\n      // SQL脚本生成相关状态\n      const sqlGenerationInProgress = ref(false)\n\n      // 数据脚本生成相关状态\n      const dataGenerationInProgress = ref(false)\n      const dataContent = ref('')\n\n      // 字段设置相关状态\n      const showFieldSettingsDialog = ref(false)\n      const currentFieldSettings = ref(null)\n      const currentFieldIndex = ref(-1)\n      const showInSearchList = ref(false)\n\n      // 关联表选择相关状态\n      const showRelatedTableDialog = ref(false)\n      const availableRelatedTables = ref([])\n\n      // 显示AI生成器弹窗\n      const showAiGeneratorModal = () => {\n        aiGeneratorInput.value = ''\n        showAiGeneratorDialog.value = true\n        nextTick(() => {\n          // 聚焦到输入框\n          const inputElement = document.querySelector('.ai-generator-input')\n          if (inputElement) {\n            inputElement.focus()\n          }\n        })\n      }\n\n      // 隐藏AI生成器弹窗\n      const hideAiGeneratorModal = () => {\n        showAiGeneratorDialog.value = false\n        aiGeneratorInput.value = ''\n      }\n\n      // 确认AI生成\n      const confirmAiGeneration = async () => {\n        const description = aiGeneratorInput.value.trim()\n        if (!description) {\n          ElMessage.warning('请输入描述内容')\n          return\n        }\n\n        // 隐藏弹窗\n        hideAiGeneratorModal()\n\n        // 调用AI生成\n        await doubaoGenerate(description)\n      }\n\n      // 调用豆包AI生成表单\n      const doubaoGenerate = async (str) => {\n        if (aiGenerationInProgress.value) {\n          ElMessage.warning('AI正在生成中，请稍候...')\n          return\n        }\n\n        // 构建prompt\n        const input_text = \"用户:users\\n\" +\n          \"aid|lname|password|role\\n\" +\n          \"用户id|用户名|密码|身份\\n\" +\n          \"int|varchar(50)|varchar(50)|int\\n\" +\n          \"\\n\" +\n          \"学习上面的格式。格式说明如下。\\n\" +\n          \"第1行表中文名称:表英文名称。\\n\" +\n          \"第2行字段列表，字段简写\\n\" +\n          \"第3行字段对应中文\\n\" +\n          \"第4行字段类型。如果是字符型加上长度。\\n\" +\n          \"\\n\" +\n          \"按上面的格式生成下面的内容。不要注释，只返回格式的内容\\n\" + str\n\n        console.log('发送给豆包AI的内容:', input_text)\n\n        const settings = {\n          url: \"https://ark.cn-beijing.volces.com/api/v3/chat/completions\",\n          method: \"POST\",\n          timeout: 30000,\n          headers: {\n            \"Authorization\": \"Bearer 8d71b27a-b4c9-484e-896b-247f7dda5412\",\n            \"Content-Type\": \"application/json\"\n          },\n          data: JSON.stringify({\n            \"model\": \"doubao-1.5-pro-32k-250115\",\n            \"messages\": [\n              {\n                \"role\": \"system\",\n                \"content\": \"你是一个数据库设计专家，专门帮助用户设计数据表结构。请严格按照指定的格式返回结果，不要添加任何额外的说明或注释，表名和字段中不要用下划线，不要大写字母。不要用关键字和保留字\"\n              },\n              {\n                \"role\": \"user\",\n                \"content\": input_text\n              }\n            ]\n          })\n        }\n\n        // 显示生成中状态\n        aiGenerationInProgress.value = true\n        ElMessage.info('正在调用豆包AI生成表结构，请稍候...')\n\n        try {\n          const response = await fetch(settings.url, {\n            method: settings.method,\n            headers: settings.headers,\n            body: settings.data\n          })\n\n          if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`)\n          }\n\n          const result = await response.json()\n          aiGenerationInProgress.value = false\n\n          // 从豆包AI响应中提取内容\n          const generatedContent = result.choices[0].message.content\n          console.log('豆包AI生成的原始内容:', generatedContent)\n\n          // 清理返回内容\n          const cleanedContent = generatedContent\n            .replace(/```[\\s\\S]*?\\n/, '') // 移除开头的markdown代码块标记\n            .replace(/\\n```[\\s\\S]*?$/, '') // 移除结尾的markdown代码块标记\n            .replace(/^\\s+|\\s+$/g, '') // 移除首尾空白\n            .replace(/\\r\\n/g, '\\n') // 统一换行符\n            .replace(/\\r/g, '\\n') // 处理Mac格式换行符\n\n          console.log('清理后的内容:', cleanedContent)\n\n          // 检查内容是否为空\n          if (!cleanedContent || cleanedContent.trim() === '') {\n            throw new Error('豆包AI返回的内容为空')\n          }\n\n          createFormFromAI(cleanedContent)\n          ElMessage.success('AI生成成功！')\n\n        } catch (error) {\n          aiGenerationInProgress.value = false\n          console.error('处理豆包AI响应时出错:', error)\n          ElMessage.error('AI生成失败：' + error.message)\n        }\n      }\n\n      // 解析AI生成的内容并创建表单\n      const createFormFromAI = (content) => {\n        try {\n          console.log('开始解析AI生成的内容:', content)\n\n          // 按行分割内容，移除空行\n          const allLines = content.split('\\n')\n          const lines = allLines.map(line => line.trim()).filter(line => line !== '')\n          console.log('过滤后的有效行:', lines)\n\n          if (lines.length < 4) {\n            throw new Error(`豆包AI返回的格式不完整，需要4行内容，实际只有${lines.length}行`)\n          }\n\n          // 解析第1行：表名\n          const tableNameLine = lines[0]\n          const tableNameMatch = tableNameLine.match(/^(.+):(.+)$/)\n          if (!tableNameMatch) {\n            throw new Error('表名格式不正确，应为：中文名:英文名，实际为：' + tableNameLine)\n          }\n\n          const chineseName = tableNameMatch[1].trim()\n          const englishName = tableNameMatch[2].trim()\n\n          // 解析第2行：英文字段名\n          const englishFields = lines[1].split('|').map(field => field.trim()).filter(field => field !== '')\n\n          // 解析第3行：中文字段名\n          const chineseFields = lines[2].split('|').map(field => field.trim()).filter(field => field !== '')\n\n          // 解析第4行：字段类型\n          const fieldTypes = lines[3].split('|').map(type => type.trim()).filter(type => type !== '')\n\n          // 验证字段数量一致性\n          if (englishFields.length !== chineseFields.length || englishFields.length !== fieldTypes.length) {\n            throw new Error(`字段数量不匹配：英文名${englishFields.length}个，中文名${chineseFields.length}个，类型${fieldTypes.length}个`)\n          }\n\n          // 构建字段数据\n          const fields = []\n          for (let i = 0; i < englishFields.length; i++) {\n            const field = {\n              id: Date.now() + i,\n              chineseName: chineseFields[i],\n              englishName: englishFields[i],\n              type: fieldTypes[i],\n              controlType: inferControlType(fieldTypes[i]),\n              required: true, // AI生成的字段默认必填项选中\n              searchable: i > 0 && i < 3, // 前几个字段设为可搜索\n              visible: true,\n              existsCheck: false\n            }\n            fields.push(field)\n          }\n\n          // 完整替换表结构\n          currentTableDesign.value.chineseName = chineseName\n          currentTableDesign.value.englishName = englishName\n          currentTableDesign.value.fields = fields\n\n          // 确保functions对象存在\n          if (!currentTableDesign.value.functions) {\n            currentTableDesign.value.functions = {}\n          }\n\n          // 默认选中后台功能\n          currentTableDesign.value.functions.backendAdd = true\n          currentTableDesign.value.functions.backendEdit = true\n          currentTableDesign.value.functions.backendDelete = true\n          currentTableDesign.value.functions.backendDetail = true\n          currentTableDesign.value.functions.backendList = false\n\n          console.log('表单创建成功:', {\n            chineseName: chineseName,\n            englishName: englishName,\n            fieldsCount: fields.length\n          })\n\n        } catch (error) {\n          console.error('解析AI内容失败:', error)\n          ElMessage.error('解析AI生成的内容失败：' + error.message)\n        }\n      }\n\n      // 根据字段类型推断控件类型\n      const inferControlType = (fieldType) => {\n        const type = fieldType.toLowerCase()\n\n        if (type.includes('int') || type.includes('bigint')) {\n          return '文本框'\n        } else if (type.includes('decimal') || type.includes('float') || type.includes('double')) {\n          return '文本框'\n        } else if (type.includes('text') || type.includes('longtext')) {\n          return '多行文本'\n        } else if (type.includes('datetime') || type.includes('timestamp')) {\n          return '日期时间'\n        } else if (type.includes('date')) {\n          return '日期选择'\n        } else if (type.includes('varchar') && type.includes('200')) {\n          return '多行文本'\n        } else if (type.includes('varchar')) {\n          return '文本框'\n        } else {\n          return '文本框'\n        }\n      }\n\n      // 模板选择相关函数\n\n      // 显示后台模板选择弹窗\n      const showBackendTemplateSelector = async () => {\n        showBackendTemplateDialog.value = true\n        await loadBackendTemplates()\n      }\n\n      // 显示前台模板选择弹窗\n      const showFrontendTemplateSelector = async () => {\n        showFrontendTemplateDialog.value = true\n        await loadFrontendTemplates()\n      }\n\n      // 加载后台模板\n      const loadBackendTemplates = async () => {\n        try {\n          templatesLoading.value = true\n          const response = await fetch(base+'/small/backend-templates')\n          const result = await response.json()\n          if (result.code === 200) {\n            backendTemplates.value = result.resdata || []\n          } else {\n            ElMessage.error('加载后台模板失败')\n          }\n        } catch (error) {\n          console.error('加载后台模板失败:', error)\n          ElMessage.error('加载后台模板失败')\n        } finally {\n          templatesLoading.value = false\n        }\n      }\n\n      // 加载前台模板\n      const loadFrontendTemplates = async () => {\n        try {\n          templatesLoading.value = true\n          const response = await fetch(base+'/small/frontend-templates')\n          const result = await response.json()\n          if (result.code === 200) {\n            frontendTemplates.value = result.resdata || []\n          } else {\n            ElMessage.error('加载前台模板失败')\n          }\n        } catch (error) {\n          console.error('加载前台模板失败:', error)\n          ElMessage.error('加载前台模板失败')\n        } finally {\n          templatesLoading.value = false\n        }\n      }\n\n      // 选择后台模板\n      const selectBackendTemplate = (template) => {\n        // 设置到项目表单的后台模板字段 - 保存模板ID\n        projectForm.backendTemplate = template.sid\n        showBackendTemplateDialog.value = false\n        ElMessage.success(`已选择后台模板: ${template.sname} (ID: ${template.sid})`)\n      }\n\n      // 选择前台模板\n      const selectFrontendTemplate = (template) => {\n        // 设置到项目表单的前台模板字段 - 保存模板ID\n        projectForm.frontendTemplate = template.sid\n        showFrontendTemplateDialog.value = false\n        ElMessage.success(`已选择前台模板: ${template.sname} (ID: ${template.sid})`)\n      }\n\n      // 查看模板详情\n      const viewTemplateDetail = (template) => {\n        currentTemplateDetail.value = template\n        showTemplateDetailDialog.value = true\n      }\n\n      // 计算属性：是否可以生成项目\n      const canGenerate = computed(() => {\n        return projectForm.name &&\n               projectForm.databaseName &&\n               projectForm.projectCode &&\n               projectTables.value.length > 0 &&\n               !generationInProgress.value\n      })\n\n      // 生成项目\n      const generateProject = async () => {\n        if (!canGenerate.value) {\n          ElMessage.warning('请完善项目配置信息')\n          return\n        }\n\n        try {\n          generationInProgress.value = true\n          generationResult.value = null\n\n          // 构建项目数据\n          const projectData = {\n            projectNumber: projectForm.projectCode,\n            databaseName: projectForm.databaseName,\n            projectName: projectForm.name,\n            packageName: projectForm.packageName || 'com',\n            databaseType: projectForm.databaseType || 'mysql',\n            backendTemplate: projectForm.backendTemplate,\n            frontendTemplate: projectForm.frontendTemplate,\n            tables: projectTables.value.map(table => ({\n              id: table.tid,\n              chineseName: table.tword,\n              englishName: table.tname,\n              functions: table.tgn ? JSON.parse(table.tgn) : {},\n              fields: table.fields || []\n            }))\n          }\n\n          console.log('开始生成项目:', projectData)\n\n          // 调用Java后端API生成项目\n          const response = await fetch(base + '/projects/generate', {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(projectData)\n          })\n\n          const result = await response.json()\n\n          if (result.code === 200) {\n            // 适配后端返回的数据结构\n            const filesData = result.resdata.files && result.resdata.files.data ? result.resdata.files.data : {}\n            const compressionData = result.resdata.compression || null\n\n            generationResult.value = {\n              status: 'success',\n              message: '项目生成成功！',\n              files: filesData,\n              compression: compressionData\n            }\n\n            // 生成项目成功后，自动生成SQL脚本和数据脚本\n            await generateSqlScript()\n            await generateDataScript()\n\n            ElMessage.success('项目生成成功！')\n          } else {\n            throw new Error(result.msg || '项目生成失败')\n          }\n\n        } catch (error) {\n          console.error('项目生成失败:', error)\n          generationResult.value = {\n            status: 'error',\n            message: '项目生成失败：' + error.message,\n            files: null,\n            compression: null\n          }\n          ElMessage.error('项目生成失败：' + error.message)\n        } finally {\n          generationInProgress.value = false\n        }\n      }\n\n      // 生成SQL脚本\n      const generateSqlScript = async () => {\n        if (!currentProjectInfo.value || !projectTables.value || projectTables.value.length === 0) {\n          ElMessage.warning('请先配置项目和数据表')\n          return\n        }\n\n        try {\n          sqlGenerationInProgress.value = true\n\n          // 根据数据库类型生成对应的SQL脚本\n          const databaseType = projectForm.databaseType || 'mysql'\n          let script = ''\n\n          if (databaseType === 'mysql') {\n            script = generateMySqlScript()\n          } else if (databaseType === 'sqlserver') {\n            script = generateSqlServerScript()\n          }\n\n          sqlContent.value = script\n          ElMessage.success('SQL脚本生成成功！')\n\n        } catch (error) {\n          console.error('生成SQL脚本失败:', error)\n          ElMessage.error('生成SQL脚本失败：' + error.message)\n        } finally {\n          sqlGenerationInProgress.value = false\n        }\n      }\n\n      // 生成MySQL脚本\n      const generateMySqlScript = () => {\n        let script = `-- ${projectForm.name || '项目'} 数据库脚本 (MySQL)\\n`\n        script += `-- 数据库名称: ${projectForm.databaseName}\\n`\n        script += `-- 生成时间: ${new Date().toLocaleString()}\\n\\n`\n\n        // 创建数据库\n        script += `CREATE DATABASE IF NOT EXISTS \\`${projectForm.databaseName}\\` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\\n`\n        script += `USE \\`${projectForm.databaseName}\\`;\\n\\n`\n\n        // 为每个表生成建表语句\n        projectTables.value.forEach(table => {\n          script += generateMySqlTableScript(table)\n          script += '\\n'\n        })\n\n        return script\n      }\n\n      // 生成SQL Server脚本\n      const generateSqlServerScript = () => {\n        let script = `-- ${projectForm.name || '项目'} 数据库脚本 (SQL Server)\\n`\n        script += `-- 数据库名称: ${projectForm.databaseName}\\n`\n        script += `-- 生成时间: ${new Date().toLocaleString()}\\n\\n`\n\n        // 创建数据库\n        script += `IF NOT EXISTS (SELECT * FROM sys.databases WHERE name = '${projectForm.databaseName}')\\n`\n        script += `CREATE DATABASE [${projectForm.databaseName}];\\n`\n        script += `GO\\n\\n`\n        script += `USE [${projectForm.databaseName}];\\n`\n        script += `GO\\n\\n`\n\n        // 为每个表生成建表语句\n        projectTables.value.forEach(table => {\n          script += generateSqlServerTableScript(table)\n          script += '\\n'\n        })\n\n        return script\n      }\n\n      // 生成MySQL表脚本\n      const generateMySqlTableScript = (table) => {\n        let script = `-- 表: ${table.tword || table.tname}\\n`\n        script += `DROP TABLE IF EXISTS \\`${table.tname}\\`;\\n`\n        script += `CREATE TABLE \\`${table.tname}\\` (\\n`\n\n        const fields = table.fields || []\n        const fieldScripts = fields.map((field, index) => {\n          let fieldScript = `  \\`${field.englishName}\\` ${convertToMySqlType(field.type)}`\n\n          // 主键处理 - 修复第一个字段的AUTO_INCREMENT问题\n          if (field.englishName.toLowerCase() === 'id' || index === 0) {\n            // 只有int类型的字段才能使用AUTO_INCREMENT\n            if (field.type && field.type.toLowerCase() === 'int') {\n              fieldScript += ' AUTO_INCREMENT NOT NULL'\n            } else {\n              fieldScript += ' NOT NULL'\n            }\n          } else if (field.required) {\n            fieldScript += ' NOT NULL'\n          }\n\n          // 注释\n          if (field.chineseName) {\n            fieldScript += ` COMMENT '${field.chineseName}'`\n          }\n\n          return fieldScript\n        })\n\n        script += fieldScripts.join(',\\n')\n\n        // 主键约束\n        if (fields.length > 0) {\n          const primaryKey = fields[0].englishName\n          script += `,\\n  PRIMARY KEY (\\`${primaryKey}\\`)`\n        }\n\n        script += `\\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='${table.tword || table.tname}';\\n\\n`\n\n        return script\n      }\n\n      // 生成SQL Server表脚本\n      const generateSqlServerTableScript = (table) => {\n        let script = `-- 表: ${table.tword || table.tname}\\n`\n        script += `IF OBJECT_ID('[${table.tname}]', 'U') IS NOT NULL DROP TABLE [${table.tname}];\\n`\n        script += `CREATE TABLE [${table.tname}] (\\n`\n\n        const fields = table.fields || []\n        const fieldScripts = fields.map((field, index) => {\n          let fieldScript = `  [${field.englishName}] ${convertToSqlServerType(field.type)}`\n\n          // 主键处理\n          if (field.englishName.toLowerCase() === 'id' || index === 0) {\n            fieldScript += ' IDENTITY(1,1)'\n          }\n\n          // 非空约束\n          if (field.required) {\n            fieldScript += ' NOT NULL'\n          }\n\n          return fieldScript\n        })\n\n        script += fieldScripts.join(',\\n')\n\n        // 主键约束\n        if (fields.length > 0) {\n          const primaryKey = fields[0].englishName\n          script += `,\\n  CONSTRAINT [PK_${table.tname}] PRIMARY KEY ([${primaryKey}])`\n        }\n\n        script += `\\n);\\n`\n\n        // 添加表注释\n        if (table.tword) {\n          script += `EXEC sp_addextendedproperty 'MS_Description', '${table.tword}', 'SCHEMA', 'dbo', 'TABLE', '${table.tname}';\\n`\n        }\n\n        // 添加字段注释\n        fields.forEach(field => {\n          if (field.chineseName) {\n            script += `EXEC sp_addextendedproperty 'MS_Description', '${field.chineseName}', 'SCHEMA', 'dbo', 'TABLE', '${table.tname}', 'COLUMN', '${field.englishName}';\\n`\n          }\n        })\n\n        script += `GO\\n\\n`\n\n        return script\n      }\n\n      // 转换为MySQL数据类型\n      const convertToMySqlType = (type) => {\n        if (!type) return 'VARCHAR(100)'\n\n        const lowerType = type.toLowerCase()\n        if (lowerType === 'int') return 'INT'\n        if (lowerType.includes('varchar')) return type.toUpperCase()\n        if (lowerType === 'text') return 'TEXT'\n        if (lowerType === 'datetime') return 'DATETIME'\n        if (lowerType.includes('decimal')) return type.toUpperCase()\n\n        return type.toUpperCase()\n      }\n\n      // 转换为SQL Server数据类型\n      const convertToSqlServerType = (type) => {\n        if (!type) return 'NVARCHAR(100)'\n\n        const lowerType = type.toLowerCase()\n        if (lowerType === 'int') return 'INT'\n        if (lowerType.includes('varchar')) {\n          // 将varchar转换为nvarchar\n          return type.replace(/varchar/i, 'NVARCHAR')\n        }\n        if (lowerType === 'text') return 'NTEXT'\n        if (lowerType === 'datetime') return 'DATETIME'\n        if (lowerType.includes('decimal')) return type.toUpperCase()\n\n        return type.toUpperCase()\n      }\n\n      // 导出SQL脚本\n      const exportSqlScript = () => {\n        if (!sqlContent.value || sqlContent.value.trim() === '') {\n          ElMessage.warning('请先生成SQL脚本')\n          return\n        }\n\n        try {\n          const blob = new Blob([sqlContent.value], { type: 'text/plain;charset=utf-8' })\n          const url = window.URL.createObjectURL(blob)\n          const link = document.createElement('a')\n          link.href = url\n\n          const databaseType = projectForm.databaseType || 'mysql'\n          const fileName = `${projectForm.databaseName || 'database'}_${databaseType}.sql`\n          link.download = fileName\n\n          document.body.appendChild(link)\n          link.click()\n          document.body.removeChild(link)\n          window.URL.revokeObjectURL(url)\n\n          ElMessage.success('SQL脚本导出成功！')\n        } catch (error) {\n          console.error('导出SQL脚本失败:', error)\n          ElMessage.error('导出SQL脚本失败：' + error.message)\n        }\n      }\n\n      // 复制SQL脚本到剪切板\n      const copySqlScript = async () => {\n        if (!sqlContent.value || sqlContent.value.trim() === '') {\n          ElMessage.warning('请先生成SQL脚本')\n          return\n        }\n\n        try {\n          await navigator.clipboard.writeText(sqlContent.value)\n          ElMessage.success('SQL脚本已复制到剪切板！')\n        } catch (error) {\n          console.error('复制到剪切板失败:', error)\n          // 降级方案：使用传统的复制方法\n          try {\n            const textArea = document.createElement('textarea')\n            textArea.value = sqlContent.value\n            document.body.appendChild(textArea)\n            textArea.select()\n            document.execCommand('copy')\n            document.body.removeChild(textArea)\n            ElMessage.success('SQL脚本已复制到剪切板！')\n          } catch (fallbackError) {\n            console.error('降级复制方法也失败:', fallbackError)\n            ElMessage.error('复制到剪切板失败，请手动复制')\n          }\n        }\n      }\n\n      // 生成数据脚本\n      const generateDataScript = async () => {\n        if (!currentProjectInfo.value || !projectTables.value || projectTables.value.length === 0) {\n          ElMessage.warning('请先配置项目和数据表')\n          return\n        }\n\n        try {\n          dataGenerationInProgress.value = true\n\n          // 筛选出需要生成数据的表（生成数据条数大于0）\n          const tablesWithData = projectTables.value.filter(table => {\n            const dataCount = parseInt(table.by1 || '0')\n            return dataCount > 0\n          })\n\n          if (tablesWithData.length === 0) {\n            ElMessage.warning('没有设置生成数据条数的表，请先在表设计中设置生成数据条数')\n            return\n          }\n\n          let script = ''\n\n          // 为每个需要生成数据的表生成建表语句和插入数据\n          for (const table of tablesWithData) {\n            script += generateTableWithDataScript(table)\n            script += '\\n'\n          }\n\n          // 添加生成要求说明\n          script += `项目名称是：${projectForm.name || '智慧社区网格化管理系统'}\\n`\n          script += `按要求生成的条数，生成上面所有的数据，数据内容要多一些，数据模拟真实的数据\\n`\n          script += `如果有密码，密码为123456。\\n`\n          script += `时间字段为当前时间\\n`\n          script += `生成的数据为中文，只生成insert into数据，不要注释说明\\n`\n\n          dataContent.value = script\n          ElMessage.success('数据脚本生成成功！')\n\n        } catch (error) {\n          console.error('生成数据脚本失败:', error)\n          ElMessage.error('生成数据脚本失败：' + error.message)\n        } finally {\n          dataGenerationInProgress.value = false\n        }\n      }\n\n      // 生成单个表的建表语句和数据\n      const generateTableWithDataScript = (table) => {\n        const fields = table.fields || []\n        const dataCount = parseInt(table.by1 || '0')\n\n        let script = `create table if NOT EXISTS ${table.tname} \\n(\\n`\n\n        // 生成字段定义\n        const fieldScripts = fields.map((field, index) => {\n          let fieldScript = `${field.englishName}   ${convertToMySqlType(field.type)}`\n\n          // 主键处理 - 修复第一个字段的AUTO_INCREMENT问题\n          if (field.englishName.toLowerCase() === 'id' || index === 0) {\n            // 只有int类型的字段才能使用AUTO_INCREMENT\n            if (field.type && field.type.toLowerCase() === 'int') {\n              fieldScript += ' auto_increment  primary key'\n            } else {\n              fieldScript += ' not null    primary key'\n            }\n          } else if (field.englishName.toLowerCase().includes('account') ||\n                     field.englishName.toLowerCase().includes('username')) {\n            fieldScript += ' not null    primary key'\n          } else if (field.required) {\n            fieldScript += ' not null   '\n          } else {\n            fieldScript += '  null   '\n          }\n\n          // 注释\n          if (field.chineseName) {\n            fieldScript += ` comment '${field.chineseName}'`\n          }\n\n          return fieldScript\n        })\n\n        script += fieldScripts.join(' ,\\n') + ' \\n'\n        script += `) ENGINE=MyISAM AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;\\n\\n`\n\n        // 添加生成数据条数说明\n        if (dataCount > 0) {\n          script += `生成${dataCount}条insert into数据\\n\\n`\n        }\n\n        return script\n      }\n\n      // 复制数据脚本到剪切板\n      const copyDataScript = async () => {\n        if (!dataContent.value || dataContent.value.trim() === '') {\n          ElMessage.warning('请先生成数据脚本')\n          return\n        }\n\n        try {\n          await navigator.clipboard.writeText(dataContent.value)\n          ElMessage.success('数据脚本已复制到剪切板！')\n        } catch (error) {\n          console.error('复制到剪切板失败:', error)\n          // 降级方案：使用传统的复制方法\n          try {\n            const textArea = document.createElement('textarea')\n            textArea.value = dataContent.value\n            document.body.appendChild(textArea)\n            textArea.select()\n            document.execCommand('copy')\n            document.body.removeChild(textArea)\n            ElMessage.success('数据脚本已复制到剪切板！')\n          } catch (fallbackError) {\n            console.error('降级复制方法也失败:', fallbackError)\n            ElMessage.error('复制到剪切板失败，请手动复制')\n          }\n        }\n      }\n\n      // 下载项目文件\n      const downloadProject = (filePath) => {\n        if (filePath) {\n          // 从filePath中提取文件名\n          const fileName = filePath.split('/').pop()\n          // 使用新的下载接口\n          const downloadUrl = `${base}/projects/download?fileName=${encodeURIComponent(fileName)}`\n          window.open(downloadUrl, '_blank')\n        } else {\n          ElMessage.error('下载链接无效')\n        }\n      }\n\n      // 获取模板图片URL\n      const getTemplateImageUrl = (memo4) => {\n        if (!memo4) return ''\n\n        // 从HTML中提取图片URL\n        const imgMatch = memo4.match(/<img[^>]+src=\"([^\"]+)\"/)\n        if (imgMatch && imgMatch[1]) {\n          return imgMatch[1]\n        }\n\n        return ''\n      }\n\n      // 处理图片加载错误\n      const handleImageError = (event) => {\n        event.target.style.display = 'none'\n        const parent = event.target.parentElement\n        if (parent) {\n          parent.innerHTML = '<div class=\"no-image\"><span>图片加载失败</span></div>'\n        }\n      }\n\n      // 获取表格字段数量\n      const getTableFieldCount = (table) => {\n        // 如果有字段数据，返回字段数量\n        if (table.fields && Array.isArray(table.fields)) {\n          return table.fields.length\n        }\n        // 否则返回0\n        return 0\n      }\n\n      // 获取表格功能列表\n      const getTableFunctions = (table) => {\n        if (!table.tgn) return []\n\n        try {\n          const functions = JSON.parse(table.tgn)\n          const activeFunctions = []\n\n          // 检查各种功能是否启用\n          if (functions.backendAdd) activeFunctions.push('后台添加')\n          if (functions.backendEdit) activeFunctions.push('后台修改')\n          if (functions.backendDelete) activeFunctions.push('后台删除')\n          if (functions.backendDetail) activeFunctions.push('后台详情')\n          if (functions.backendList) activeFunctions.push('后台列表')\n          if (functions.frontendAdd) activeFunctions.push('前台添加')\n          if (functions.frontendEdit) activeFunctions.push('前台修改')\n          if (functions.frontendDelete) activeFunctions.push('前台删除')\n          if (functions.miniAdd) activeFunctions.push('小程序添加')\n\n          return activeFunctions.slice(0, 3) // 只显示前3个功能\n        } catch (error) {\n          console.error('解析表功能配置失败:', error)\n          return []\n        }\n      }\n\n      // 打开表设计弹窗\n      const openTableDesignModal = (table = null) => {\n        if (table) {\n          // 编辑现有表\n          currentTableDesign.value = {\n            id: table.tid,\n            chineseName: table.tword || '',\n            englishName: table.tname || '',\n            menuOrder: parseInt(table.by2 || '1'),\n            generateData: '0',\n            generateDataCount: parseInt(table.by1 || '0'),\n            functions: table.tgn ? JSON.parse(table.tgn) : {\n              backendAdd: true, backendEdit: true, backendDelete: true,\n              backendDetail: true, backendList: false, batchImport: false,\n              batchExport: false, backendLogin: false, backendRegister: false,\n              backendProfile: false, backendPassword: false,\n              frontendAdd: false, frontendEdit: false, frontendDelete: false,\n              frontendDetail: false, frontendList: false, frontendLogin: false,\n              miniAdd: false, miniEdit: false, miniDelete: false,\n              miniDetail: false, miniList: false\n            },\n            fields: table.fields || [\n              {\n                id: 1,\n                chineseName: '主键ID',\n                englishName: 'id',\n                type: 'int',\n                controlType: '文本框',\n                required: true,\n                searchable: false,\n                visible: true,\n                existsCheck: false\n              }\n            ]\n          }\n        } else {\n          // 创建新表\n          resetTableDesign()\n        }\n        showTableDesignModal.value = true\n      }\n\n      // 重置表设计数据\n      const resetTableDesign = () => {\n        currentTableDesign.value = {\n          id: null,\n          chineseName: '',\n          englishName: '',\n          menuOrder: 1,\n          generateData: '0',\n          generateDataCount: 0,\n          functions: {\n            backendAdd: true, backendEdit: true, backendDelete: true,\n            backendDetail: true, backendList: false, batchImport: false,\n            batchExport: false, backendLogin: false, backendRegister: false,\n            backendProfile: false, backendPassword: false,\n            frontendAdd: false, frontendEdit: false, frontendDelete: false,\n            frontendDetail: false, frontendList: false, frontendLogin: false,\n            miniAdd: false, miniEdit: false, miniDelete: false,\n            miniDetail: false, miniList: false\n          },\n          fields: [\n            {\n              id: 1,\n              chineseName: '主键ID',\n              englishName: 'id',\n              type: 'int',\n              controlType: '文本框',\n              required: true,\n              searchable: false,\n              visible: true,\n              existsCheck: false\n            }\n          ]\n        }\n      }\n\n      // 关闭表设计弹窗\n      const closeTableDesignModal = () => {\n        showTableDesignModal.value = false\n        currentTableTab.value = 'table-settings'\n      }\n\n      // 表设计弹窗Tab切换功能\n      const switchTableTab = (tabId) => {\n        currentTableTab.value = tabId\n      }\n\n      // 添加字段到设计中\n      const addNewFieldToDesign = () => {\n        const newField = {\n          id: Date.now(),\n          chineseName: '',\n          englishName: '',\n          type: 'varchar(100)',\n          controlType: '文本框',\n          required: true, // 默认必填项选中\n          searchable: false,\n          visible: true,\n          existsCheck: false,\n          relatedTable: '', // 关联表\n          customOptions: '' // 自定义选项\n        }\n        currentTableDesign.value.fields.push(newField)\n      }\n\n      // 清空所有字段\n      const clearAllFields = async () => {\n        try {\n          await ElMessageBox.confirm(\n            '确定要清空所有字段吗？此操作不可撤销。',\n            '清空字段',\n            {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              type: 'warning',\n            }\n          )\n\n          currentTableDesign.value.fields = []\n          ElMessage.success('字段清空成功')\n        } catch (error) {\n          if (error !== 'cancel') {\n            console.error('清空字段失败:', error)\n          }\n        }\n      }\n\n      // 删除设计中的字段\n      const deleteFieldFromDesign = async (index) => {\n        try {\n          await ElMessageBox.confirm(\n            '确定要删除这个字段吗？此操作不可撤销。',\n            '删除字段',\n            {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              type: 'warning',\n            }\n          )\n\n          currentTableDesign.value.fields.splice(index, 1)\n          ElMessage.success('字段删除成功')\n        } catch (error) {\n          if (error !== 'cancel') {\n            console.error('删除字段失败:', error)\n          }\n        }\n      }\n\n      // 上移字段\n      const moveFieldUp = (index) => {\n        if (index > 0) {\n          const field = currentTableDesign.value.fields.splice(index, 1)[0]\n          currentTableDesign.value.fields.splice(index - 1, 0, field)\n        }\n      }\n\n      // 下移字段\n      const moveFieldDown = (index) => {\n        if (index < currentTableDesign.value.fields.length - 1) {\n          const field = currentTableDesign.value.fields.splice(index, 1)[0]\n          currentTableDesign.value.fields.splice(index + 1, 0, field)\n        }\n      }\n\n      // 编辑字段设置\n      const editFieldSettings = (field, index) => {\n        currentFieldSettings.value = { ...field }\n        currentFieldIndex.value = index\n        showInSearchList.value = field.searchable || false\n        showFieldSettingsDialog.value = true\n      }\n\n      // 关闭字段设置弹窗\n      const closeFieldSettingsDialog = () => {\n        showFieldSettingsDialog.value = false\n        currentFieldSettings.value = null\n        currentFieldIndex.value = -1\n      }\n\n      // 保存字段设置\n      const saveFieldSettings = () => {\n        if (currentFieldIndex.value >= 0 && currentFieldSettings.value) {\n          // 更新字段数据\n          const field = currentTableDesign.value.fields[currentFieldIndex.value]\n          field.relatedTable = currentFieldSettings.value.relatedTable\n          field.customOptions = currentFieldSettings.value.customOptions\n          field.searchable = showInSearchList.value\n\n          ElMessage.success('字段设置保存成功')\n          closeFieldSettingsDialog()\n        }\n      }\n\n      // 显示关联表选择器\n      const showRelatedTableSelector = async () => {\n        if (!currentProjectInfo.value || !currentProjectInfo.value.pid) {\n          ElMessage.warning('请先保存项目信息')\n          return\n        }\n\n        try {\n          // 加载项目的所有表\n          await loadAvailableRelatedTables()\n          showRelatedTableDialog.value = true\n        } catch (error) {\n          console.error('加载关联表失败:', error)\n          ElMessage.error('加载关联表失败')\n        }\n      }\n\n      // 加载可用的关联表\n      const loadAvailableRelatedTables = async () => {\n        try {\n          const url = base + \"/tables/list?currentPage=1&pageSize=100\"\n          const res = await request.post(url, { pid: currentProjectInfo.value.pid })\n\n          if (res.code === 200 && res.resdata) {\n            availableRelatedTables.value = res.resdata.map(table => {\n              // 获取表的第二个字段作为显示名称，如果第二个字段是密码则使用第三个字段\n              let displayName = table.tword || table.tname\n              if (table.fields && table.fields.length > 1) {\n                const secondField = table.fields[1]\n                if (secondField && secondField.chineseName && !secondField.chineseName.includes('密码')) {\n                  displayName = secondField.chineseName\n                } else if (table.fields.length > 2) {\n                  const thirdField = table.fields[2]\n                  if (thirdField && thirdField.chineseName) {\n                    displayName = thirdField.chineseName\n                  }\n                }\n              }\n\n              return {\n                primaryKey: table.fields && table.fields.length > 0 ? table.fields[0].englishName : 'id',\n                displayName: displayName,\n                tableName: table.tname,\n                linkValue: '1',\n                selected: false\n              }\n            })\n          }\n        } catch (error) {\n          console.error('加载关联表失败:', error)\n          throw error\n        }\n      }\n\n      // 关闭关联表选择弹窗\n      const closeRelatedTableDialog = () => {\n        showRelatedTableDialog.value = false\n        availableRelatedTables.value = []\n      }\n\n      // 确认关联表选择\n      const confirmRelatedTableSelection = () => {\n        const selectedTables = availableRelatedTables.value.filter(table => table.selected)\n        if (selectedTables.length === 0) {\n          ElMessage.warning('请至少选择一个关联表')\n          return\n        }\n\n        // 构建关联表字符串，格式：doro,dbid,dormitory,1\n        const relatedTableStr = selectedTables.map(table =>\n          `${table.primaryKey},${table.displayName},${table.tableName},${table.linkValue}`\n        ).join(';')\n\n        if (currentFieldSettings.value) {\n          currentFieldSettings.value.relatedTable = relatedTableStr\n        }\n\n        closeRelatedTableDialog()\n        ElMessage.success('关联表设置成功')\n      }\n\n      // 保存表设计\n      const saveTableDesign = async () => {\n        // 验证必填字段\n        if (!currentTableDesign.value.chineseName.trim()) {\n          ElMessage.warning('请输入表的中文名称')\n          return\n        }\n\n        if (!currentTableDesign.value.englishName.trim()) {\n          ElMessage.warning('请输入表的英文名称')\n          return\n        }\n\n        // 验证字段\n        for (let field of currentTableDesign.value.fields) {\n          if (!field.chineseName.trim() || !field.englishName.trim()) {\n            ElMessage.warning('请完善所有字段的中文名称和英文名称')\n            return\n          }\n        }\n\n        // 确保有项目ID\n        if (!currentProjectInfo.value || !currentProjectInfo.value.pid) {\n          ElMessage.error('项目信息缺失，请重新配置项目')\n          return\n        }\n\n        try {\n          // 准备表数据\n          const tableData = {\n            pid: currentProjectInfo.value.pid,\n            tword: currentTableDesign.value.chineseName,\n            tname: currentTableDesign.value.englishName,\n            tgn: JSON.stringify(currentTableDesign.value.functions),\n            tlist: '',\n            vlist: '',\n            by1: (currentTableDesign.value.generateDataCount || 0).toString(),\n            by2: (currentTableDesign.value.menuOrder || 1).toString()\n          }\n\n          // 如果是编辑模式，添加tid\n          if (currentTableDesign.value.id) {\n            tableData.tid = currentTableDesign.value.id\n          }\n\n          // 保存表信息\n          const isUpdate = currentTableDesign.value.id\n          const url = base + (isUpdate ? \"/tables/update\" : \"/tables/add\")\n          const res = await request.post(url, tableData)\n\n          if (res.code === 200) {\n            let tableId = currentTableDesign.value.id\n            if (!isUpdate && res.resdata) {\n              // 后端返回的是新创建的表ID\n              tableId = res.resdata\n              currentTableDesign.value.id = tableId\n            }\n\n            // 如果有字段，保存字段信息\n            if (currentTableDesign.value.fields.length > 0) {\n              // 先删除原有字段（如果是更新模式）\n              if (isUpdate) {\n                try {\n                  await request.post(base + \"/mores/deleteByTid\", { tid: tableId })\n                } catch (error) {\n                  console.warn('删除原有字段失败:', error)\n                }\n              }\n\n              // 保存新字段\n              for (let field of currentTableDesign.value.fields) {\n                const fieldData = {\n                  tid: tableId,\n                  moname: field.englishName,\n                  mozname: field.chineseName,\n                  motype: field.type,\n                  moflag: field.controlType,\n                  molong: '',\n                  moyz: field.required ? '1' : '0',\n                  mobt: field.searchable ? '1' : '0',\n                  by1: field.visible ? '1' : '0',\n                  by2: field.existsCheck ? '1' : '0',\n                  by3: field.relatedTable || '',\n                  by4: field.customOptions || '',\n                  by5: '',\n                  by6: ''\n                }\n\n                try {\n                  await request.post(base + \"/mores/add\", fieldData)\n                } catch (error) {\n                  console.error('保存字段失败:', field, error)\n                }\n              }\n            }\n\n            ElMessage.success('表设计保存成功')\n            closeTableDesignModal()\n            // 重新加载项目表单列表\n            if (currentProjectInfo.value.pid) {\n              loadProjectTables(currentProjectInfo.value.pid)\n            }\n          } else {\n            ElMessage.error(res.msg || '表保存失败')\n          }\n        } catch (error) {\n          console.error('保存表设计失败:', error)\n          ElMessage.error('保存表设计失败，请检查网络连接')\n        }\n      }\n\n      // 删除表\n      const deleteTable = async (table) => {\n        try {\n          await ElMessageBox.confirm(\n            `确定要删除表 \"${table.tword || table.tname}\" 吗？`,\n            '确认删除',\n            {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              type: 'warning',\n            }\n          )\n\n          const url = base + \"/tables/del?id=\" + table.tid\n          const res = await request.post(url, {})\n\n          if (res.code === 200) {\n            ElMessage.success('删除成功')\n            // 重新加载表单列表\n            if (currentProjectInfo.value && currentProjectInfo.value.pid) {\n              loadProjectTables(currentProjectInfo.value.pid)\n            }\n          } else {\n            ElMessage.error(res.msg || '删除失败')\n          }\n        } catch (error) {\n          if (error !== 'cancel') {\n            console.error('删除表失败:', error)\n            ElMessage.error('删除失败，请检查网络连接')\n          }\n        }\n      }\n\n      // 下一步操作\n      const nextStep = async () => {\n        if (activeTab.value === 'project') {\n          // 从项目配置到表单设计\n          if (!selectedProject.value) {\n            ElMessage.warning('请先选择项目类型')\n            return\n          }\n          if (!projectForm.name) {\n            ElMessage.warning('请输入项目中文名称')\n            return\n          }\n\n          // 验证必填字段\n          if (projectForm.databaseMode === 'new') {\n            if (!projectForm.projectCode) {\n              ElMessage.warning('请输入项目编号')\n              return\n            }\n            if (!projectForm.databaseName) {\n              ElMessage.warning('请输入数据库名称')\n              return\n            }\n          } else if (projectForm.databaseMode === 'existing') {\n            if (!projectForm.selectedDatabase) {\n              ElMessage.warning('请选择已有数据库')\n              return\n            }\n          }\n\n          // 设置当前项目信息\n          currentProjectInfo.value = {\n            name: projectForm.name,\n            projectCode: projectForm.projectCode,\n            databaseName: projectForm.databaseName,\n            pid: projectForm.selectedDatabase || null\n          }\n\n          // 保存或更新项目到数据库\n          const result = await saveOrUpdateProject()\n          if (!result.success) return\n\n          // 更新项目ID\n          if (result.projectId) {\n            currentProjectInfo.value.pid = result.projectId\n          }\n\n          // 如果是已有数据库模式，加载项目表单\n          if (projectForm.databaseMode === 'existing' && projectForm.selectedDatabase) {\n            loadProjectTables(projectForm.selectedDatabase)\n          } else if (currentProjectInfo.value.pid) {\n            // 新建项目也加载表单（可能为空）\n            loadProjectTables(currentProjectInfo.value.pid)\n          }\n\n          activeTab.value = 'form'\n          ElMessage.success('项目配置保存成功，请继续设计表单')\n        } else if (activeTab.value === 'form') {\n          // 从表单设计到项目生成\n          if (!projectTables.value || projectTables.value.length === 0) {\n            ElMessage.warning('请先设计数据表，不能为空')\n            return\n          }\n          activeTab.value = 'generate'\n          ElMessage.success('表单设计完成，请生成项目')\n        }\n      }\n\n      const handleTabClick = (tab) => {\n        console.log('切换到标签页:', tab.props.name)\n      }\n\n      // 组件挂载时检查登录状态\n      onMounted(() => {\n        checkLoginStatus()\n      })\n\n      return {\n        activeTab,\n        logLevel,\n        isLoggedIn,\n        loginDialogVisible,\n        loginLoading,\n        loginFormRef,\n        userInfo,\n        loginForm,\n        loginRules,\n        selectedProject,\n        projectsLoading,\n        availableDatabases,\n        projectForm,\n        currentProjectInfo,\n        projectTables,\n        projectTablesLoading,\n        showTableDesignModal,\n        currentTableTab,\n        currentTableDesign,\n        formFields,\n        tableData,\n        sqlContent,\n        logs,\n        handleTabClick,\n        handleLogin,\n        handleUserCommand,\n        handleLogout,\n        selectProject,\n        getProjectName,\n        openTemplateModal,\n        openFrontTemplateModal,\n        handleDatabaseModeChange,\n        handleProjectSelect,\n        loadProjectTables,\n        openTableDesignModal,\n        closeTableDesignModal,\n        switchTableTab,\n        addNewFieldToDesign,\n        clearAllFields,\n        deleteFieldFromDesign,\n        moveFieldUp,\n        moveFieldDown,\n        saveTableDesign,\n        deleteTable,\n        nextStep,\n        getTableFieldCount,\n        getTableFunctions,\n        backendFunctions,\n        frontendFunctions,\n        miniFunctions,\n        resetFields,\n        showAiGeneratorModal,\n\n        // AI生成器相关\n        showAiGeneratorDialog,\n        aiGeneratorInput,\n        aiGenerationInProgress,\n        hideAiGeneratorModal,\n        confirmAiGeneration,\n        doubaoGenerate,\n        createFormFromAI,\n        inferControlType,\n\n        // 模板选择相关\n        showBackendTemplateDialog,\n        showFrontendTemplateDialog,\n        showTemplateDetailDialog,\n        backendTemplates,\n        frontendTemplates,\n        templatesLoading,\n        currentTemplateDetail,\n        showBackendTemplateSelector,\n        showFrontendTemplateSelector,\n        selectBackendTemplate,\n        selectFrontendTemplate,\n        viewTemplateDetail,\n        getTemplateImageUrl,\n        handleImageError,\n\n        // 项目生成相关\n        generationInProgress,\n        generationResult,\n        canGenerate,\n        generateProject,\n        downloadProject,\n\n        // SQL脚本生成相关\n        sqlGenerationInProgress,\n        generateSqlScript,\n        exportSqlScript,\n        copySqlScript,\n\n        // 数据脚本生成相关\n        dataGenerationInProgress,\n        dataContent,\n        generateDataScript,\n        copyDataScript,\n\n        // 字段设置相关\n        showFieldSettingsDialog,\n        currentFieldSettings,\n        showInSearchList,\n        editFieldSettings,\n        closeFieldSettingsDialog,\n        saveFieldSettings,\n        showRelatedTableSelector,\n\n        // 关联表选择相关\n        showRelatedTableDialog,\n        availableRelatedTables,\n        closeRelatedTableDialog,\n        confirmRelatedTableSelection\n      }\n    }\n  }\n</script>"], "mappings": ";;EACOA,KAAK,EAAC;AAA0B;;;EAEZA,KAAK,EAAC;;;EAEnBA,KAAK,EAAC;AAAmB;;EAkB9BA,KAAK,EAAC;AAAc;;EAKXA,KAAK,EAAC;AAAW;;EAOpBA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAgB;;EACrBA,KAAK,EAAC;AAAY;;EAWrBA,KAAK,EAAC;AAAc;;EAOlBA,KAAK,EAAC;AAAe;;;EA4BAA,KAAK,EAAC;;;EAC3BA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAc;;;EAqDVA,KAAK,EAAC;;;EAmEdA,KAAK,EAAC;AAAc;;EAavBA,KAAK,EAAC;AAAW;;EAOpBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAc;;;EAOQA,KAAK,EAAC;;;EAC9BA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAAiB;;EACpBA,KAAK,EAAC;AAAqB;;EAG3BA,KAAK,EAAC;AAAqB;;EAG3BA,KAAK,EAAC;AAAqB;;EAQlCA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAmB;;;EAOGA,KAAK,EAAC;;;;EAOKA,KAAK,EAAC;;;;EAMtCA,KAAK,EAAC;;;;EAGTA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAAoB;;EAM9BA,KAAK,EAAC;AAAwB;;;EAG5BA,KAAK,EAAC;;;EAOZA,KAAK,EAAC,cAAc;EAACC,KAAyB,EAAzB;IAAA;EAAA;;;EAetBD,KAAK,EAAC;AAAW;;EAOpBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAc;;EAOlBA,KAAK,EAAC;AAAoB;;EAIpBA,KAAK,EAAC;AAAc;;EAEjBA,KAAK,EAAC;AAAe;;EAIxBA,KAAK,EAAC;AAAc;;EAEjBA,KAAK,EAAC;AAAe;;EAIxBA,KAAK,EAAC;AAAc;;EAEjBA,KAAK,EAAC;AAAe;;EAMxBA,KAAK,EAAC;AAAc;;EAEjBA,KAAK,EAAC;AAAe;;EAIxBA,KAAK,EAAC;AAAc;;EAEjBA,KAAK,EAAC;AAAe;;EAIxBA,KAAK,EAAC;AAAc;;EAEjBA,KAAK,EAAC;AAAe;;EAO9BA,KAAK,EAAC;AAAoB;;;EAiBIA,KAAK,EAAC;;;;EAOZA,KAAK,EAAC;;;EASzBA,KAAK,EAAC;AAAa;;;EAIQA,KAAK,EAAC;;;;EAIuDA,KAAK,EAAC;;;EAG5FA,KAAK,EAAC;AAAmB;;EAGnBA,KAAK,EAAC;AAAW;;;EAOiEA,KAAK,EAAC;;;EAG9FA,KAAK,EAAC;AAAiB;;EAGjBA,KAAK,EAAC;AAAW;;EACjBA,KAAK,EAAC;AAAY;;;EAQOA,KAAK,EAAC;;;EAOrCA,KAAK,EAAC;AAAkB;;;EACgBA,KAAK,EAAC;;;EAkBtDA,KAAK,EAAC;AAAW;;EAOpBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAc;;EAKlBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAc;;EAIpBA,KAAK,EAAC;AAAa;;EAYtBA,KAAK,EAAC;AAAW;;EAOpBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAc;;EAKlBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAa;;EAKnBA,KAAK,EAAC;AAAY;;EAYrBA,KAAK,EAAC;AAAW;;EAOpBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAc;;EAKlBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAc;;EAUpBA,KAAK,EAAC;AAAc;;EAEhBA,KAAK,EAAC;AAAU;;EAChBA,KAAK,EAAC;AAAW;;EACjBA,KAAK,EAAC;AAAa;;EAsB9BA,KAAK,EAAC;AAAe;;EAYxBA,KAAK,EAAC;AAAwB;;EAE5BA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAS;;EAkBfA,KAAK,EAAC;AAAqB;;EAEqBA,KAAK,EAAC;AAAa;;EAE/DA,KAAK,EAAC;AAAoB;;EA0C1BA,KAAK,EAAC;AAA4B;;EAK5BA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAqB;;EAkB7BA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAqB;;EAa7BA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAqB;;EAcOA,KAAK,EAAC;AAAa;;EAE7DA,KAAK,EAAC;AAAuB;;EA4B7BA,KAAK,EAAC;AAAqB;;EA4EnBA,KAAK,EAAC;AAAe;;EAkCnCA,KAAK,EAAC;AAAe;;EAsBvBA,KAAK,EAAC;AAAwB;;EAM5BA,KAAK,EAAC;AAAmB;;EAyBzBA,KAAK,EAAC;AAAqB;;EAe7BA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAoB;;;;;EAKbA,KAAK,EAAC;;;EAOfA,KAAK,EAAC;AAAe;;;EAErBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAkB;;EAiBhCA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAoB;;;;;EAKbA,KAAK,EAAC;;;EAOfA,KAAK,EAAC;AAAe;;;EAErBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAkB;;;EAiBhCA,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAwB;;EAE9BA,KAAK,EAAC;AAAoB;;EAE1BA,KAAK,EAAC;AAAiC;;;EACFA,KAAK,EAAC;;;;EAWlCA,KAAK,EAAC;;;EAOfA,KAAK,EAAC;AAAsB;;EAa5BA,KAAK,EAAC;AAAwB;;;EAShCA,KAAK,EAAC;;;EAkCJA,KAAK,EAAC;AAAuB;;EAU/BA,KAAK,EAAC;AAAyB;;EAmB7BA,KAAK,EAAC;AAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAxkCvCE,mBAAA,CA8kCM,OA9kCNC,UA8kCM,GA7kCJC,mBAAA,cAAiB,EACNC,MAAA,CAAAC,UAAU,I,cAArBJ,mBAAA,CAiBM,OAjBNK,UAiBM,GAhBJC,YAAA,CAecC,sBAAA;IAfAC,SAAO,EAAEL,MAAA,CAAAM;EAAiB;IAU3BC,QAAQ,EAAAC,QAAA,CACjB,MAEmB,CAFnBL,YAAA,CAEmBM,2BAAA;wBADjB,MAA0D,CAA1DN,YAAA,CAA0DO,2BAAA;QAAxCC,OAAO,EAAC;MAAQ;0BAAC,MAAIC,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;;;;sBAX3C,MAQO,CARPC,mBAAA,CAQO,QARPC,UAQO,GAPLX,YAAA,CAEUY,kBAAA;wBADR,MAAQ,CAARZ,YAAA,CAAQa,eAAA,E;;yBACA,GACV,GAAAC,gBAAA,CAAGjB,MAAA,CAAAkB,QAAQ,CAACC,QAAQ,IAAG,GACvB,iBAAAhB,YAAA,CAEUY,kBAAA;MAFDpB,KAAK,EAAC;IAAgB;wBAC7B,MAAa,CAAbQ,YAAA,CAAaiB,oBAAA,E;;;;2EAWrBrB,mBAAA,YAAe,EACfc,mBAAA,CA8hBM,OA9hBNQ,UA8hBM,GA7hBJlB,YAAA,CA4hBUmB,kBAAA;gBA5hBQtB,MAAA,CAAAuB,SAAS;iEAATvB,MAAA,CAAAuB,SAAS,GAAAC,MAAA;IAAEC,IAAI,EAAC,MAAM;IAAC9B,KAAK,EAAC,gBAAgB;IAAE+B,UAAS,EAAE1B,MAAA,CAAA2B;;sBAC1E,MAAa,CAAb5B,mBAAA,UAAa,EACbI,YAAA,CAgMcyB,sBAAA;MAhMDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;MAClBD,KAAK,EAAArB,QAAA,CACd,MAKO,CALPK,mBAAA,CAKO,QALPkB,UAKO,GAJL5B,YAAA,CAEUY,kBAAA;0BADR,MAAU,CAAVZ,YAAA,CAAU6B,iBAAA,E;;uDACF,QAEZ,G;wBAEF,MAsLM,CAtLNnB,mBAAA,CAsLM,OAtLNoB,UAsLM,GArLJlC,mBAAA,UAAa,EACbc,mBAAA,CAUM,OAVNqB,UAUM,GATJrB,mBAAA,CAQM,OARNsB,UAQM,GAPJtB,mBAAA,CAKK,MALLuB,UAKK,GAJHjC,YAAA,CAEUY,kBAAA;QAFDpB,KAAK,EAAC;MAAY;0BACzB,MAAW,CAAXQ,YAAA,CAAWkC,kBAAA,E;;uDACH,SAEZ,G,+BACAxB,mBAAA,CAAiD;QAA9ClB,KAAK,EAAC;MAAkB,GAAC,mBAAiB,qB,KAIjDI,mBAAA,YAAe,EACfc,mBAAA,CAgCM,OAhCNyB,WAgCM,G,4BA/BJzB,mBAAA,CAGM;QAHDlB,KAAK,EAAC;MAAa,IACtBkB,mBAAA,CAAgB,YAAZ,SAAO,GACXA,mBAAA,CAAyB,WAAtB,oBAAkB,E,sBAGvBd,mBAAA,YAAe,EACfc,mBAAA,CAwBM,OAxBN0B,WAwBM,GAvBJ1B,mBAAA,CAIM;QAJDlB,KAAK,EAAA6C,eAAA,EAAC,cAAc;UAAAC,MAAA,EACLzC,MAAA,CAAA0C,eAAe;QAAA;QADRC,OAAK,EAAA/B,MAAA,QAAAA,MAAA,MAAAY,MAAA,IAAExB,MAAA,CAAA4C,aAAa;sCAE7C/B,mBAAA,CAAkC,YAA9B,2BAAyB,qBAC7BA,mBAAA,CAA2C,WAAxC,sCAAoC,oB,mBAGzCA,mBAAA,CAIM;QAJDlB,KAAK,EAAA6C,eAAA,EAAC,cAAc;UAAAC,MAAA,EACLzC,MAAA,CAAA0C,eAAe;QAAA;QADRC,OAAK,EAAA/B,MAAA,QAAAA,MAAA,MAAAY,MAAA,IAAExB,MAAA,CAAA4C,aAAa;sCAE7C/B,mBAAA,CAA4B,YAAxB,qBAAmB,qBACvBA,mBAAA,CAAoC,WAAjC,+BAA6B,oB,mBAGlCA,mBAAA,CAIM;QAJDlB,KAAK,EAAA6C,eAAA,EAAC,cAAc;UAAAC,MAAA,EACLzC,MAAA,CAAA0C,eAAe;QAAA;QADRC,OAAK,EAAA/B,MAAA,QAAAA,MAAA,MAAAY,MAAA,IAAExB,MAAA,CAAA4C,aAAa;sCAE7C/B,mBAAA,CAA2B,YAAvB,oBAAkB,qBACtBA,mBAAA,CAA4C,WAAzC,uCAAqC,oB,mBAG1CA,mBAAA,CAIM;QAJDlB,KAAK,EAAA6C,eAAA,EAAC,cAAc;UAAAC,MAAA,EACLzC,MAAA,CAAA0C,eAAe;QAAA;QADRC,OAAK,EAAA/B,MAAA,QAAAA,MAAA,MAAAY,MAAA,IAAExB,MAAA,CAAA4C,aAAa;sCAE7C/B,mBAAA,CAAqB,YAAjB,cAAY,qBAChBA,mBAAA,CAAuD,WAApD,kDAAgD,oB,uBAKzDd,mBAAA,YAAe,EACJC,MAAA,CAAA0C,eAAe,I,cAA1B7C,mBAAA,CAmIM,OAnINgD,WAmIM,GAlIJhC,mBAAA,CAGM,OAHNiC,WAGM,GAFJjC,mBAAA,CAAqD,YAAjD,SAAO,GAAAI,gBAAA,CAAGjB,MAAA,CAAA+C,cAAc,CAAC/C,MAAA,CAAA0C,eAAe,mB,4BAC5C7B,mBAAA,CAAoB,WAAjB,eAAa,qB,GAElBA,mBAAA,CA6HM,OA7HNmC,WA6HM,GA5HJ7C,YAAA,CAqHU8C,kBAAA;QArHAC,KAAK,EAAElD,MAAA,CAAAmD,WAAW;QAAE,aAAW,EAAC,OAAO;QAACxD,KAAK,EAAC;;0BACtD,MAuBS,CAvBTQ,YAAA,CAuBSiD,iBAAA;UAvBAC,MAAM,EAAE;QAAE;4BACjB,MAOS,CAPTlD,YAAA,CAOSmD,iBAAA;YAPAC,IAAI,EAAE;UAAC;8BACd,MAKe,CALfpD,YAAA,CAKeqD,uBAAA;cALD3B,KAAK,EAAC;YAAO;gCACzB,MAGY,CAHZ1B,YAAA,CAGYsD,oBAAA;4BAHQzD,MAAA,CAAAmD,WAAW,CAACO,YAAY;2EAAxB1D,MAAA,CAAAmD,WAAW,CAACO,YAAY,GAAAlC,MAAA;gBAAEmC,WAAW,EAAC,UAAU;gBAAC/D,KAAmB,EAAnB;kBAAA;gBAAA;;kCACnE,MAAyC,CAAzCO,YAAA,CAAyCyD,oBAAA;kBAA9B/B,KAAK,EAAC,OAAO;kBAACgC,KAAK,EAAC;oBAC/B1D,YAAA,CAAkDyD,oBAAA;kBAAvC/B,KAAK,EAAC,YAAY;kBAACgC,KAAK,EAAC;;;;;;;cAI1C1D,YAAA,CAQSmD,iBAAA;YARAC,IAAI,EAAE;UAAC;8BACd,MAMe,CANfpD,YAAA,CAMeqD,uBAAA;cAND3B,KAAK,EAAC;YAAO;gCACzB,MAIY,CAJZ1B,YAAA,CAIYsD,oBAAA;4BAJQzD,MAAA,CAAAmD,WAAW,CAACW,YAAY;2EAAxB9D,MAAA,CAAAmD,WAAW,CAACW,YAAY,GAAAtC,MAAA;gBAAEmC,WAAW,EAAC,UAAU;gBAAC/D,KAAmB,EAAnB;kBAAA;gBAAA,CAAmB;gBACrFmE,QAAM,EAAE/D,MAAA,CAAAgE;;kCACT,MAAuC,CAAvC7D,YAAA,CAAuCyD,oBAAA;kBAA5B/B,KAAK,EAAC,OAAO;kBAACgC,KAAK,EAAC;oBAC/B1D,YAAA,CAA4CyD,oBAAA;kBAAjC/B,KAAK,EAAC,OAAO;kBAACgC,KAAK,EAAC;;;;;;;cAIrC1D,YAAA,CAISmD,iBAAA;YAJAC,IAAI,EAAE;UAAC;8BACd,MAEe,CAFfpD,YAAA,CAEeqD,uBAAA;cAFD3B,KAAK,EAAC;YAAQ;gCAC1B,MAA+D,CAA/D1B,YAAA,CAA+D8D,mBAAA;4BAA5CjE,MAAA,CAAAmD,WAAW,CAACrB,IAAI;2EAAhB9B,MAAA,CAAAmD,WAAW,CAACrB,IAAI,GAAAN,MAAA;gBAAEmC,WAAW,EAAC;;;;;;;YAK5B3D,MAAA,CAAAmD,WAAW,CAACW,YAAY,c,cAAnDI,YAAA,CAgBSd,iBAAA;;UAhBAC,MAAM,EAAE;;4BACf,MAIS,CAJTlD,YAAA,CAISmD,iBAAA;YAJAC,IAAI,EAAE;UAAC;8BACd,MAEe,CAFfpD,YAAA,CAEeqD,uBAAA;cAFD3B,KAAK,EAAC;YAAM;gCACxB,MAAiE,CAAjE1B,YAAA,CAAiE8D,mBAAA;4BAA9CjE,MAAA,CAAAmD,WAAW,CAACgB,WAAW;2EAAvBnE,MAAA,CAAAmD,WAAW,CAACgB,WAAW,GAAA3C,MAAA;gBAAEmC,WAAW,EAAC;;;;;cAG5DxD,YAAA,CAISmD,iBAAA;YAJAC,IAAI,EAAE;UAAC;8BACd,MAEe,CAFfpD,YAAA,CAEeqD,uBAAA;cAFD3B,KAAK,EAAC;YAAO;gCACzB,MAAsE,CAAtE1B,YAAA,CAAsE8D,mBAAA;4BAAnDjE,MAAA,CAAAmD,WAAW,CAACiB,YAAY;2EAAxBpE,MAAA,CAAAmD,WAAW,CAACiB,YAAY,GAAA5C,MAAA;gBAAEmC,WAAW,EAAC;;;;;cAG7DxD,YAAA,CAISmD,iBAAA;YAJAC,IAAI,EAAE;UAAC;8BACd,MAEe,CAFfpD,YAAA,CAEeqD,uBAAA;cAFD3B,KAAK,EAAC;YAAM;gCACxB,MAAmE,CAAnE1B,YAAA,CAAmE8D,mBAAA;4BAAhDjE,MAAA,CAAAmD,WAAW,CAACkB,UAAU;2EAAtBrE,MAAA,CAAAmD,WAAW,CAACkB,UAAU,GAAA7C,MAAA;gBAAEmC,WAAW,EAAC;;;;;;;iDAKlC3D,MAAA,CAAAmD,WAAW,CAACW,YAAY,mB,cAAnDI,YAAA,CAaSd,iBAAA;;UAbAC,MAAM,EAAE;;4BACf,MAWS,CAXTlD,YAAA,CAWSmD,iBAAA;YAXAC,IAAI,EAAE;UAAE;8BACf,MASe,CATfpD,YAAA,CASeqD,uBAAA;cATD3B,KAAK,EAAC;YAAO;gCACzB,MAIY,CAJZ1B,YAAA,CAIYsD,oBAAA;4BAJQzD,MAAA,CAAAmD,WAAW,CAACmB,gBAAgB;6EAA5BtE,MAAA,CAAAmD,WAAW,CAACmB,gBAAgB,GAAA9C,MAAA;gBAAEmC,WAAW,EAAC,QAAQ;gBAAC/D,KAAmB,EAAnB;kBAAA;gBAAA,CAAmB;gBACvFmE,QAAM,EAAE/D,MAAA,CAAAuE,mBAAmB;gBAAGC,OAAO,EAAExE,MAAA,CAAAyE;;kCAC7B,MAAgC,E,kBAA3C5E,mBAAA,CAC0B6E,SAAA,QAAAC,WAAA,CADF3E,MAAA,CAAA4E,kBAAkB,EAAxBC,EAAE;uCAApBX,YAAA,CAC0BN,oBAAA;oBADmBkB,GAAG,EAAED,EAAE,CAAChB,KAAK;oBAAGhC,KAAK,EAAEgD,EAAE,CAACE,IAAI;oBAAGlB,KAAK,EAAEgB,EAAE,CAAChB,KAAK;oBAC1FmB,QAAQ,GAAGH,EAAE,CAAChB;;;;wEAEU7D,MAAA,CAAA4E,kBAAkB,CAACK,MAAM,Q,cAAtDpF,mBAAA,CAEM,OAFNqF,WAEM,EAFsD,2BAE5D,K;;;;;;iDAKN/E,YAAA,CAkBSiD,iBAAA;UAlBAC,MAAM,EAAE;QAAE;4BACjB,MAKS,CALTlD,YAAA,CAKSmD,iBAAA;YALAC,IAAI,EAAE;UAAC;8BACd,MAGe,CAHfpD,YAAA,CAGeqD,uBAAA;cAHD3B,KAAK,EAAC;YAAM;gCACxB,MAC+B,CAD/B1B,YAAA,CAC+B8D,mBAAA;4BADZjE,MAAA,CAAAmD,WAAW,CAACgC,eAAe;6EAA3BnF,MAAA,CAAAmD,WAAW,CAACgC,eAAe,GAAA3D,MAAA;gBAAEmC,WAAW,EAAC,UAAU;gBAACyB,QAAQ,EAAR,EAAQ;gBAC5EzC,OAAK,EAAE3C,MAAA,CAAAqF;;;;;cAGdlF,YAAA,CAKSmD,iBAAA;YALAC,IAAI,EAAE;UAAC;8BACd,MAGe,CAHfpD,YAAA,CAGeqD,uBAAA;cAHD3B,KAAK,EAAC;YAAM;gCACxB,MACoC,CADpC1B,YAAA,CACoC8D,mBAAA;4BADjBjE,MAAA,CAAAmD,WAAW,CAACmC,gBAAgB;6EAA5BtF,MAAA,CAAAmD,WAAW,CAACmC,gBAAgB,GAAA9D,MAAA;gBAAEmC,WAAW,EAAC,UAAU;gBAACyB,QAAQ,EAAR,EAAQ;gBAC7EzC,OAAK,EAAE3C,MAAA,CAAAuF;;;;;cAGdpF,YAAA,CAISmD,iBAAA;YAJAC,IAAI,EAAE;UAAC;8BACd,MAEe,CAFfpD,YAAA,CAEeqD,uBAAA;cAFD3B,KAAK,EAAC;YAAM;gCACxB,MAAsE,CAAtE1B,YAAA,CAAsE8D,mBAAA;4BAAnDjE,MAAA,CAAAmD,WAAW,CAACqC,WAAW;6EAAvBxF,MAAA,CAAAmD,WAAW,CAACqC,WAAW,GAAAhE,MAAA;gBAAEmC,WAAW,EAAC;;;;;;;YAK9DxD,YAAA,CAoBSiD,iBAAA;UApBAC,MAAM,EAAE;QAAE;4BACjB,MAOS,CAPTlD,YAAA,CAOSmD,iBAAA;YAPAC,IAAI,EAAE;UAAC;8BACd,MAKe,CALfpD,YAAA,CAKeqD,uBAAA;cALD3B,KAAK,EAAC;YAAU;gCAC5B,MAGY,CAHZ1B,YAAA,CAGYsD,oBAAA;4BAHQzD,MAAA,CAAAmD,WAAW,CAACsC,KAAK;6EAAjBzF,MAAA,CAAAmD,WAAW,CAACsC,KAAK,GAAAjE,MAAA;gBAAE5B,KAAmB,EAAnB;kBAAA;gBAAA;;kCACrC,MAAiC,CAAjCO,YAAA,CAAiCyD,oBAAA;kBAAtB/B,KAAK,EAAC,GAAG;kBAACgC,KAAK,EAAC;oBAC3B1D,YAAA,CAAiCyD,oBAAA;kBAAtB/B,KAAK,EAAC,GAAG;kBAACgC,KAAK,EAAC;;;;;;;cAIjC1D,YAAA,CAOSmD,iBAAA;YAPAC,IAAI,EAAE;UAAC;8BACd,MAKe,CALfpD,YAAA,CAKeqD,uBAAA;cALD3B,KAAK,EAAC;YAAM;gCACxB,MAGY,CAHZ1B,YAAA,CAGYsD,oBAAA;4BAHQzD,MAAA,CAAAmD,WAAW,CAACuC,MAAM;6EAAlB1F,MAAA,CAAAmD,WAAW,CAACuC,MAAM,GAAAlE,MAAA;gBAAE5B,KAAmB,EAAnB;kBAAA;gBAAA;;kCACtC,MAAiC,CAAjCO,YAAA,CAAiCyD,oBAAA;kBAAtB/B,KAAK,EAAC,GAAG;kBAACgC,KAAK,EAAC;oBAC3B1D,YAAA,CAAiCyD,oBAAA;kBAAtB/B,KAAK,EAAC,GAAG;kBAACgC,KAAK,EAAC;;;;;;;cAIjC1D,YAAA,CAESmD,iBAAA;YAFAC,IAAI,EAAE;UAAC;8BACd,MAAkB,CAAlBxD,mBAAA,eAAkB,C;;;;YAItBI,YAAA,CAeeqD,uBAAA;UAfD3B,KAAK,EAAC;QAAW;4BAC7B,MAaS,CAbT1B,YAAA,CAaSiD,iBAAA;YAbAC,MAAM,EAAE;UAAE;8BACjB,MAES,CAFTlD,YAAA,CAESmD,iBAAA;cAFAC,IAAI,EAAE;YAAC;gCACd,MAA8D,CAA9DpD,YAAA,CAA8D8D,mBAAA;4BAA3CjE,MAAA,CAAAmD,WAAW,CAACwC,OAAO;6EAAnB3F,MAAA,CAAAmD,WAAW,CAACwC,OAAO,GAAAnE,MAAA;gBAAEmC,WAAW,EAAC;;;gBAEtDxD,YAAA,CAESmD,iBAAA;cAFAC,IAAI,EAAE;YAAC;gCACd,MAAgE,CAAhEpD,YAAA,CAAgE8D,mBAAA;4BAA7CjE,MAAA,CAAAmD,WAAW,CAACyC,SAAS;6EAArB5F,MAAA,CAAAmD,WAAW,CAACyC,SAAS,GAAApE,MAAA;gBAAEmC,WAAW,EAAC;;;gBAExDxD,YAAA,CAESmD,iBAAA;cAFAC,IAAI,EAAE;YAAC;gCACd,MAAgE,CAAhEpD,YAAA,CAAgE8D,mBAAA;4BAA7CjE,MAAA,CAAAmD,WAAW,CAAC0C,SAAS;6EAArB7F,MAAA,CAAAmD,WAAW,CAAC0C,SAAS,GAAArE,MAAA;gBAAEmC,WAAW,EAAC;;;gBAExDxD,YAAA,CAESmD,iBAAA;cAFAC,IAAI,EAAE;YAAC;gCACd,MAAmE,CAAnEpD,YAAA,CAAmE8D,mBAAA;4BAAhDjE,MAAA,CAAAmD,WAAW,CAAC2C,cAAc;6EAA1B9F,MAAA,CAAAmD,WAAW,CAAC2C,cAAc,GAAAtE,MAAA;gBAAEmC,WAAW,EAAC;;;;;;;;;oCAMnE9C,mBAAA,CAIM,OAJNkF,WAIM,GAHJ5F,YAAA,CAEY6F,oBAAA;QAFDvE,IAAI,EAAC,SAAS;QAAEkB,OAAK,EAAE3C,MAAA,CAAAiG,QAAQ;QAAEC,IAAI,EAAC;;0BAAQ,MAEzDtF,MAAA,SAAAA,MAAA,Q,iBAFyD,eAEzD,E;;;;;QAOVb,mBAAA,UAAa,EACbI,YAAA,CAuFcyB,sBAAA;MAvFDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;MAClBD,KAAK,EAAArB,QAAA,CACd,MAKO,CALPK,mBAAA,CAKO,QALPsF,WAKO,GAJLhG,YAAA,CAEUY,kBAAA;0BADR,MAAQ,CAARZ,YAAA,CAAQiG,eAAA,E;;uDACA,QAEZ,G;wBAEF,MA6EM,CA7ENvF,mBAAA,CA6EM,OA7ENwF,WA6EM,GA5EJxF,mBAAA,CA2EM,OA3ENyF,WA2EM,G,4BA1EJzF,mBAAA,CAGM;QAHDlB,KAAK,EAAC;MAAa,IACtBkB,mBAAA,CAAc,YAAV,OAAK,GACTA,mBAAA,CAAsB,WAAnB,iBAAe,E,sBAGpBd,mBAAA,YAAe,EACJC,MAAA,CAAAuG,kBAAkB,I,cAA7B1G,mBAAA,CAeM,OAfN2G,WAeM,GAdJ3F,mBAAA,CAaM,OAbN4F,WAaM,GAZJ5F,mBAAA,CAA2C,YAAvC,OAAK,GAAAI,gBAAA,CAAGjB,MAAA,CAAAuG,kBAAkB,CAACzE,IAAI,kBACnCjB,mBAAA,CAUM,OAVN6F,WAUM,GATJ7F,mBAAA,CAEO,QAFP8F,WAEO,G,4BADL9F,mBAAA,CAAsB,gBAAd,OAAK,sB,kCAAYb,MAAA,CAAAuG,kBAAkB,CAACpC,WAAW,iB,GAEzDtD,mBAAA,CAEO,QAFP+F,WAEO,G,4BADL/F,mBAAA,CAAqB,gBAAb,MAAI,sB,kCAAYb,MAAA,CAAAuG,kBAAkB,CAACnC,YAAY,iB,GAEzDvD,mBAAA,CAEO,QAFPgG,WAEO,G,4BADLhG,mBAAA,CAAoB,gBAAZ,KAAG,sB,kCAAYb,MAAA,CAAA+C,cAAc,CAAC/C,MAAA,CAAA0C,eAAe,kB,8CAM7D3C,mBAAA,UAAa,EACbc,mBAAA,CAwCM,OAxCNiG,WAwCM,GAvCJjG,mBAAA,CAKM,OALNkG,WAKM,G,4BAJJlG,mBAAA,CAAc,YAAV,OAAK,sBACTV,YAAA,CAEY6F,oBAAA;QAFDvE,IAAI,EAAC,SAAS;QAAEuF,IAAI,EAAEC,IAAA,CAAAC,IAAI;QAAGvE,OAAK,EAAA/B,MAAA,SAAAA,MAAA,OAAAY,MAAA,IAAExB,MAAA,CAAAmH,oBAAoB;;0BAAI,MAEvEvG,MAAA,SAAAA,MAAA,Q,iBAFuE,QAEvE,E;;;qCAGSZ,MAAA,CAAAoH,oBAAoB,I,cAA/BvH,mBAAA,CAKM,OALNwH,WAKM,GAJJlH,YAAA,CAEUY,kBAAA;QAFDpB,KAAK,EAAC;MAAY;0BACzB,MAAW,CAAXQ,YAAA,CAAWmH,kBAAA,E;;sCAEbzG,mBAAA,CAAwB,cAAlB,aAAW,qB,KAGHb,MAAA,CAAAuH,aAAa,CAACtC,MAAM,U,cAApCpF,mBAAA,CAIM,OAJN2H,WAIM,GAHJrH,YAAA,CAEWsH,mBAAA;QAFDC,WAAW,EAAC;MAAO;0BAC3B,MAA4E,CAA5EvH,YAAA,CAA4E6F,oBAAA;UAAjEvE,IAAI,EAAC,SAAS;UAAEkB,OAAK,EAAA/B,MAAA,SAAAA,MAAA,OAAAY,MAAA,IAAExB,MAAA,CAAAmH,oBAAoB;;4BAAI,MAAMvG,MAAA,SAAAA,MAAA,Q,iBAAN,QAAM,E;;;;;6BAIpEf,mBAAA,CAkBM,OAlBN8H,WAkBM,I,kBAjBJ9H,mBAAA,CAgBM6E,SAAA,QAAAC,WAAA,CAhBkC3E,MAAA,CAAAuH,aAAa,EAAtBK,KAAK;6BAApC/H,mBAAA,CAgBM;UAhBDF,KAAK,EAAC,YAAY;UAAiCmF,GAAG,EAAE8C,KAAK,CAACC,GAAG;UACnElF,OAAK,EAAAnB,MAAA,IAAExB,MAAA,CAAAmH,oBAAoB,CAACS,KAAK;YAClC/G,mBAAA,CAOM,OAPNiH,WAOM,GANJjH,mBAAA,CAAyC,YAAAI,gBAAA,CAAlC2G,KAAK,CAACG,KAAK,IAAIH,KAAK,CAACI,KAAK,kBACjCnH,mBAAA,CAIM,OAJNoH,WAIM,GAHJ9H,YAAA,CAA6F6F,oBAAA;UAAlFE,IAAI,EAAC,OAAO;UAAEc,IAAI,EAAEC,IAAA,CAAAiB,IAAI;UAAGvF,OAAK,EAAAwF,cAAA,CAAA3G,MAAA,IAAOxB,MAAA,CAAAmH,oBAAoB,CAACS,KAAK;;4BAAG,MAAE,KAAAhH,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;kEACjFT,YAAA,CACiD6F,oBAAA;UADtCE,IAAI,EAAC,OAAO;UAACzE,IAAI,EAAC,QAAQ;UAAEuF,IAAI,EAAEC,IAAA,CAAAmB,MAAM;UAChDzF,OAAK,EAAAwF,cAAA,CAAA3G,MAAA,IAAOxB,MAAA,CAAAqI,WAAW,CAACT,KAAK;;4BAAG,MAAE,KAAAhH,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;sEAGzCC,mBAAA,CAEI,KAFJyH,WAEI,EAAArH,gBAAA,CADC2G,KAAK,CAACI,KAAK,IAAG,IAAE,GAAA/G,gBAAA,CAAGjB,MAAA,CAAAuI,kBAAkB,CAACX,KAAK,KAAI,OACpD,iBACwCA,KAAK,CAACY,GAAG,I,cAAjD3I,mBAAA,CAEM,OAFN4I,WAEM,I,kBADJ5I,mBAAA,CAAiG6E,SAAA,QAAAC,WAAA,CAAvD3E,MAAA,CAAA0I,iBAAiB,CAACd,KAAK,GAA/Be,IAAI;+BAAtC9I,mBAAA,CAAiG;YAA3FF,KAAK,EAAC,cAAc;YAA2CmF,GAAG,EAAE6D;8BAASA,IAAI;;2CAM/F9H,mBAAA,CAOM,OAPN+H,WAOM,GANJzI,YAAA,CAEY6F,oBAAA;QAFArD,OAAK,EAAA/B,MAAA,SAAAA,MAAA,OAAAY,MAAA,IAAExB,MAAA,CAAAuB,SAAS;QAAc2E,IAAI,EAAC;;0BAAQ,MAEvDtF,MAAA,SAAAA,MAAA,Q,iBAFuD,cAEvD,E;;;UACAT,YAAA,CAEY6F,oBAAA;QAFDvE,IAAI,EAAC,SAAS;QAAEkB,OAAK,EAAE3C,MAAA,CAAAiG,QAAQ;QAAEC,IAAI,EAAC;;0BAAQ,MAEzDtF,MAAA,SAAAA,MAAA,Q,iBAFyD,cAEzD,E;;;;;QAMRb,mBAAA,UAAa,EACbI,YAAA,CAwJcyB,sBAAA;MAxJDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;MAClBD,KAAK,EAAArB,QAAA,CACd,MAKO,CALPK,mBAAA,CAKO,QALPgI,WAKO,GAJL1I,YAAA,CAEUY,kBAAA;0BADR,MAAO,CAAPZ,YAAA,CAAO2I,cAAA,E;;uDACC,QAEZ,G;wBAEF,MA8IM,CA9INjI,mBAAA,CA8IM,OA9INkI,WA8IM,GA7IJlI,mBAAA,CA4IM,OA5INmI,WA4IM,G,8BA3IJnI,mBAAA,CAGM;QAHDlB,KAAK,EAAC;MAAa,IACtBkB,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAoB,WAAjB,eAAa,E,sBAGlBd,mBAAA,YAAe,EACfc,mBAAA,CA0CM,OA1CNoI,WA0CM,G,8BAzCJpI,mBAAA,CAAe,YAAX,QAAM,sBACVV,YAAA,CAmBSiD,iBAAA;QAnBAC,MAAM,EAAE;MAAE;0BACjB,MAKS,CALTlD,YAAA,CAKSmD,iBAAA;UALAC,IAAI,EAAE;QAAC;4BACd,MAGM,CAHN1C,mBAAA,CAGM,OAHNqI,WAGM,G,8BAFJrI,mBAAA,CAAwC;YAAlClB,KAAK,EAAC;UAAe,GAAC,OAAK,sBACjCkB,mBAAA,CAAkE,QAAlEsI,WAAkE,EAAAlI,gBAAA,CAAnCjB,MAAA,CAAAmD,WAAW,CAACrB,IAAI,0B;;YAGnD3B,YAAA,CAKSmD,iBAAA;UALAC,IAAI,EAAE;QAAC;4BACd,MAGM,CAHN1C,mBAAA,CAGM,OAHNuI,WAGM,G,8BAFJvI,mBAAA,CAAyC;YAAnClB,KAAK,EAAC;UAAe,GAAC,QAAM,sBAClCkB,mBAAA,CAA0E,QAA1EwI,WAA0E,EAAApI,gBAAA,CAA3CjB,MAAA,CAAAmD,WAAW,CAACiB,YAAY,0B;;YAG3DjE,YAAA,CAKSmD,iBAAA;UALAC,IAAI,EAAE;QAAC;4BACd,MAGM,CAHN1C,mBAAA,CAGM,OAHNyI,WAGM,G,8BAFJzI,mBAAA,CAAwC;YAAlClB,KAAK,EAAC;UAAe,GAAC,OAAK,sBACjCkB,mBAAA,CAAyE,QAAzE0I,WAAyE,EAAAtI,gBAAA,CAA1CjB,MAAA,CAAAmD,WAAW,CAACgB,WAAW,0B;;;;UAI5DhE,YAAA,CAmBSiD,iBAAA;QAnBAC,MAAM,EAAE,EAAE;QAAEzD,KAAyB,EAAzB;UAAA;QAAA;;0BACnB,MAKS,CALTO,YAAA,CAKSmD,iBAAA;UALAC,IAAI,EAAE;QAAC;4BACd,MAGM,CAHN1C,mBAAA,CAGM,OAHN2I,WAGM,G,8BAFJ3I,mBAAA,CAAyC;YAAnClB,KAAK,EAAC;UAAe,GAAC,QAAM,sBAClCkB,mBAAA,CAA+D,QAA/D4I,WAA+D,EAAAxI,gBAAA,CAAhCjB,MAAA,CAAAuH,aAAa,CAACtC,MAAM,IAAG,IAAE,gB;;YAG5D9E,YAAA,CAKSmD,iBAAA;UALAC,IAAI,EAAE;QAAC;4BACd,MAGM,CAHN1C,mBAAA,CAGM,OAHN6I,WAGM,G,8BAFJ7I,mBAAA,CAAwC;YAAlClB,KAAK,EAAC;UAAe,GAAC,OAAK,sBACjCkB,mBAAA,CAA6E,QAA7E8I,WAA6E,EAAA1I,gBAAA,CAA9CjB,MAAA,CAAAmD,WAAW,CAACgC,eAAe,0B;;YAG9DhF,YAAA,CAKSmD,iBAAA;UALAC,IAAI,EAAE;QAAC;4BACd,MAGM,CAHN1C,mBAAA,CAGM,OAHN+I,WAGM,G,8BAFJ/I,mBAAA,CAAwC;YAAlClB,KAAK,EAAC;UAAe,GAAC,OAAK,sBACjCkB,mBAAA,CAA8E,QAA9EgJ,WAA8E,EAAA5I,gBAAA,CAA/CjB,MAAA,CAAAmD,WAAW,CAACmC,gBAAgB,0B;;;;YAMnEvF,mBAAA,YAAe,EACfc,mBAAA,CAqBM,OArBNiJ,WAqBM,GApBJ3J,YAAA,CAaY6F,oBAAA;QAZVvE,IAAI,EAAC,SAAS;QACdyE,IAAI,EAAC,OAAO;QACXvD,OAAK,EAAE3C,MAAA,CAAA+J,eAAe;QACtBvF,OAAO,EAAExE,MAAA,CAAAgK,oBAAoB;QAC7BhF,QAAQ,GAAGhF,MAAA,CAAAiK,WAAW,IAAIjK,MAAA,CAAAgK;;0BAC3B,MAEU,C,CAFMhK,MAAA,CAAAgK,oBAAoB,I,cAApC9F,YAAA,CAEUnD,kBAAA;UAAA+D,GAAA;QAAA;4BADR,MAAO,CAAP3E,YAAA,CAAO2I,cAAA,E;;6BAET5E,YAAA,CAEUnD,kBAAA;UAAA+D,GAAA;QAAA;4BADR,MAAW,CAAX3E,YAAA,CAAWmH,kBAAA,E;;8BACH,GACV,GAAArG,gBAAA,CAAGjB,MAAA,CAAAgK,oBAAoB,+C;;6DAGzBjK,mBAAA,YAAe,EACJC,MAAA,CAAAgK,oBAAoB,I,cAA/BnK,mBAAA,CAGM,OAHNqK,WAGM,GAFJ/J,YAAA,CAA2FgK,sBAAA;QAA7EC,UAAU,EAAE,GAAG;QAAG,WAAS,EAAE,KAAK;QAAEC,MAAM,EAAC,SAAS;QAAEC,aAAa,EAAE;wCACnFzJ,mBAAA,CAA6C;QAA1ClB,KAAK,EAAC;MAAe,GAAC,kBAAgB,qB,0CAI7CI,mBAAA,gBAAmB,EACRC,MAAA,CAAAuK,gBAAgB,I,cAA3B1K,mBAAA,CA+DM,OA/DN2K,WA+DM,G,8BA9DJ3J,mBAAA,CAAe,YAAX,QAAM,sBAEVd,mBAAA,UAAa,EACbc,mBAAA,CAMM;QANDlB,KAAK,EAAA6C,eAAA,EAAC,mBAAmB,EAASxC,MAAA,CAAAuK,gBAAgB,CAACF,MAAM;UAC5DlK,YAAA,CAGUY,kBAAA;QAHDpB,KAAK,EAAC;MAAa;0BAC1B,MAAsD,CAAzCK,MAAA,CAAAuK,gBAAgB,CAACF,MAAM,kB,cAApCnG,YAAA,CAAsDuG,gBAAA;UAAA3F,GAAA;QAAA,O,cACtDZ,YAAA,CAAgBwG,gBAAA;UAAA5F,GAAA;QAAA,I;;UAElBjE,mBAAA,CAA+D,QAA/D8J,WAA+D,EAAA1J,gBAAA,CAAlCjB,MAAA,CAAAuK,gBAAgB,CAACK,OAAO,iB,kBAGvD7K,mBAAA,UAAa,EACFC,MAAA,CAAAuK,gBAAgB,CAACM,KAAK,I,cAAjChL,mBAAA,CA6BM,OA7BNiL,WA6BM,GA5BJjK,mBAAA,CAAkG,YAA9F,WAAS,GAAAI,gBAAA,CAAGjB,MAAA,CAAAuK,gBAAgB,CAACM,KAAK,CAACE,UAAU,IAAG,GAAC,GAAA9J,gBAAA,CAAGjB,MAAA,CAAAuK,gBAAgB,CAACM,KAAK,CAACG,UAAU,IAAG,GAAC,iBAE7FjL,mBAAA,YAAe,EACJC,MAAA,CAAAuK,gBAAgB,CAACM,KAAK,CAACI,UAAU,IAAIjL,MAAA,CAAAuK,gBAAgB,CAACM,KAAK,CAACI,UAAU,CAAChG,MAAM,Q,cAAxFpF,mBAAA,CAUM,OAVNqL,WAUM,G,8BATJrK,mBAAA,CAAmB,YAAf,YAAU,sBACdV,YAAA,CAOegL,uBAAA;QAPD,YAAU,EAAC;MAAO;0BAC9B,MAKK,CALLtK,mBAAA,CAKK,MALLuK,WAKK,I,kBAJHvL,mBAAA,CAGK6E,SAAA,QAAAC,WAAA,CAHc3E,MAAA,CAAAuK,gBAAgB,CAACM,KAAK,CAACI,UAAU,EAAzCI,IAAI;+BAAfxL,mBAAA,CAGK;YAHkDiF,GAAG,EAAEuG;UAAI,IAC9DlL,YAAA,CAAiDY,kBAAA;YAAxCpB,KAAK,EAAC;UAAW;8BAAC,MAAY,CAAZQ,YAAA,CAAYmL,mBAAA,E;;cACvCzK,mBAAA,CAAyC,QAAzC0K,WAAyC,EAAAtK,gBAAA,CAAdoK,IAAI,iB;;;iDAMvCtL,mBAAA,YAAe,EACJC,MAAA,CAAAuK,gBAAgB,CAACM,KAAK,CAACW,WAAW,IAAIxL,MAAA,CAAAuK,gBAAgB,CAACM,KAAK,CAACW,WAAW,CAACvG,MAAM,Q,cAA1FpF,mBAAA,CAWM,OAXN4L,WAWM,G,8BAVJ5K,mBAAA,CAAmB,YAAf,YAAU,sBACdV,YAAA,CAQegL,uBAAA;QARD,YAAU,EAAC;MAAO;0BAC9B,MAMK,CANLtK,mBAAA,CAMK,MANL6K,WAMK,I,kBALH7L,mBAAA,CAIK6E,SAAA,QAAAC,WAAA,CAJc3E,MAAA,CAAAuK,gBAAgB,CAACM,KAAK,CAACW,WAAW,EAA1CH,IAAI;+BAAfxL,mBAAA,CAIK;YAJmDiF,GAAG,EAAEuG,IAAI,CAACM;cAChExL,YAAA,CAAgDY,kBAAA;YAAvCpB,KAAK,EAAC;UAAW;8BAAC,MAAW,CAAXQ,YAAA,CAAWyL,kBAAA,E;;cACtC/K,mBAAA,CAAkD,QAAlDgL,WAAkD,EAAA5K,gBAAA,CAAvBoK,IAAI,CAACM,QAAQ,kBACxC9K,mBAAA,CAAgD,QAAhDiL,WAAgD,EAAA7K,gBAAA,CAApBoK,IAAI,CAACU,KAAK,iB;;;wFAOhDhM,mBAAA,UAAa,EACFC,MAAA,CAAAuK,gBAAgB,CAACyB,WAAW,I,cAAvCnM,mBAAA,CAiBM,OAjBNoM,WAiBM,G,8BAhBJpL,mBAAA,CAAe,YAAX,QAAM,sBACVA,mBAAA,CAcM;QAdDlB,KAAK,EAAA6C,eAAA,EAAC,kBAAkB,EAASxC,MAAA,CAAAuK,gBAAgB,CAACyB,WAAW,CAAC3B,MAAM;UACvElK,YAAA,CAGUY,kBAAA;QAHDpB,KAAK,EAAC;MAAa;0BAC1B,MAAgE,CAArDK,MAAA,CAAAuK,gBAAgB,CAACyB,WAAW,CAAC3B,MAAM,kB,cAA9CnG,YAAA,CAAgEgI,cAAA;UAAApH,GAAA;QAAA,O,cAChEZ,YAAA,CAAgBwG,gBAAA;UAAA5F,GAAA;QAAA,I;;UAElBjE,mBAAA,CAAgF,QAAhFsL,WAAgF,EAAAlL,gBAAA,CAA9CjB,MAAA,CAAAuK,gBAAgB,CAACyB,WAAW,CAACpB,OAAO,kBAC3D5K,MAAA,CAAAuK,gBAAgB,CAACyB,WAAW,CAACI,IAAI,I,cAA5CvM,mBAAA,CAOM,OAPNwM,WAOM,GANJxL,mBAAA,CAA+D,WAA5D,OAAK,GAAAI,gBAAA,CAAGjB,MAAA,CAAAuK,gBAAgB,CAACyB,WAAW,CAACI,IAAI,CAACE,WAAW,kBACxDzL,mBAAA,CAA6D,WAA1D,QAAM,GAAAI,gBAAA,CAAGjB,MAAA,CAAAuK,gBAAgB,CAACyB,WAAW,CAACI,IAAI,CAACG,QAAQ,kBACtDpM,YAAA,CAGY6F,oBAAA;QAHDvE,IAAI,EAAC,SAAS;QAAEkB,OAAK,EAAA/B,MAAA,SAAAA,MAAA,OAAAY,MAAA,IAAExB,MAAA,CAAAwM,eAAe,CAACxM,MAAA,CAAAuK,gBAAgB,CAACyB,WAAW,CAACI,IAAI,CAACK,WAAW;;0BAC7F,MAA+B,CAA/BtM,YAAA,CAA+BY,kBAAA;4BAAtB,MAAY,CAAZZ,YAAA,CAAYuM,mBAAA,E;;2DAAU,UAEjC,G;;;;;QASd3M,mBAAA,QAAW,EACXI,YAAA,CA2BcyB,sBAAA;MA3BDC,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC;;MAChBD,KAAK,EAAArB,QAAA,CACd,MAKO,CALPK,mBAAA,CAKO,QALP8L,WAKO,GAJLxM,YAAA,CAEUY,kBAAA;0BADR,MAAa,CAAbZ,YAAA,CAAayM,oBAAA,E;;yDACL,MAEZ,G;wBAEF,MAiBM,CAjBN/L,mBAAA,CAiBM,OAjBNgM,WAiBM,GAhBJhM,mBAAA,CAeM,OAfNiM,WAeM,G,8BAdJjM,mBAAA,CAGM;QAHDlB,KAAK,EAAC;MAAa,IACtBkB,mBAAA,CAAe,YAAX,QAAM,GACVA,mBAAA,CAAwB,WAArB,mBAAiB,E,sBAEtBA,mBAAA,CASM,OATNkM,WASM,GARJlM,mBAAA,CAGM,OAHNmM,WAGM,GAFJ7M,YAAA,CAAyH6F,oBAAA;QAA9GvE,IAAI,EAAC,SAAS;QAAEuF,IAAI,EAAEC,IAAA,CAAAC,IAAI;QAAGvE,OAAK,EAAE3C,MAAA,CAAAiN,kBAAkB;QAAGzI,OAAO,EAAExE,MAAA,CAAAkN;;0BAA0B,MAAMtM,MAAA,UAAAA,MAAA,S,iBAAN,QAAM,E;;;yDAC7GT,YAAA,CAAwE6F,oBAAA;QAA5DgB,IAAI,EAAEC,IAAA,CAAAkG,YAAY;QAAGxK,OAAK,EAAE3C,MAAA,CAAAoN;;0BAAgB,MAAIxM,MAAA,UAAAA,MAAA,S,iBAAJ,MAAI,E;;;gDAE9DC,mBAAA,CAGM,OAHNwM,WAGM,GAFJlN,YAAA,CAC0B8D,mBAAA;oBADPjE,MAAA,CAAAsN,WAAW;qEAAXtN,MAAA,CAAAsN,WAAW,GAAA9L,MAAA;QAAEC,IAAI,EAAC,UAAU;QAAE8L,IAAI,EAAE,EAAE;QAAE5J,WAAW,EAAC,eAAe;QACpFhE,KAAK,EAAC;;;QAOlBI,mBAAA,WAAc,EACdI,YAAA,CA4BcyB,sBAAA;MA5BDC,KAAK,EAAC,OAAO;MAACC,IAAI,EAAC;;MACnBD,KAAK,EAAArB,QAAA,CACd,MAKO,CALPK,mBAAA,CAKO,QALP2M,WAKO,GAJLrN,YAAA,CAEUY,kBAAA;0BADR,MAAgB,CAAhBZ,YAAA,CAAgBsN,uBAAA,E;;yDACR,SAEZ,G;wBAEF,MAkBM,CAlBN5M,mBAAA,CAkBM,OAlBN6M,WAkBM,GAjBJ7M,mBAAA,CAgBM,OAhBN8M,WAgBM,G,8BAfJ9M,mBAAA,CAGM;QAHDlB,KAAK,EAAC;MAAa,IACtBkB,mBAAA,CAAgB,YAAZ,SAAO,GACXA,mBAAA,CAAiB,WAAd,YAAU,E,sBAEfA,mBAAA,CAUM,OAVN+M,WAUM,GATJ/M,mBAAA,CAIM,OAJNgN,WAIM,GAHJ1N,YAAA,CAAuH6F,oBAAA;QAA5GvE,IAAI,EAAC,SAAS;QAAEuF,IAAI,EAAEC,IAAA,CAAAC,IAAI;QAAGvE,OAAK,EAAE3C,MAAA,CAAA8N,iBAAiB;QAAGtJ,OAAO,EAAExE,MAAA,CAAA+N;;0BAAyB,MAAMnN,MAAA,UAAAA,MAAA,S,iBAAN,QAAM,E;;;yDAC3GT,YAAA,CAAqE6F,oBAAA;QAAzDgB,IAAI,EAAEC,IAAA,CAAA+G,QAAQ;QAAGrL,OAAK,EAAE3C,MAAA,CAAAiO;;0BAAiB,MAAIrN,MAAA,UAAAA,MAAA,S,iBAAJ,MAAI,E;;;8CACzDT,YAAA,CAAuE6F,oBAAA;QAA3DgB,IAAI,EAAEC,IAAA,CAAAkG,YAAY;QAAGxK,OAAK,EAAE3C,MAAA,CAAAkO;;0BAAe,MAAItN,MAAA,UAAAA,MAAA,S,iBAAJ,MAAI,E;;;gDAE7DC,mBAAA,CAGM,OAHNsN,WAGM,GAFJhO,YAAA,CACyB8D,mBAAA;oBADNjE,MAAA,CAAAoO,UAAU;qEAAVpO,MAAA,CAAAoO,UAAU,GAAA5M,MAAA;QAAEC,IAAI,EAAC,UAAU;QAAE8L,IAAI,EAAE,EAAE;QAAE5J,WAAW,EAAC,gBAAgB;QACpFhE,KAAK,EAAC;;;QAOlBI,mBAAA,UAAa,EACbI,YAAA,CAoCcyB,sBAAA;MApCDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;MAClBD,KAAK,EAAArB,QAAA,CACd,MAKO,CALPK,mBAAA,CAKO,QALPwN,WAKO,GAJLlO,YAAA,CAEUY,kBAAA;0BADR,MAAW,CAAXZ,YAAA,CAAWyL,kBAAA,E;;yDACH,QAEZ,G;wBAEF,MA0BM,CA1BN/K,mBAAA,CA0BM,OA1BNyN,WA0BM,GAzBJzN,mBAAA,CAwBM,OAxBN0N,WAwBM,G,8BAvBJ1N,mBAAA,CAGM;QAHDlB,KAAK,EAAC;MAAa,IACtBkB,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAsB,WAAnB,iBAAe,E,sBAEpBA,mBAAA,CAkBM,OAlBN2N,WAkBM,GAjBJ3N,mBAAA,CASM,OATN4N,WASM,GARJtO,YAAA,CAA2C6F,oBAAA;QAA/BgB,IAAI,EAAEC,IAAA,CAAAyH;MAAO;0BAAE,MAAI9N,MAAA,UAAAA,MAAA,S,iBAAJ,MAAI,E;;;mCAC/BT,YAAA,CAA0C6F,oBAAA;QAA9BgB,IAAI,EAAEC,IAAA,CAAAmB;MAAM;0BAAE,MAAIxH,MAAA,UAAAA,MAAA,S,iBAAJ,MAAI,E;;;mCAC9BT,YAAA,CAKYsD,oBAAA;oBALQzD,MAAA,CAAA2O,QAAQ;qEAAR3O,MAAA,CAAA2O,QAAQ,GAAAnN,MAAA;QAAEmC,WAAW,EAAC,MAAM;QAAC/D,KAAoB,EAApB;UAAA;QAAA;;0BAC/C,MAAoC,CAApCO,YAAA,CAAoCyD,oBAAA;UAAzB/B,KAAK,EAAC,IAAI;UAACgC,KAAK,EAAC;YAC5B1D,YAAA,CAAsCyD,oBAAA;UAA3B/B,KAAK,EAAC,IAAI;UAACgC,KAAK,EAAC;YAC5B1D,YAAA,CAAwCyD,oBAAA;UAA7B/B,KAAK,EAAC,IAAI;UAACgC,KAAK,EAAC;YAC5B1D,YAAA,CAAqCyD,oBAAA;UAA1B/B,KAAK,EAAC,IAAI;UAACgC,KAAK,EAAC;;;2CAGhChD,mBAAA,CAMM,OANN+N,WAMM,I,kBALJ/O,mBAAA,CAIM6E,SAAA,QAAAC,WAAA,CAJuC3E,MAAA,CAAA6O,IAAI,GAAnBC,GAAG,EAAEC,KAAK;6BAAxClP,mBAAA,CAIM;UAJDF,KAAK,EAAA6C,eAAA,EAAC,UAAU,EAAmDsM,GAAG,CAACE,KAAK;UAA7BlK,GAAG,EAAEiK;YACvDlO,mBAAA,CAA0C,OAA1CoO,WAA0C,EAAAhO,gBAAA,CAAjB6N,GAAG,CAACI,IAAI,kBACjCrO,mBAAA,CAA0D,OAA1DsO,WAA0D,EAAAlO,gBAAA,CAAhC6N,GAAG,CAACE,KAAK,CAACI,WAAW,oBAC/CvO,mBAAA,CAAgD,OAAhDwO,WAAgD,EAAApO,gBAAA,CAApB6N,GAAG,CAAClE,OAAO,iB;;;;;qDAUvD7K,mBAAA,WAAc,EACdI,YAAA,CAiBYmP,oBAAA;gBAjBQtP,MAAA,CAAAuP,kBAAkB;iEAAlBvP,MAAA,CAAAuP,kBAAkB,GAAA/N,MAAA;IAAEgO,KAAK,EAAC,MAAM;IAACC,KAAK,EAAC,OAAO;IAAE,sBAAoB,EAAE,KAAK;IAC5F,uBAAqB,EAAE,KAAK;IAAG,YAAU,EAAE;;IASjCC,MAAM,EAAAlP,QAAA,CACf,MAIO,CAJPK,mBAAA,CAIO,QAJP8O,WAIO,GAHLxP,YAAA,CAEY6F,oBAAA;MAFDvE,IAAI,EAAC,SAAS;MAAEkB,OAAK,EAAE3C,MAAA,CAAA4P,WAAW;MAAGpL,OAAO,EAAExE,MAAA,CAAA6P;;wBAAc,MAEvEjP,MAAA,UAAAA,MAAA,S,iBAFuE,MAEvE,E;;;;sBAZJ,MAOU,CAPVT,YAAA,CAOU8C,kBAAA;MAPAC,KAAK,EAAElD,MAAA,CAAA8P,SAAS;MAAGC,KAAK,EAAE/P,MAAA,CAAAgQ,UAAU;MAAEC,GAAG,EAAC,cAAc;MAAC,aAAW,EAAC;;wBAC7E,MAEe,CAFf9P,YAAA,CAEeqD,uBAAA;QAFD3B,KAAK,EAAC,KAAK;QAACqO,IAAI,EAAC;;0BAC7B,MAA8D,CAA9D/P,YAAA,CAA8D8D,mBAAA;sBAA3CjE,MAAA,CAAA8P,SAAS,CAAC3O,QAAQ;uEAAlBnB,MAAA,CAAA8P,SAAS,CAAC3O,QAAQ,GAAAK,MAAA;UAAEmC,WAAW,EAAC;;;UAErDxD,YAAA,CAEeqD,uBAAA;QAFD3B,KAAK,EAAC,IAAI;QAACqO,IAAI,EAAC;;0BAC5B,MAAwG,CAAxG/P,YAAA,CAAwG8D,mBAAA;sBAArFjE,MAAA,CAAA8P,SAAS,CAACK,QAAQ;uEAAlBnQ,MAAA,CAAA8P,SAAS,CAACK,QAAQ,GAAA3O,MAAA;UAAEC,IAAI,EAAC,UAAU;UAACkC,WAAW,EAAC,OAAO;UAAEyM,OAAK,EAAAC,SAAA,CAAQrQ,MAAA,CAAA4P,WAAW;;;;;;;qCAY1G7P,mBAAA,WAAc,EACdI,YAAA,CA2RYmP,oBAAA;gBA3RQtP,MAAA,CAAAsQ,oBAAoB;iEAApBtQ,MAAA,CAAAsQ,oBAAoB,GAAA9O,MAAA;IAAGgO,KAAK,EAAExP,MAAA,CAAAuQ,kBAAkB,CAACC,EAAE;IAAsBf,KAAK,EAAC,KAAK;IACrG,sBAAoB,EAAE,KAAK;IAAG,cAAY,EAAEzP,MAAA,CAAAyQ,qBAAqB;IAAE9Q,KAAK,EAAC,qBAAqB;IAAC+Q,GAAG,EAAC,KAAK;IACzG,kBAAgB,EAAhB;;IAuQWhB,MAAM,EAAAlP,QAAA,CACf,MAeM,CAfNK,mBAAA,CAeM,OAfN8P,YAeM,GAdJxQ,YAAA,CAaWyQ,mBAAA;MAbD1K,IAAI,EAAC;IAAO;wBACpB,MAKY,CALZ/F,YAAA,CAKY6F,oBAAA;QALArD,OAAK,EAAE3C,MAAA,CAAAyQ,qBAAqB;QAAEvK,IAAI,EAAC;;0BAC7C,MAEU,CAFV/F,YAAA,CAEUY,kBAAA;4BADR,MAAS,CAATZ,YAAA,CAASuK,gBAAA,E;;0CAEX7J,mBAAA,CAAe,cAAT,IAAE,qB;;;sCAEVV,YAAA,CAKY6F,oBAAA;QALDvE,IAAI,EAAC,SAAS;QAAEkB,OAAK,EAAE3C,MAAA,CAAA6Q,eAAe;QAAE3K,IAAI,EAAC;;0BACtD,MAEU,CAFV/F,YAAA,CAEUY,kBAAA;4BADR,MAAS,CAATZ,YAAA,CAASsK,gBAAA,E;;0CAEX5J,mBAAA,CAAkB,cAAZ,OAAK,qB;;;;;;sBAnRnB,MAoQM,CApQNA,mBAAA,CAoQM,OApQNiQ,WAoQM,GAnQJ/Q,mBAAA,cAAiB,EACjBc,mBAAA,CAiQM,OAjQNkQ,WAiQM,GAhQJlQ,mBAAA,CAeM,OAfNmQ,WAeM,GAdJnQ,mBAAA,CAMM;MANDlB,KAAK,EAAA6C,eAAA,EAAC,UAAU;QAAAC,MAAA,EAAmBzC,MAAA,CAAAiR,eAAe;MAAA;MACpDtO,OAAK,EAAA/B,MAAA,SAAAA,MAAA,OAAAY,MAAA,IAAExB,MAAA,CAAAkR,cAAc;QACtB/Q,YAAA,CAEUY,kBAAA;MAFDpB,KAAK,EAAC;IAAU;wBACvB,MAAW,CAAXQ,YAAA,CAAWkC,kBAAA,E;;sCAEbxB,mBAAA,CAAiC;MAA3BlB,KAAK,EAAC;IAAU,GAAC,KAAG,qB,kBAE5BkB,mBAAA,CAMM;MANDlB,KAAK,EAAA6C,eAAA,EAAC,UAAU;QAAAC,MAAA,EAAmBzC,MAAA,CAAAiR,eAAe;MAAA;MACpDtO,OAAK,EAAA/B,MAAA,SAAAA,MAAA,OAAAY,MAAA,IAAExB,MAAA,CAAAkR,cAAc;QACtB/Q,YAAA,CAEUY,kBAAA;MAFDpB,KAAK,EAAC;IAAU;wBACvB,MAAa,CAAbQ,YAAA,CAAayM,oBAAA,E;;sCAEf/L,mBAAA,CAAkC;MAA5BlB,KAAK,EAAC;IAAU,GAAC,MAAI,qB,oBAI/BI,mBAAA,WAAc,EACdc,mBAAA,CA6OM,OA7ONsQ,WA6OM,GA5OJpR,mBAAA,WAAc,E,gBACdc,mBAAA,CAiGM,OAjGNuQ,WAiGM,GAhGJrR,mBAAA,UAAa,EACbc,mBAAA,CAuCM,OAvCNwQ,WAuCM,GAtCJlR,YAAA,CAqCU8C,kBAAA;MArCAC,KAAK,EAAElD,MAAA,CAAAuQ,kBAAkB;MAAE,aAAW,EAAC,OAAO;MAAC5Q,KAAK,EAAC,aAAa;MAACuG,IAAI,EAAC;;wBAChF,MAsBS,CAtBT/F,YAAA,CAsBSiD,iBAAA;QAtBAC,MAAM,EAAE;MAAE;0BACjB,MAIS,CAJTlD,YAAA,CAISmD,iBAAA;UAJAC,IAAI,EAAE;QAAC;4BACd,MAEe,CAFfpD,YAAA,CAEeqD,uBAAA;YAFD3B,KAAK,EAAC,OAAO;YAACyP,QAAQ,EAAR;;8BAC1B,MAAuF,CAAvFnR,YAAA,CAAuF8D,mBAAA;0BAApEjE,MAAA,CAAAuQ,kBAAkB,CAACgB,WAAW;2EAA9BvR,MAAA,CAAAuQ,kBAAkB,CAACgB,WAAW,GAAA/P,MAAA;cAAEmC,WAAW,EAAC,WAAW;cAAC6N,SAAS,EAAT;;;;;YAG/ErR,YAAA,CAISmD,iBAAA;UAJAC,IAAI,EAAE;QAAC;4BACd,MAEe,CAFfpD,YAAA,CAEeqD,uBAAA;YAFD3B,KAAK,EAAC,OAAO;YAACyP,QAAQ,EAAR;;8BAC1B,MAAuF,CAAvFnR,YAAA,CAAuF8D,mBAAA;0BAApEjE,MAAA,CAAAuQ,kBAAkB,CAACkB,WAAW;2EAA9BzR,MAAA,CAAAuQ,kBAAkB,CAACkB,WAAW,GAAAjQ,MAAA;cAAEmC,WAAW,EAAC,WAAW;cAAC6N,SAAS,EAAT;;;;;YAG/ErR,YAAA,CAISmD,iBAAA;UAJAC,IAAI,EAAE;QAAC;4BACd,MAEe,CAFfpD,YAAA,CAEeqD,uBAAA;YAFD3B,KAAK,EAAC;UAAQ;8BAC1B,MAAmH,CAAnH1B,YAAA,CAAmHuR,0BAAA;0BAAzF1R,MAAA,CAAAuQ,kBAAkB,CAACoB,SAAS;2EAA5B3R,MAAA,CAAAuQ,kBAAkB,CAACoB,SAAS,GAAAnQ,MAAA;cAAGoQ,GAAG,EAAE,CAAC;cAAGC,GAAG,EAAE,GAAG;cAAElO,WAAW,EAAC,GAAG;cAAC/D,KAAoB,EAApB;gBAAA;cAAA;;;;;YAGhGO,YAAA,CAKSmD,iBAAA;UALAC,IAAI,EAAE;QAAC;4BACd,MAGe,CAHfpD,YAAA,CAGeqD,uBAAA;YAHD3B,KAAK,EAAC;UAAQ;8BAC1B,MAC8C,CAD9C1B,YAAA,CAC8CuR,0BAAA;0BADpB1R,MAAA,CAAAuQ,kBAAkB,CAACuB,iBAAiB;2EAApC9R,MAAA,CAAAuQ,kBAAkB,CAACuB,iBAAiB,GAAAtQ,MAAA;cAAGoQ,GAAG,EAAE,CAAC;cAAGC,GAAG,EAAE,IAAI;cACjFlO,WAAW,EAAC,QAAQ;cAAC/D,KAAoB,EAApB;gBAAA;cAAA;;;;;;;UAK7BO,YAAA,CAWSiD,iBAAA;QAXAC,MAAM,EAAE;MAAE;0BACjB,MASS,CATTlD,YAAA,CASSmD,iBAAA;UATAC,IAAI,EAAE;QAAE;4BACf,MAOe,CAPfpD,YAAA,CAOeqD,uBAAA;YAPD3B,KAAK,EAAC;UAAM;8BACxB,MAKY,CALZ1B,YAAA,CAKY6F,oBAAA;cALDvE,IAAI,EAAC,SAAS;cAAEkB,OAAK,EAAA/B,MAAA,SAAAA,MAAA,OAAAY,MAAA,IAAExB,MAAA,CAAA+R,oBAAoB;cAAWnS,KAAoB,EAApB;gBAAA;cAAA;;gCAC/D,MAEU,CAFVO,YAAA,CAEUY,kBAAA;kCADR,MAAO,CAAPZ,YAAA,CAAO2I,cAAA,E;;iEACC,WAEZ,G;;;;;;;;;;;oCAOV/I,mBAAA,UAAa,EACbc,mBAAA,CAoDM,OApDNmR,WAoDM,G,8BAnDJnR,mBAAA,CAA6E;MAAzEjB,KAA+D,EAA/D;QAAA;QAAA;QAAA;MAAA;IAA+D,GAAC,MAAI,sBACxEO,YAAA,CAiDSiD,iBAAA;MAjDAC,MAAM,EAAE;IAAE;wBACjB,MAAa,CAAbtD,mBAAA,UAAa,EACbI,YAAA,CAiBSmD,iBAAA;QAjBAC,IAAI,EAAE;MAAC;0BACd,MAeM,CAfN1C,mBAAA,CAeM,OAfNoR,WAeM,G,8BAdJpR,mBAAA,CAAa,YAAT,MAAI,sBACRA,mBAAA,CAYM,OAZNqR,WAYM,GAXJ/R,YAAA,CAAiFgS,sBAAA;sBAA3DnS,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACC,UAAU;uEAAvCrS,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACC,UAAU,GAAA7Q,MAAA;;4BAAE,MAAIZ,MAAA,UAAAA,MAAA,S,iBAAJ,MAAI,E;;;2CACnET,YAAA,CAAkFgS,sBAAA;sBAA5DnS,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACE,WAAW;uEAAxCtS,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACE,WAAW,GAAA9Q,MAAA;;4BAAE,MAAIZ,MAAA,UAAAA,MAAA,S,iBAAJ,MAAI,E;;;2CACpET,YAAA,CAAoFgS,sBAAA;sBAA9DnS,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACG,aAAa;uEAA1CvS,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACG,aAAa,GAAA/Q,MAAA;;4BAAE,MAAIZ,MAAA,UAAAA,MAAA,S,iBAAJ,MAAI,E;;;2CACtET,YAAA,CAAoFgS,sBAAA;sBAA9DnS,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACI,aAAa;uEAA1CxS,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACI,aAAa,GAAAhR,MAAA;;4BAAE,MAAIZ,MAAA,UAAAA,MAAA,S,iBAAJ,MAAI,E;;;2CACtET,YAAA,CAAkFgS,sBAAA;sBAA5DnS,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACK,WAAW;uEAAxCzS,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACK,WAAW,GAAAjR,MAAA;;4BAAE,MAAIZ,MAAA,UAAAA,MAAA,S,iBAAJ,MAAI,E;;;2CACpET,YAAA,CAAkFgS,sBAAA;sBAA5DnS,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACM,WAAW;uEAAxC1S,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACM,WAAW,GAAAlR,MAAA;;4BAAE,MAAIZ,MAAA,UAAAA,MAAA,S,iBAAJ,MAAI,E;;;2CACpET,YAAA,CAAkFgS,sBAAA;sBAA5DnS,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACO,WAAW;uEAAxC3S,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACO,WAAW,GAAAnR,MAAA;;4BAAE,MAAIZ,MAAA,UAAAA,MAAA,S,iBAAJ,MAAI,E;;;2CACpET,YAAA,CAAmFgS,sBAAA;sBAA7DnS,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACQ,YAAY;uEAAzC5S,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACQ,YAAY,GAAApR,MAAA;;4BAAE,MAAIZ,MAAA,UAAAA,MAAA,S,iBAAJ,MAAI,E;;;2CACrET,YAAA,CAAsFgS,sBAAA;sBAAhEnS,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACS,eAAe;uEAA5C7S,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACS,eAAe,GAAArR,MAAA;;4BAAE,MAAIZ,MAAA,UAAAA,MAAA,S,iBAAJ,MAAI,E;;;2CACxET,YAAA,CAAuFgS,sBAAA;sBAAjEnS,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACU,cAAc;uEAA3C9S,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACU,cAAc,GAAAtR,MAAA;;4BAAE,MAAMZ,MAAA,UAAAA,MAAA,S,iBAAN,QAAM,E;;;2CACzET,YAAA,CAAwFgS,sBAAA;sBAAlEnS,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACW,eAAe;uEAA5C/S,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACW,eAAe,GAAAvR,MAAA;;4BAAE,MAAMZ,MAAA,UAAAA,MAAA,S,iBAAN,QAAM,E;;;;;UAKhFb,mBAAA,UAAa,EACbI,YAAA,CAYSmD,iBAAA;QAZAC,IAAI,EAAE;MAAC;0BACd,MAUM,CAVN1C,mBAAA,CAUM,OAVNmS,WAUM,G,8BATJnS,mBAAA,CAAa,YAAT,MAAI,sBACRA,mBAAA,CAOM,OAPNoS,YAOM,GANJ9S,YAAA,CAAkFgS,sBAAA;sBAA5DnS,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACc,WAAW;uEAAxClT,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACc,WAAW,GAAA1R,MAAA;;4BAAE,MAAIZ,MAAA,UAAAA,MAAA,S,iBAAJ,MAAI,E;;;2CACpET,YAAA,CAAmFgS,sBAAA;sBAA7DnS,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACe,YAAY;uEAAzCnT,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACe,YAAY,GAAA3R,MAAA;;4BAAE,MAAIZ,MAAA,UAAAA,MAAA,S,iBAAJ,MAAI,E;;;2CACrET,YAAA,CAAqFgS,sBAAA;sBAA/DnS,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACgB,cAAc;uEAA3CpT,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACgB,cAAc,GAAA5R,MAAA;;4BAAE,MAAIZ,MAAA,UAAAA,MAAA,S,iBAAJ,MAAI,E;;;2CACvET,YAAA,CAAqFgS,sBAAA;sBAA/DnS,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACiB,cAAc;uEAA3CrT,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACiB,cAAc,GAAA7R,MAAA;;4BAAE,MAAIZ,MAAA,UAAAA,MAAA,S,iBAAJ,MAAI,E;;;2CACvET,YAAA,CAAmFgS,sBAAA;sBAA7DnS,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACkB,YAAY;uEAAzCtT,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACkB,YAAY,GAAA9R,MAAA;;4BAAE,MAAIZ,MAAA,UAAAA,MAAA,S,iBAAJ,MAAI,E;;;2CACrET,YAAA,CAAyFgS,sBAAA;sBAAnEnS,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACmB,aAAa;uEAA1CvT,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACmB,aAAa,GAAA/R,MAAA;;4BAAE,MAASZ,MAAA,UAAAA,MAAA,S,iBAAT,WAAS,E;;;;;UAKjFb,mBAAA,WAAc,EACdI,YAAA,CAWSmD,iBAAA;QAXAC,IAAI,EAAE;MAAC;0BACd,MASM,CATN1C,mBAAA,CASM,OATN2S,YASM,G,8BARJ3S,mBAAA,CAAc,YAAV,OAAK,sBACTA,mBAAA,CAMM,OANN4S,YAMM,GALJtT,YAAA,CAA+EgS,sBAAA;sBAAzDnS,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACsB,OAAO;uEAApC1T,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACsB,OAAO,GAAAlS,MAAA;;4BAAE,MAAKZ,MAAA,UAAAA,MAAA,S,iBAAL,OAAK,E;;;2CACjET,YAAA,CAAgFgS,sBAAA;sBAA1DnS,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACuB,QAAQ;uEAArC3T,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACuB,QAAQ,GAAAnS,MAAA;;4BAAE,MAAKZ,MAAA,UAAAA,MAAA,S,iBAAL,OAAK,E;;;2CAClET,YAAA,CAAkFgS,sBAAA;sBAA5DnS,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACwB,UAAU;uEAAvC5T,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACwB,UAAU,GAAApS,MAAA;;4BAAE,MAAKZ,MAAA,UAAAA,MAAA,S,iBAAL,OAAK,E;;;2CACpET,YAAA,CAAkFgS,sBAAA;sBAA5DnS,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACyB,UAAU;uEAAvC7T,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAACyB,UAAU,GAAArS,MAAA;;4BAAE,MAAKZ,MAAA,UAAAA,MAAA,S,iBAAL,OAAK,E;;;2CACpET,YAAA,CAAkFgS,sBAAA;sBAA5DnS,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAAC0B,QAAQ;uEAArC9T,MAAA,CAAAuQ,kBAAkB,CAAC6B,SAAS,CAAC0B,QAAQ,GAAAtS,MAAA;;4BAAE,MAAOZ,MAAA,UAAAA,MAAA,S,iBAAP,SAAO,E;;;;;;;4CA3FnEZ,MAAA,CAAAiR,eAAe,uB,GAmG5BlR,mBAAA,YAAe,E,gBACfc,mBAAA,CAsIM,OAtINkT,YAsIM,GArIJhU,mBAAA,SAAY,EACZc,mBAAA,CAyBM,OAzBNmT,YAyBM,GAxBJ7T,YAAA,CAuBWyQ,mBAAA;MAvBD1K,IAAI,EAAC,SAAS;MAAC+N,IAAI,EAAJ;;wBACvB,MAKY,CALZ9T,YAAA,CAKY6F,oBAAA;QALDvE,IAAI,EAAC,SAAS;QAAEkB,OAAK,EAAE3C,MAAA,CAAAkU;;0BAChC,MAEU,CAFV/T,YAAA,CAEUY,kBAAA;4BADR,MAAQ,CAARZ,YAAA,CAAQgU,eAAA,E;;2DACA,QAEZ,G;;;sCACAhU,YAAA,CAKY6F,oBAAA;QALDvE,IAAI,EAAC,SAAS;QAAEkB,OAAK,EAAE3C,MAAA,CAAAoU;;0BAChC,MAEU,CAFVjU,YAAA,CAEUY,kBAAA;4BADR,MAAW,CAAXZ,YAAA,CAAWkU,kBAAA,E;;2DACH,QAEZ,G;;;sCACAlU,YAAA,CAKY6F,oBAAA;QALDvE,IAAI,EAAC,QAAQ;QAAEkB,OAAK,EAAE3C,MAAA,CAAAsU;;0BAC/B,MAEU,CAFVnU,YAAA,CAEUY,kBAAA;4BADR,MAAU,CAAVZ,YAAA,CAAUoU,iBAAA,E;;2DACF,QAEZ,G;;;sCACApU,YAAA,CAAmCqU,qBAAA;QAAvBC,SAAS,EAAC;MAAU,IAChCtU,YAAA,CAEUuU,kBAAA;QAFDjT,IAAI,EAAC;MAAM;0BAAC;UAAA,IAAAkT,qBAAA;UAAA,OACX,C,iBADW,WACX,GAAA1T,gBAAA,CAAG,EAAA0T,qBAAA,GAAA3U,MAAA,CAAAuQ,kBAAkB,CAACqE,MAAM,cAAAD,qBAAA,uBAAzBA,qBAAA,CAA2B1P,MAAM,uB;;;;;UAKlDlF,mBAAA,UAAa,EACbc,mBAAA,CAuGM,OAvGNgU,YAuGM,GAtGJ1U,YAAA,CAqGW2U,mBAAA;MArGA1I,IAAI,EAAEpM,MAAA,CAAAuQ,kBAAkB,CAACqE,MAAM;MAAEjV,KAAK,EAAC,aAAa;MAACoV,MAAM,EAAN,EAAM;MAACC,MAAM,EAAN,EAAM;MAC3E,YAAU,EAAC,gBAAgB;MAAC9O,IAAI,EAAC;;wBACjC,MAAqE,CAArE/F,YAAA,CAAqE8U,0BAAA;QAApDpT,KAAK,EAAC,IAAI;QAACJ,IAAI,EAAC,OAAO;QAACgO,KAAK,EAAC,IAAI;QAACyF,KAAK,EAAC;UAE1D/U,YAAA,CAIkB8U,0BAAA;QAJDpT,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC;;QAC3BsT,OAAO,EAAA3U,QAAA,CAChB,CAAmF;UAD/D4U;QAAG,OACvBjV,YAAA,CAAmF8D,mBAAA;sBAAhEmR,GAAG,CAAC7D,WAAW;2CAAf6D,GAAG,CAAC7D,WAAW,GAAA/P,MAAA;UAAEmC,WAAW,EAAC,SAAS;UAACuC,IAAI,EAAC,OAAO;UAACsL,SAAS,EAAT;;;UAI3ErR,YAAA,CAIkB8U,0BAAA;QAJDpT,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC;;QAC3BsT,OAAO,EAAA3U,QAAA,CAChB,CAAmF;UAD/D4U;QAAG,OACvBjV,YAAA,CAAmF8D,mBAAA;sBAAhEmR,GAAG,CAAC3D,WAAW;2CAAf2D,GAAG,CAAC3D,WAAW,GAAAjQ,MAAA;UAAEmC,WAAW,EAAC,SAAS;UAACuC,IAAI,EAAC,OAAO;UAACsL,SAAS,EAAT;;;UAI3ErR,YAAA,CAakB8U,0BAAA;QAbDpT,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC;;QAC3BsT,OAAO,EAAA3U,QAAA,CAChB,CASY;UAVQ4U;QAAG,OACvBjV,YAAA,CASYsD,oBAAA;sBATQ2R,GAAG,CAAC3T,IAAI;2CAAR2T,GAAG,CAAC3T,IAAI,GAAAD,MAAA;UAAEmC,WAAW,EAAC,MAAM;UAACuC,IAAI,EAAC,OAAO;UAACtG,KAAmB,EAAnB;YAAA;UAAA;;4BAC5D,MAAqC,CAArCO,YAAA,CAAqCyD,oBAAA;YAA1B/B,KAAK,EAAC,KAAK;YAACgC,KAAK,EAAC;cAC7B1D,YAAA,CAAqDyD,oBAAA;YAA1C/B,KAAK,EAAC,aAAa;YAACgC,KAAK,EAAC;cACrC1D,YAAA,CAAuDyD,oBAAA;YAA5C/B,KAAK,EAAC,cAAc;YAACgC,KAAK,EAAC;cACtC1D,YAAA,CAAuDyD,oBAAA;YAA5C/B,KAAK,EAAC,cAAc;YAACgC,KAAK,EAAC;cACtC1D,YAAA,CAAuDyD,oBAAA;YAA5C/B,KAAK,EAAC,cAAc;YAACgC,KAAK,EAAC;cACtC1D,YAAA,CAAuCyD,oBAAA;YAA5B/B,KAAK,EAAC,MAAM;YAACgC,KAAK,EAAC;cAC9B1D,YAAA,CAA+CyD,oBAAA;YAApC/B,KAAK,EAAC,UAAU;YAACgC,KAAK,EAAC;cAClC1D,YAAA,CAAyDyD,oBAAA;YAA9C/B,KAAK,EAAC,eAAe;YAACgC,KAAK,EAAC;;;;;UAK7C1D,YAAA,CAgBkB8U,0BAAA;QAhBDpT,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC;;QAC3BsT,OAAO,EAAA3U,QAAA,CAChB,CAYY;UAbQ4U;QAAG,OACvBjV,YAAA,CAYYsD,oBAAA;sBAZQ2R,GAAG,CAACC,WAAW;2CAAfD,GAAG,CAACC,WAAW,GAAA7T,MAAA;UAAEmC,WAAW,EAAC,MAAM;UAACuC,IAAI,EAAC,OAAO;UAACtG,KAAmB,EAAnB;YAAA;UAAA;;4BACnE,MAAqC,CAArCO,YAAA,CAAqCyD,oBAAA;YAA1B/B,KAAK,EAAC,KAAK;YAACgC,KAAK,EAAC;cAC7B1D,YAAA,CAAuCyD,oBAAA;YAA5B/B,KAAK,EAAC,MAAM;YAACgC,KAAK,EAAC;cAC9B1D,YAAA,CAAqCyD,oBAAA;YAA1B/B,KAAK,EAAC,KAAK;YAACgC,KAAK,EAAC;cAC7B1D,YAAA,CAAuCyD,oBAAA;YAA5B/B,KAAK,EAAC,MAAM;YAACgC,KAAK,EAAC;cAC9B1D,YAAA,CAAqCyD,oBAAA;YAA1B/B,KAAK,EAAC,KAAK;YAACgC,KAAK,EAAC;cAC7B1D,YAAA,CAAuCyD,oBAAA;YAA5B/B,KAAK,EAAC,MAAM;YAACgC,KAAK,EAAC;cAC9B1D,YAAA,CAAuCyD,oBAAA;YAA5B/B,KAAK,EAAC,MAAM;YAACgC,KAAK,EAAC;cAC9B1D,YAAA,CAAuCyD,oBAAA;YAA5B/B,KAAK,EAAC,MAAM;YAACgC,KAAK,EAAC;cAC9B1D,YAAA,CAAuCyD,oBAAA;YAA5B/B,KAAK,EAAC,MAAM;YAACgC,KAAK,EAAC;cAC9B1D,YAAA,CAAqCyD,oBAAA;YAA1B/B,KAAK,EAAC,KAAK;YAACgC,KAAK,EAAC;cAC7B1D,YAAA,CAA2CyD,oBAAA;YAAhC/B,KAAK,EAAC,QAAQ;YAACgC,KAAK,EAAC;;;;;UAKtC1D,YAAA,CAIkB8U,0BAAA;QAJDpT,KAAK,EAAC,IAAI;QAAC4N,KAAK,EAAC,IAAI;QAACyF,KAAK,EAAC;;QAChCC,OAAO,EAAA3U,QAAA,CAChB,CAAsC;UADlB4U;QAAG,OACvBjV,YAAA,CAAsCgS,sBAAA;sBAAhBiD,GAAG,CAAC9D,QAAQ;2CAAZ8D,GAAG,CAAC9D,QAAQ,GAAA9P;;;UAItCrB,YAAA,CAIkB8U,0BAAA;QAJDpT,KAAK,EAAC,IAAI;QAAC4N,KAAK,EAAC,IAAI;QAACyF,KAAK,EAAC;;QAChCC,OAAO,EAAA3U,QAAA,CAChB,CAAwC;UADpB4U;QAAG,OACvBjV,YAAA,CAAwCgS,sBAAA;sBAAlBiD,GAAG,CAACE,UAAU;2CAAdF,GAAG,CAACE,UAAU,GAAA9T;;;UAIxCrB,YAAA,CAIkB8U,0BAAA;QAJDpT,KAAK,EAAC,IAAI;QAAC4N,KAAK,EAAC,IAAI;QAACyF,KAAK,EAAC;;QAChCC,OAAO,EAAA3U,QAAA,CAChB,CAAqC;UADjB4U;QAAG,OACvBjV,YAAA,CAAqCgS,sBAAA;sBAAfiD,GAAG,CAACG,OAAO;2CAAXH,GAAG,CAACG,OAAO,GAAA/T;;;UAIrCrB,YAAA,CAIkB8U,0BAAA;QAJDpT,KAAK,EAAC,IAAI;QAAC4N,KAAK,EAAC,IAAI;QAACyF,KAAK,EAAC;;QAChCC,OAAO,EAAA3U,QAAA,CAChB,CAAyC;UADrB4U;QAAG,OACvBjV,YAAA,CAAyCgS,sBAAA;sBAAnBiD,GAAG,CAACI,WAAW;2CAAfJ,GAAG,CAACI,WAAW,GAAAhU;;;UAIzCrB,YAAA,CA2BkB8U,0BAAA;QA3BDpT,KAAK,EAAC,IAAI;QAAC4N,KAAK,EAAC,KAAK;QAACyF,KAAK,EAAC,QAAQ;QAACO,KAAK,EAAC;;QAChDN,OAAO,EAAA3U,QAAA,CAChB,CAuBM;UAxBc4U,GAAG;UAAEM;QAAM,OAC/B7U,mBAAA,CAuBM,OAvBN8U,YAuBM,GAtBJxV,YAAA,CAKY6F,oBAAA;UALDE,IAAI,EAAC,OAAO;UAACzE,IAAI,EAAC,SAAS;UAAEkB,OAAK,EAAAnB,MAAA,IAAExB,MAAA,CAAA4V,WAAW,CAACF,MAAM;UAAI1Q,QAAQ,EAAE0Q,MAAM;UACnFG,MAAM,EAAN;;4BACA,MAEU,CAFV1V,YAAA,CAEUY,kBAAA;8BADR,MAAW,CAAXZ,YAAA,CAAW2V,kBAAA,E;;;;sEAGf3V,YAAA,CAKY6F,oBAAA;UALDE,IAAI,EAAC,OAAO;UAACzE,IAAI,EAAC,SAAS;UAAEkB,OAAK,EAAAnB,MAAA,IAAExB,MAAA,CAAA+V,aAAa,CAACL,MAAM;UAChE1Q,QAAQ,EAAE0Q,MAAM,KAAK1V,MAAA,CAAAuQ,kBAAkB,CAACqE,MAAM,CAAC3P,MAAM;UAAM4Q,MAAM,EAAN;;4BAC5D,MAEU,CAFV1V,YAAA,CAEUY,kBAAA;8BADR,MAAa,CAAbZ,YAAA,CAAaiB,oBAAA,E;;;;sEAGjBjB,YAAA,CAIY6F,oBAAA;UAJDE,IAAI,EAAC,OAAO;UAACzE,IAAI,EAAC,SAAS;UAAEkB,OAAK,EAAAnB,MAAA,IAAExB,MAAA,CAAAgW,iBAAiB,CAACZ,GAAG,EAAEM,MAAM;UAAGG,MAAM,EAAN;;4BAC7E,MAEU,CAFV1V,YAAA,CAEUY,kBAAA;8BADR,MAAQ,CAARZ,YAAA,CAAQiG,eAAA,E;;;;0DAGZjG,YAAA,CAIY6F,oBAAA;UAJDE,IAAI,EAAC,OAAO;UAACzE,IAAI,EAAC,QAAQ;UAAEkB,OAAK,EAAAnB,MAAA,IAAExB,MAAA,CAAAiW,qBAAqB,CAACP,MAAM;UAAGG,MAAM,EAAN;;4BAC3E,MAEU,CAFV1V,YAAA,CAEUY,kBAAA;8BADR,MAAU,CAAVZ,YAAA,CAAUoU,iBAAA,E;;;;;;;;qEA9HbvU,MAAA,CAAAiR,eAAe,qB;;8DA+JpClR,mBAAA,aAAgB,EAChBI,YAAA,CA2CYmP,oBAAA;gBA3CQtP,MAAA,CAAAkW,qBAAqB;iEAArBlW,MAAA,CAAAkW,qBAAqB,GAAA1U,MAAA;IAAEgO,KAAK,EAAC,SAAS;IAACC,KAAK,EAAC,OAAO;IAAE,sBAAoB,EAAE,KAAK;IAClG,cAAY,EAAEzP,MAAA,CAAAmW,oBAAoB;IAAExW,KAAK,EAAC,qBAAqB;IAAC,kBAAgB,EAAhB;;IA+BtD+P,MAAM,EAAAlP,QAAA,CACf,MAQM,CARNK,mBAAA,CAQM,OARNuV,YAQM,GAPJjW,YAAA,CAA0F6F,oBAAA;MAA9ErD,OAAK,EAAE3C,MAAA,CAAAmW,oBAAoB;MAAGnR,QAAQ,EAAEhF,MAAA,CAAAqW;;wBAAwB,MAAEzV,MAAA,UAAAA,MAAA,S,iBAAF,IAAE,E;;;gDAC9ET,YAAA,CAKY6F,oBAAA;MALDvE,IAAI,EAAC,SAAS;MAAEkB,OAAK,EAAE3C,MAAA,CAAAsW,mBAAmB;MAAG9R,OAAO,EAAExE,MAAA,CAAAqW;;wBAC/D,MAEU,C,CAFMrW,MAAA,CAAAqW,sBAAsB,I,cAAtCnS,YAAA,CAEUnD,kBAAA;QAAA+D,GAAA;MAAA;0BADR,MAAO,CAAP3E,YAAA,CAAO2I,cAAA,E;;gEACC,GACV,GAAA7H,gBAAA,CAAGjB,MAAA,CAAAqW,sBAAsB,sC;;;sBArC/B,MA4BM,CA5BNxV,mBAAA,CA4BM,OA5BN0V,YA4BM,G,8BA3BJ1V,mBAAA,CAGM;MAHDlB,KAAK,EAAC;IAAqB,IAC9BkB,mBAAA,CAAqB,YAAjB,cAAY,GAChBA,mBAAA,CAAmC,WAAhC,8BAA4B,E,sBAGjCA,mBAAA,CAoBM,OApBN2V,YAoBM,GAnBJrW,YAAA,CAMU8C,kBAAA;MAND,aAAW,EAAC;IAAO;wBAC1B,MAIe,CAJf9C,YAAA,CAIeqD,uBAAA;QAJD3B,KAAK,EAAC;MAAK;0BACvB,MAEuC,CAFvC1B,YAAA,CAEuC8D,mBAAA;sBAFpBjE,MAAA,CAAAyW,gBAAgB;uEAAhBzW,MAAA,CAAAyW,gBAAgB,GAAAjV,MAAA;UAAEC,IAAI,EAAC,UAAU;UAAE8L,IAAI,EAAE,CAAC;UAC3D5J,WAAW,EAAC,qCAAqC;UAAChE,KAAK,EAAC,oBAAoB;UAC3EqF,QAAQ,EAAEhF,MAAA,CAAAqW;;;;;sCAIjBxV,mBAAA,CAUM;MAVDlB,KAAK,EAAC;IAAsB,IAC/BkB,mBAAA,CAAe,YAAX,QAAM,GACVA,mBAAA,CAOM;MAPDlB,KAAK,EAAC;IAAc,IACvBkB,mBAAA,CAEM;MAFDlB,KAAK,EAAC;IAAc,IACvBkB,mBAAA,CAAuB,gBAAf,QAAM,G,iBAAS,uCACzB,E,GACAA,mBAAA,CAEM;MAFDlB,KAAK,EAAC;IAAc,IACvBkB,mBAAA,CAAsB,gBAAd,OAAK,G,iBAAS,4BACxB,E;;qDAoBVd,mBAAA,cAAiB,EACjBI,YAAA,CA8BYmP,oBAAA;gBA9BQtP,MAAA,CAAA0W,yBAAyB;iEAAzB1W,MAAA,CAAA0W,yBAAyB,GAAAlV,MAAA;IAAEgO,KAAK,EAAC,QAAQ;IAACC,KAAK,EAAC,QAAQ;IAAE,sBAAoB,EAAE,KAAK;IACvG9P,KAAK,EAAC,iBAAiB;IAAC,kBAAgB,EAAhB;;sBACxB,MA2BM,CA3BNkB,mBAAA,CA2BM,OA3BN8V,YA2BM,G,+BA1BJ9W,mBAAA,CAyBM,OAzBN+W,YAyBM,I,kBAxBJ/W,mBAAA,CAuBM6E,SAAA,QAAAC,WAAA,CAvBkB3E,MAAA,CAAA6W,gBAAgB,EAA5BC,QAAQ;2BAApBjX,mBAAA,CAuBM;QAvBqCiF,GAAG,EAAEgS,QAAQ,CAACC,GAAG;QAAEpX,KAAK,EAAC;UAClEkB,mBAAA,CASM;QATDlB,KAAK,EAAC,gBAAgB;QAAEgD,OAAK,EAAAnB,MAAA,IAAExB,MAAA,CAAAgX,qBAAqB,CAACF,QAAQ;UACrDA,QAAQ,CAACG,KAAK,I,cAAzBpX,mBAAA,CAC8B;;QADFqX,GAAG,EAAElX,MAAA,CAAAmX,mBAAmB,CAACL,QAAQ,CAACG,KAAK;QAAIG,GAAG,EAAEN,QAAQ,CAACO,KAAK;QACvFC,OAAK,EAAA1W,MAAA,SAAAA,MAAA,WAAA2W,IAAA,KAAEvX,MAAA,CAAAwX,gBAAA,IAAAxX,MAAA,CAAAwX,gBAAA,IAAAD,IAAA,CAAgB;+EAC1B1X,mBAAA,CAKM,OALN4X,YAKM,GAJJtX,YAAA,CAEUY,kBAAA;0BADR,MAAW,CAAXZ,YAAA,CAAWuX,kBAAA,E;;wCAEb7W,mBAAA,CAAiB,cAAX,MAAI,qB,mCAGdA,mBAAA,CAWM,OAXN8W,YAWM,GAVJ9W,mBAAA,CAAsE;QAAjE8B,OAAK,EAAAnB,MAAA,IAAExB,MAAA,CAAAgX,qBAAqB,CAACF,QAAQ;0BAAMA,QAAQ,CAACO,KAAK,wBAAAO,YAAA,GAC9D/W,mBAAA,CAAmD,KAAnDgX,YAAmD,EAA5B,QAAM,GAAA5W,gBAAA,CAAG6V,QAAQ,CAACC,GAAG,kBAC5ClW,mBAAA,CAOM,OAPNiX,YAOM,GANJ3X,YAAA,CAEY6F,oBAAA;QAFDvE,IAAI,EAAC,SAAS;QAACyE,IAAI,EAAC,OAAO;QAAEvD,OAAK,EAAAnB,MAAA,IAAExB,MAAA,CAAAgX,qBAAqB,CAACF,QAAQ;;0BAAG,MAEhF,KAAAlW,MAAA,UAAAA,MAAA,S,iBAFgF,QAEhF,E;;;wDACAT,YAAA,CAEY6F,oBAAA;QAFDvE,IAAI,EAAC,MAAM;QAACyE,IAAI,EAAC,OAAO;QAAEvD,OAAK,EAAAnB,MAAA,IAAExB,MAAA,CAAA+X,kBAAkB,CAACjB,QAAQ;;0BAAG,MAE1E,KAAAlW,MAAA,UAAAA,MAAA,S,iBAF0E,QAE1E,E;;;;6DArBmCZ,MAAA,CAAAgY,gBAAgB,E;;qCA6B/DjY,mBAAA,cAAiB,EACjBI,YAAA,CA8BYmP,oBAAA;gBA9BQtP,MAAA,CAAAiY,0BAA0B;iEAA1BjY,MAAA,CAAAiY,0BAA0B,GAAAzW,MAAA;IAAEgO,KAAK,EAAC,QAAQ;IAACC,KAAK,EAAC,QAAQ;IAAE,sBAAoB,EAAE,KAAK;IACxG9P,KAAK,EAAC,iBAAiB;IAAC,kBAAgB,EAAhB;;sBACxB,MA2BM,CA3BNkB,mBAAA,CA2BM,OA3BNqX,YA2BM,G,+BA1BJrY,mBAAA,CAyBM,OAzBNsY,YAyBM,I,kBAxBJtY,mBAAA,CAuBM6E,SAAA,QAAAC,WAAA,CAvBkB3E,MAAA,CAAAoY,iBAAiB,EAA7BtB,QAAQ;2BAApBjX,mBAAA,CAuBM;QAvBsCiF,GAAG,EAAEgS,QAAQ,CAACC,GAAG;QAAEpX,KAAK,EAAC;UACnEkB,mBAAA,CASM;QATDlB,KAAK,EAAC,gBAAgB;QAAEgD,OAAK,EAAAnB,MAAA,IAAExB,MAAA,CAAAqY,sBAAsB,CAACvB,QAAQ;UACtDA,QAAQ,CAACG,KAAK,I,cAAzBpX,mBAAA,CAC8B;;QADFqX,GAAG,EAAElX,MAAA,CAAAmX,mBAAmB,CAACL,QAAQ,CAACG,KAAK;QAAIG,GAAG,EAAEN,QAAQ,CAACO,KAAK;QACvFC,OAAK,EAAA1W,MAAA,SAAAA,MAAA,WAAA2W,IAAA,KAAEvX,MAAA,CAAAwX,gBAAA,IAAAxX,MAAA,CAAAwX,gBAAA,IAAAD,IAAA,CAAgB;+EAC1B1X,mBAAA,CAKM,OALNyY,YAKM,GAJJnY,YAAA,CAEUY,kBAAA;0BADR,MAAW,CAAXZ,YAAA,CAAWuX,kBAAA,E;;wCAEb7W,mBAAA,CAAiB,cAAX,MAAI,qB,mCAGdA,mBAAA,CAWM,OAXN0X,YAWM,GAVJ1X,mBAAA,CAAuE;QAAlE8B,OAAK,EAAAnB,MAAA,IAAExB,MAAA,CAAAqY,sBAAsB,CAACvB,QAAQ;0BAAMA,QAAQ,CAACO,KAAK,wBAAAmB,YAAA,GAC/D3X,mBAAA,CAAmD,KAAnD4X,YAAmD,EAA5B,QAAM,GAAAxX,gBAAA,CAAG6V,QAAQ,CAACC,GAAG,kBAC5ClW,mBAAA,CAOM,OAPN6X,YAOM,GANJvY,YAAA,CAEY6F,oBAAA;QAFDvE,IAAI,EAAC,SAAS;QAACyE,IAAI,EAAC,OAAO;QAAEvD,OAAK,EAAAnB,MAAA,IAAExB,MAAA,CAAAqY,sBAAsB,CAACvB,QAAQ;;0BAAG,MAEjF,KAAAlW,MAAA,UAAAA,MAAA,S,iBAFiF,QAEjF,E;;;wDACAT,YAAA,CAEY6F,oBAAA;QAFDvE,IAAI,EAAC,MAAM;QAACyE,IAAI,EAAC,OAAO;QAAEvD,OAAK,EAAAnB,MAAA,IAAExB,MAAA,CAAA+X,kBAAkB,CAACjB,QAAQ;;0BAAG,MAE1E,KAAAlW,MAAA,UAAAA,MAAA,S,iBAF0E,QAE1E,E;;;;6DArBmCZ,MAAA,CAAAgY,gBAAgB,E;;qCA6B/DjY,mBAAA,cAAiB,EACjBI,YAAA,CA2CYmP,oBAAA;gBA3CQtP,MAAA,CAAA2Y,wBAAwB;iEAAxB3Y,MAAA,CAAA2Y,wBAAwB,GAAAnX,MAAA;IAAEgO,KAAK,EAAC,MAAM;IAACC,KAAK,EAAC,KAAK;IAAE,sBAAoB,EAAE,KAAK;IACjG9P,KAAK,EAAC,wBAAwB;IAAC,kBAAgB,EAAhB,EAAgB;IAAC+Q,GAAG,EAAC;;IAqCzChB,MAAM,EAAAlP,QAAA,CACf,MAEM,CAFNK,mBAAA,CAEM,OAFN+X,YAEM,GADJzY,YAAA,CAAgF6F,oBAAA;MAApErD,OAAK,EAAA/B,MAAA,SAAAA,MAAA,OAAAY,MAAA,IAAExB,MAAA,CAAA2Y,wBAAwB;MAAUzS,IAAI,EAAC;;wBAAQ,MAAEtF,MAAA,UAAAA,MAAA,S,iBAAF,IAAE,E;;;;sBAtCxE,MAmCM,CAnCuCZ,MAAA,CAAA6Y,qBAAqB,I,cAAlEhZ,mBAAA,CAmCM,OAnCNiZ,YAmCM,GAlCJjY,mBAAA,CAGM,OAHNkY,YAGM,GAFJlY,mBAAA,CAA0C,YAAAI,gBAAA,CAAnCjB,MAAA,CAAA6Y,qBAAqB,CAACxB,KAAK,kBAClCxW,mBAAA,CAAuE,KAAvEmY,YAAuE,EAAzC,QAAM,GAAA/X,gBAAA,CAAGjB,MAAA,CAAA6Y,qBAAqB,CAAC9B,GAAG,iB,GAElElW,mBAAA,CAkBM,OAlBNoY,YAkBM,GAjBOjZ,MAAA,CAAA6Y,qBAAqB,CAAC5B,KAAK,I,cAAtCpX,mBAAA,CAUM,OAVNqZ,YAUM,GATJ/Y,YAAA,CAQEgZ,mBAAA;MAPCjC,GAAG,EAAElX,MAAA,CAAAmX,mBAAmB,CAACnX,MAAA,CAAA6Y,qBAAqB,CAAC5B,KAAK;MACpDG,GAAG,EAAEpX,MAAA,CAAA6Y,qBAAqB,CAACxB,KAAK;MACjC+B,GAAG,EAAC,SAAS;MACZ,kBAAgB,GAAGpZ,MAAA,CAAAmX,mBAAmB,CAACnX,MAAA,CAAA6Y,qBAAqB,CAAC5B,KAAK;MAClE,eAAa,EAAE,CAAC;MACjB,oBAAkB,EAAlB,EAAkB;MAClBtX,KAAK,EAAC;oFAGVE,mBAAA,CAKM,OALNwZ,YAKM,GAJJlZ,YAAA,CAEUY,kBAAA;wBADR,MAAW,CAAXZ,YAAA,CAAWuX,kBAAA,E;;sCAEb7W,mBAAA,CAAmB,cAAb,QAAM,qB,MAGhBA,mBAAA,CAUM,OAVNyY,YAUM,GATJnZ,YAAA,CAQWoZ,mBAAA;MAPT/J,KAAK,EAAC,IAAI;MACV/N,IAAI,EAAC,MAAM;MACV+X,QAAQ,EAAE,KAAK;MAChB,WAAS,EAAT;;MACWrE,OAAO,EAAA3U,QAAA,CAAC,MAEnBI,MAAA,UAAAA,MAAA,S,iBAFmB,wBAEnB,E;;;;qCAWRb,mBAAA,YAAe,EACfI,YAAA,CAyCYmP,oBAAA;gBAzCQtP,MAAA,CAAAyZ,uBAAuB;iEAAvBzZ,MAAA,CAAAyZ,uBAAuB,GAAAjY,MAAA;IAAEgO,KAAK,EAAC,MAAM;IAACC,KAAK,EAAC,OAAO;IAAE,sBAAoB,EAAE,KAAK;IAClG9P,KAAK,EAAC,uBAAuB;IAAC,kBAAgB,EAAhB;;IAkCnB+P,MAAM,EAAAlP,QAAA,CACf,MAGM,CAHNK,mBAAA,CAGM,OAHN6Y,YAGM,GAFJvZ,YAAA,CAA2D6F,oBAAA;MAA/CrD,OAAK,EAAE3C,MAAA,CAAA2Z;IAAwB;wBAAE,MAAE/Y,MAAA,UAAAA,MAAA,S,iBAAF,IAAE,E;;;oCAC/CT,YAAA,CAAmE6F,oBAAA;MAAxDvE,IAAI,EAAC,SAAS;MAAEkB,OAAK,EAAE3C,MAAA,CAAA4Z;;wBAAmB,MAAEhZ,MAAA,UAAAA,MAAA,S,iBAAF,IAAE,E;;;;sBApC3D,MA+BM,CA/BsCZ,MAAA,CAAA6Z,oBAAoB,I,cAAhEha,mBAAA,CA+BM,OA/BNia,YA+BM,GA9BJ3Z,YAAA,CA6BU8C,kBAAA;MA7BAC,KAAK,EAAElD,MAAA,CAAA6Z,oBAAoB;MAAE,aAAW,EAAC,OAAO;MAAC3T,IAAI,EAAC;;wBAC9D,MAEe,CAFf/F,YAAA,CAEeqD,uBAAA;QAFD3B,KAAK,EAAC;MAAM;0BACxB,MAAgE,CAAhE1B,YAAA,CAAgE8D,mBAAA;sBAA7CjE,MAAA,CAAA6Z,oBAAoB,CAACtI,WAAW;uEAAhCvR,MAAA,CAAA6Z,oBAAoB,CAACtI,WAAW,GAAA/P,MAAA;UAAE4D,QAAQ,EAAR;;;UAGvDjF,YAAA,CAYeqD,uBAAA;QAZD3B,KAAK,EAAC;MAAK;0BACvB,MAUW,CAVX1B,YAAA,CAUW8D,mBAAA;sBAVQjE,MAAA,CAAA6Z,oBAAoB,CAACE,YAAY;uEAAjC/Z,MAAA,CAAA6Z,oBAAoB,CAACE,YAAY,GAAAvY,MAAA;UAClDmC,WAAW,EAAC,0BAA0B;UACrChB,OAAK,EAAE3C,MAAA,CAAAga,wBAAwB;UAChC5U,QAAQ,EAAR,EAAQ;UACRxF,KAAwB,EAAxB;YAAA;UAAA;;UACWqa,MAAM,EAAAzZ,QAAA,CACf,MAEU,CAFVL,YAAA,CAEUY,kBAAA;YAFDnB,KAAwB,EAAxB;cAAA;YAAA;UAAwB;8BAC/B,MAAQ,CAARO,YAAA,CAAQ+Z,eAAA,E;;;;;;UAMhB/Z,YAAA,CAEeqD,uBAAA;QAFD3B,KAAK,EAAC;MAAM;0BACxB,MAA4D,CAA5D1B,YAAA,CAA4DgS,sBAAA;sBAAtCnS,MAAA,CAAAma,gBAAgB;uEAAhBna,MAAA,CAAAma,gBAAgB,GAAA3Y,MAAA;;4BAAE,MAAMZ,MAAA,UAAAA,MAAA,S,iBAAN,QAAM,E;;;;;UAGhDT,YAAA,CAKeqD,uBAAA;QALD3B,KAAK,EAAC;MAAO;0BACzB,MAGqD,CAHrD1B,YAAA,CAGqD8D,mBAAA;sBAHlCjE,MAAA,CAAA6Z,oBAAoB,CAACO,aAAa;uEAAlCpa,MAAA,CAAA6Z,oBAAoB,CAACO,aAAa,GAAA5Y,MAAA;UACnDC,IAAI,EAAC,UAAU;UACd8L,IAAI,EAAE,CAAC;UACR5J,WAAW,EAAC;;;;;;;qCAatB5D,mBAAA,aAAgB,EAChBI,YAAA,CA0BYmP,oBAAA;gBA1BQtP,MAAA,CAAAqa,sBAAsB;iEAAtBra,MAAA,CAAAqa,sBAAsB,GAAA7Y,MAAA;IAAEgO,KAAK,EAAC,OAAO;IAACC,KAAK,EAAC,OAAO;IAAE,sBAAoB,EAAE,KAAK;IAClG9P,KAAK,EAAC,sBAAsB;IAAC,kBAAgB,EAAhB;;IAmBlB+P,MAAM,EAAAlP,QAAA,CACf,MAGM,CAHNK,mBAAA,CAGM,OAHNyZ,YAGM,GAFJna,YAAA,CAA0D6F,oBAAA;MAA9CrD,OAAK,EAAE3C,MAAA,CAAAua;IAAuB;wBAAE,MAAE3Z,MAAA,UAAAA,MAAA,S,iBAAF,IAAE,E;;;oCAC9CT,YAAA,CAA8E6F,oBAAA;MAAnEvE,IAAI,EAAC,SAAS;MAAEkB,OAAK,EAAE3C,MAAA,CAAAwa;;wBAA8B,MAAE5Z,MAAA,UAAAA,MAAA,S,iBAAF,IAAE,E;;;;sBArBtE,MAgBM,CAhBNC,mBAAA,CAgBM,OAhBN4Z,YAgBM,GAfJta,YAAA,CAcW2U,mBAAA;MAdA1I,IAAI,EAAEpM,MAAA,CAAA0a,sBAAsB;MAAE3F,MAAM,EAAN,EAAM;MAACC,MAAM,EAAN,EAAM;MAAC9O,IAAI,EAAC,OAAO;MAAC,YAAU,EAAC;;wBAC7E,MAA4E,CAA5E/F,YAAA,CAA4E8U,0BAAA;QAA3DpT,KAAK,EAAC,MAAM;QAACqO,IAAI,EAAC,YAAY;QAACT,KAAK,EAAC,IAAI;QAACyF,KAAK,EAAC;UACjE/U,YAAA,CAA6D8U,0BAAA;QAA5CpT,KAAK,EAAC,IAAI;QAACqO,IAAI,EAAC,aAAa;QAACT,KAAK,EAAC;UACrDtP,YAAA,CAA4D8U,0BAAA;QAA3CpT,KAAK,EAAC,KAAK;QAACqO,IAAI,EAAC,WAAW;QAACT,KAAK,EAAC;UACpDtP,YAAA,CAIkB8U,0BAAA;QAJDpT,KAAK,EAAC,IAAI;QAAC4N,KAAK,EAAC,KAAK;QAACyF,KAAK,EAAC;;QACjCC,OAAO,EAAA3U,QAAA,CAChB,CAAsE;UADlD4U;QAAG,OACvBjV,YAAA,CAAsE8D,mBAAA;sBAAnDmR,GAAG,CAACuF,SAAS;2CAAbvF,GAAG,CAACuF,SAAS,GAAAnZ,MAAA;UAAE0E,IAAI,EAAC,OAAO;UAACtG,KAAoB,EAApB;YAAA;UAAA;;;UAGnDO,YAAA,CAIkB8U,0BAAA;QAJDpT,KAAK,EAAC,IAAI;QAAC4N,KAAK,EAAC,IAAI;QAACyF,KAAK,EAAC;;QAChCC,OAAO,EAAA3U,QAAA,CAChB,CAAsC;UADlB4U;QAAG,OACvBjV,YAAA,CAAsCgS,sBAAA;sBAAhBiD,GAAG,CAACwF,QAAQ;2CAAZxF,GAAG,CAACwF,QAAQ,GAAApZ", "ignoreList": []}]}