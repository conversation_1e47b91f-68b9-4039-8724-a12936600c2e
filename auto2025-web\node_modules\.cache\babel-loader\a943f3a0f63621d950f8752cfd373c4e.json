{"remainingRequest": "J:\\auto2025\\auto2025-web\\node_modules\\babel-loader\\lib\\index.js!J:\\auto2025\\auto2025-web\\src\\router\\index.js", "dependencies": [{"path": "J:\\auto2025\\auto2025-web\\src\\router\\index.js", "mtime": 1749464307874}, {"path": "J:\\auto2025\\auto2025-web\\babel.config.js", "mtime": 1748614864000}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748675476717}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748675484569}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["createRouter", "createWebHistory", "routes", "path", "name", "component", "meta", "requireAuth", "redirect", "children", "title", "requiresAuth", "router", "history", "process", "env", "BASE_URL", "beforeEach", "to", "from", "next", "sessionStorage", "removeItem", "currentUser", "getItem", "console", "log"], "sources": ["J:/auto2025/auto2025-web/src/router/index.js"], "sourcesContent": ["import { createRouter, createWebHistory } from 'vue-router'\r\n\r\nconst routes = [\r\n  {\r\n    path: '/adminlogin',\r\n    name: 'Login',\r\n    component: () => import('../views/Login'),\r\n    meta: {\r\n      requireAuth: false\r\n    }\r\n  },\r\n\r\n  {\r\n    path: '/main',\r\n    name: 'Main',\r\n    component: () => import('../views/Main'),\r\n    redirect: \"/home\",\r\n    children: [\r\n      {\r\n        path: '/home',\r\n        name: 'Home',\r\n        component: () => import('../views/admin/Home'),\r\n        meta: {\r\n          requireAuth: true,title:'首页'\r\n        }\r\n\r\n      },\r\n  \r\n\r\n      {\r\n      path: '/bigAdd',\r\n      name: 'BigAdd',\r\n      component: () => import('../views/admin/big/BigAdd'),\r\n      meta: { requiresAuth: true,title: '大类添加' }\r\n    },\r\n {\r\n      path: '/bigEdit',\r\n      name: 'BigEdit',\r\n      component: () => import('../views/admin/big/BigEdit'),\r\n      meta: { requiresAuth: true,title: '大类修改' }\r\n    },\r\n {\r\n      path: '/bigManage',\r\n      name: 'BigManage',\r\n      component: () => import('../views/admin/big/BigManage'),\r\n      meta: { requiresAuth: true,title: '大类管理' }\r\n    },\r\n{\r\n    path: '/bigDetail',\r\n    name: 'BigDetail',\r\n    component: () => import('../views/admin/big/BigDetail'),\r\n    meta: { requiresAuth: true,title: '大类详情' }\r\n  },\r\n{\r\n      path: '/moresAdd',\r\n      name: 'MoresAdd',\r\n      component: () => import('../views/admin/mores/MoresAdd'),\r\n      meta: { requiresAuth: true,title: '字段添加' }\r\n    },\r\n {\r\n      path: '/moresEdit',\r\n      name: 'MoresEdit',\r\n      component: () => import('../views/admin/mores/MoresEdit'),\r\n      meta: { requiresAuth: true,title: '字段修改' }\r\n    },\r\n {\r\n      path: '/moresManage',\r\n      name: 'MoresManage',\r\n      component: () => import('../views/admin/mores/MoresManage'),\r\n      meta: { requiresAuth: true,title: '字段管理' }\r\n    },\r\n{\r\n    path: '/moresDetail',\r\n    name: 'MoresDetail',\r\n    component: () => import('../views/admin/mores/MoresDetail'),\r\n    meta: { requiresAuth: true,title: '字段详情' }\r\n  },\r\n{\r\n      path: '/tablesAdd',\r\n      name: 'TablesAdd',\r\n      component: () => import('../views/admin/tables/TablesAdd'),\r\n      meta: { requiresAuth: true,title: '表名添加' }\r\n    },\r\n {\r\n      path: '/tablesEdit',\r\n      name: 'TablesEdit',\r\n      component: () => import('../views/admin/tables/TablesEdit'),\r\n      meta: { requiresAuth: true,title: '表名修改' }\r\n    },\r\n {\r\n      path: '/tablesManage',\r\n      name: 'TablesManage',\r\n      component: () => import('../views/admin/tables/TablesManage'),\r\n      meta: { requiresAuth: true,title: '表名管理' }\r\n    },\r\n{\r\n    path: '/tablesDetail',\r\n    name: 'TablesDetail',\r\n    component: () => import('../views/admin/tables/TablesDetail'),\r\n    meta: { requiresAuth: true,title: '表名详情' }\r\n  },\r\n{\r\n      path: '/projectsAdd',\r\n      name: 'ProjectsAdd',\r\n      component: () => import('../views/admin/projects/ProjectsAdd'),\r\n      meta: { requiresAuth: true,title: '项目添加' }\r\n    },\r\n {\r\n      path: '/projectsEdit',\r\n      name: 'ProjectsEdit',\r\n      component: () => import('../views/admin/projects/ProjectsEdit'),\r\n      meta: { requiresAuth: true,title: '项目修改' }\r\n    },\r\n {\r\n      path: '/projectsManage',\r\n      name: 'ProjectsManage',\r\n      component: () => import('../views/admin/projects/ProjectsManage'),\r\n      meta: { requiresAuth: true,title: '项目管理' }\r\n    },\r\n{\r\n    path: '/projectsDetail',\r\n    name: 'ProjectsDetail',\r\n    component: () => import('../views/admin/projects/ProjectsDetail'),\r\n    meta: { requiresAuth: true,title: '项目详情' }\r\n  },\r\n{\r\n      path: '/adminAdd',\r\n      name: 'AdminAdd',\r\n      component: () => import('../views/admin/admin/AdminAdd'),\r\n      meta: { requiresAuth: true,title: '管理员添加' }\r\n    },\r\n {\r\n      path: '/adminEdit',\r\n      name: 'AdminEdit',\r\n      component: () => import('../views/admin/admin/AdminEdit'),\r\n      meta: { requiresAuth: true,title: '管理员修改' }\r\n    },\r\n {\r\n      path: '/adminManage',\r\n      name: 'AdminManage',\r\n      component: () => import('../views/admin/admin/AdminManage'),\r\n      meta: { requiresAuth: true,title: '管理员管理' }\r\n    },\r\n{\r\n      path: '/smallAdd',\r\n      name: 'SmallAdd',\r\n      component: () => import('../views/admin/small/SmallAdd'),\r\n      meta: { requiresAuth: true,title: '小类添加' }\r\n    },\r\n {\r\n      path: '/smallEdit',\r\n      name: 'SmallEdit',\r\n      component: () => import('../views/admin/small/SmallEdit'),\r\n      meta: { requiresAuth: true,title: '小类修改' }\r\n    },\r\n {\r\n      path: '/smallManage',\r\n      name: 'SmallManage',\r\n      component: () => import('../views/admin/small/SmallManage'),\r\n      meta: { requiresAuth: true,title: '小类管理' }\r\n    },\r\n{\r\n    path: '/smallDetail',\r\n    name: 'SmallDetail',\r\n    component: () => import('../views/admin/small/SmallDetail'),\r\n    meta: { requiresAuth: true,title: '小类详情' }\r\n  },\r\n{\r\n    path: '/total1',\r\n    name: 'Total1',\r\n    component: () => import('../views/admin/total/Total1'),\r\n    meta: { requiresAuth: true,title: '图表1' }\r\n  },\r\n{\r\n    path: '/total2',\r\n    name: 'Total2',\r\n    component: () => import('../views/admin/total/Total2'),\r\n    meta: { requiresAuth: true,title: '图表2' }\r\n  },\r\n{\r\n    path: '/total3',\r\n    name: 'Total3',\r\n    component: () => import('../views/admin/total/Total3'),\r\n    meta: { requiresAuth: true,title: '图表3' }\r\n  },\r\n{\r\n    path: '/total4',\r\n    name: 'Total4',\r\n    component: () => import('../views/admin/total/Total4'),\r\n    meta: { requiresAuth: true,title: '图表4' }\r\n  },\r\n\r\n     {\r\n          path: '/password',\r\n          name: 'Password',\r\n          component: () => import('../views/admin/system/Password'),\r\n          meta: {\r\n            requireAuth: true,title:'修改密码'\r\n          }\r\n     },\r\n    ]\r\n  },\r\n  {\r\n    path: '/',\r\n    name: '/',\r\n    component: () => import('../views/Index'),\r\n    redirect: \"/codeGenerator\",\r\n    children: [\r\n    {\r\n        path: '/codeGenerator',\r\n        name: 'codeGenerator',\r\n        component: () => import('../views/web/CodeGenerator'),\r\n        meta: {\r\n          requireAuth: true,title:'代码生成器'\r\n        }\r\n    },\r\n    ]\r\n},\r\n\r\n]\r\n\r\n\r\n\r\nconst router = createRouter({\r\n  history: createWebHistory(process.env.BASE_URL),\r\n  routes\r\n})\r\n\r\n\r\n\r\nrouter.beforeEach((to, from, next) => {\r\n  if (to.path == '/') {\r\n    sessionStorage.removeItem('userLname');\r\n    sessionStorage.removeItem('role');\r\n  }\r\n  let currentUser = sessionStorage.getItem('userLname');\r\n  console.log(to + \"  to.meta.requireAuth\");\r\n\r\n  if (to.meta.requireAuth) {\r\n    if (!currentUser && to.path != '/login') {\r\n      next({ path: '/' });\r\n    } else {\r\n      next();\r\n    }\r\n  } else {\r\n\r\n    next();\r\n  }\r\n})\r\n\r\nexport default router\r\n\r\n\r\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AAE3D,MAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,aAAa;EACnBC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gBAAgB,CAAC;EACzCC,IAAI,EAAE;IACJC,WAAW,EAAE;EACf;AACF,CAAC,EAED;EACEJ,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,eAAe,CAAC;EACxCG,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE,CACR;IACEN,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC;IAC9CC,IAAI,EAAE;MACJC,WAAW,EAAE,IAAI;MAACG,KAAK,EAAC;IAC1B;EAEF,CAAC,EAGD;IACAP,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC;IACpDC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACJ;IACKP,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,SAAS;IACfC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC;IACrDC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACJ;IACKP,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,8BAA8B,CAAC;IACvDC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACL;IACIP,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,8BAA8B,CAAC;IACvDC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACH;IACMP,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC;IACxDC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACJ;IACKP,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC;IACzDC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACJ;IACKP,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC;IAC3DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACL;IACIP,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC;IAC3DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACH;IACMP,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC;IAC1DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACJ;IACKP,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,YAAY;IAClBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC;IAC3DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACJ;IACKP,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC;IAC7DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACL;IACIP,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC;IAC7DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACH;IACMP,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC;IAC9DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACJ;IACKP,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,cAAc;IACpBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC;IAC/DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACJ;IACKP,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE,gBAAgB;IACtBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC;IACjEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACL;IACIP,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE,gBAAgB;IACtBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC;IACjEC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACH;IACMP,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC;IACxDC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAQ;EAC5C,CAAC,EACJ;IACKP,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC;IACzDC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAQ;EAC5C,CAAC,EACJ;IACKP,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC;IAC3DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAQ;EAC5C,CAAC,EACL;IACMP,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC;IACxDC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACJ;IACKP,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC;IACzDC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACJ;IACKP,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC;IAC3DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACL;IACIP,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC;IAC3DC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAO;EAC3C,CAAC,EACH;IACIP,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC;IACtDC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAM;EAC1C,CAAC,EACH;IACIP,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC;IACtDC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAM;EAC1C,CAAC,EACH;IACIP,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC;IACtDC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAM;EAC1C,CAAC,EACH;IACIP,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC;IACtDC,IAAI,EAAE;MAAEK,YAAY,EAAE,IAAI;MAACD,KAAK,EAAE;IAAM;EAC1C,CAAC,EAEE;IACKP,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC;IACzDC,IAAI,EAAE;MACJC,WAAW,EAAE,IAAI;MAACG,KAAK,EAAC;IAC1B;EACL,CAAC;AAEJ,CAAC,EACD;EACEP,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,GAAG;EACTC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,gBAAgB,CAAC;EACzCG,QAAQ,EAAE,gBAAgB;EAC1BC,QAAQ,EAAE,CACV;IACIN,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC;IACrDC,IAAI,EAAE;MACJC,WAAW,EAAE,IAAI;MAACG,KAAK,EAAC;IAC1B;EACJ,CAAC;AAEL,CAAC,CAEA;AAID,MAAME,MAAM,GAAGZ,YAAY,CAAC;EAC1Ba,OAAO,EAAEZ,gBAAgB,CAACa,OAAO,CAACC,GAAG,CAACC,QAAQ,CAAC;EAC/Cd;AACF,CAAC,CAAC;AAIFU,MAAM,CAACK,UAAU,CAAC,CAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,KAAK;EACpC,IAAIF,EAAE,CAACf,IAAI,IAAI,GAAG,EAAE;IAClBkB,cAAc,CAACC,UAAU,CAAC,WAAW,CAAC;IACtCD,cAAc,CAACC,UAAU,CAAC,MAAM,CAAC;EACnC;EACA,IAAIC,WAAW,GAAGF,cAAc,CAACG,OAAO,CAAC,WAAW,CAAC;EACrDC,OAAO,CAACC,GAAG,CAACR,EAAE,GAAG,uBAAuB,CAAC;EAEzC,IAAIA,EAAE,CAACZ,IAAI,CAACC,WAAW,EAAE;IACvB,IAAI,CAACgB,WAAW,IAAIL,EAAE,CAACf,IAAI,IAAI,QAAQ,EAAE;MACvCiB,IAAI,CAAC;QAAEjB,IAAI,EAAE;MAAI,CAAC,CAAC;IACrB,CAAC,MAAM;MACLiB,IAAI,CAAC,CAAC;IACR;EACF,CAAC,MAAM;IAELA,IAAI,CAAC,CAAC;EACR;AACF,CAAC,CAAC;AAEF,eAAeR,MAAM", "ignoreList": []}]}