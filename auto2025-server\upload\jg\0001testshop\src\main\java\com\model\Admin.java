package com.model;

import java.util.Date;
import java.math.BigDecimal;
import java.io.Serializable;

/**
 * 管理员实体类
 *
 * 功能说明：
 * 1. 对应数据库表：admin
 * 2. 继承ComData基类，包含通用字段和方法
 * 3. 实现Serializable接口，支持序列化
 */
public class Admin extends ComData{

	/** 序列化版本号 */
	private static final long serialVersionUID = 1L;

	// ==================== 字段定义 ====================
	/** 管理员ID */
	private Integer id;

	/** 账号 */
	private String lname;

	/** 登录密码 */
	private String password;

	/** 婚姻状况 */
	private String marrys;



	// ==================== 构造方法 ====================

	/**
	 * 默认构造方法
	 */
	public Admin()
	{
		super();
	}

	// ==================== Getter和Setter方法 ====================
		public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getLname() {
		return lname;
	}

	public void setLname(String lname) {
		this.lname = lname;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getMarrys() {
		return marrys;
	}

	public void setMarrys(String marrys) {
		this.marrys = marrys;
	}



}
