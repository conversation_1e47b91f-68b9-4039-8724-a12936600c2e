<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
<head th:replace="@{/admin/head.html}"></head>
  
</head>

<body>
    <div class="content-area" style="height: 100%; padding: 20px;">
        <h4 class="page-title">管理产品分类</h4>

        <div>
        <div class="container-fluid">
    <div class="card mb-3">
        <div class="card-body">
            <form class="search-form" method="get">
                <div class="row g-3 align-items-end">
                    <div class="col-md-3">
                        <label for="search_cname" class="form-label">分类名称</label>
                        <input type="text" class="form-control" id="search_cname" name="cname" placeholder="请输入分类名称" th:value="${param.cname}">
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-search"></i> 搜索
                        </button>
                        <a href="?" class="btn btn-secondary">
                            <i class="bi bi-arrow-clockwise"></i> 重置
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">产品分类列表</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="thead-light">
                        <tr>
                    <th>分类名称</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr th:each="item : ${list}">
                    <td th:text="${item.cname}"></td>
                    <td>
                        <a th:href="@{/productcategoryToEdit(id=${item.cid})}" class="btn btn-sm btn-primary me-1">
                            <i class="bi bi-pencil-square"></i> 编辑
                        </a>
                        <a href="javascript:void(0)" th:data-id="${item.cid}" onclick="deleteData('productcategoryDel',this)" class="btn btn-sm btn-danger">
                            <i class="bi bi-trash3"></i> 删除
                        </a>
                    </td>
                        </tr>
                        <tr th:if="${#lists.isEmpty(list)}">
                            <td colspan="100%" class="text-center text-muted">暂无数据</td>
                        </tr>
                    </tbody>
                </table>
            
            <!-- 分页区域 -->
            <nav aria-label="分页导航" th:if="${totalPages > 1}">
                <ul class="pagination justify-content-center">
                    <li class="page-item" th:classappend="${currentPage == 1} ? 'disabled'">
                        <a class="page-link" th:href="@{''(page=${currentPage - 1})}">上一页</a>
                    </li>
                    <li class="page-item" th:each="i : ${#numbers.sequence(1, totalPages)}" 
                        th:classappend="${i == currentPage} ? 'active'">
                        <a class="page-link" th:href="@{''(page=${i})}" th:text="${i}"></a>
                    </li>
                    <li class="page-item" th:classappend="${currentPage == totalPages} ? 'disabled'">
                        <a class="page-link" th:href="@{''(page=${currentPage + 1})}">下一页</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
</div>
        <script>
        // 分页功能
        function goToPage(page) {
            var url = new URL(window.location);
            url.searchParams.set('page', page);
            window.location.href = url.toString();
        }
        
        // 图片预览函数
        function previewImage(src) {
            var modal = '<div class="modal fade" id="imagePreviewModal" tabindex="-1">' +
                       '<div class="modal-dialog modal-lg modal-dialog-centered">' +
                       '<div class="modal-content">' +
                       '<div class="modal-header">' +
                       '<h5 class="modal-title">图片预览</h5>' +
                       '<button type="button" class="btn-close" data-bs-dismiss="modal"></button>' +
                       '</div>' +
                       '<div class="modal-body text-center">' +
                       '<img src="' + src + '" class="img-fluid" style="max-height: 70vh;">' +
                       '</div>' +
                       '</div></div></div>';
            
            $('body').append(modal);
            var modalInstance = new bootstrap.Modal(document.getElementById('imagePreviewModal'));
            modalInstance.show();
            
            $('#imagePreviewModal').on('hidden.bs.modal', function() {
                $(this).remove();
            });
        }
        </script>

        </div>
    </div>

    <!-- Bootstrap JS -->
</body>

</html>