{"remainingRequest": "J:\\auto2025\\auto2025-web\\node_modules\\babel-loader\\lib\\index.js!J:\\auto2025\\auto2025-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!J:\\auto2025\\auto2025-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!J:\\auto2025\\auto2025-web\\src\\views\\admin\\small\\SmallDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "J:\\auto2025\\auto2025-web\\src\\views\\admin\\small\\SmallDetail.vue", "mtime": 1749439666296}, {"path": "J:\\auto2025\\auto2025-web\\babel.config.js", "mtime": 1748614864000}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748675476717}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748675484569}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748675476717}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1748675485115}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHJlcXVlc3QsIHsgYmFzZSB9IGZyb20gIi4uLy4uLy4uLy4uL3V0aWxzL2h0dHAiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1NtYWxsRGV0YWlsJywKICBjb21wb25lbnRzOiB7fSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgaWQ6ICcnLAogICAgICBmb3JtRGF0YToge30gLy/ooajljZXmlbDmja4gICAgICAgICAKICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5pZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5LmlkOyAvL+iOt+WPluWPguaVsAogICAgdGhpcy5nZXREYXRhcygpOwogIH0sCiAgbWV0aG9kczogewogICAgLy/ojrflj5bliJfooajmlbDmja4KICAgIGdldERhdGFzKCkgewogICAgICBsZXQgcGFyYSA9IHt9OwogICAgICB0aGlzLmxpc3RMb2FkaW5nID0gdHJ1ZTsKICAgICAgbGV0IHVybCA9IGJhc2UgKyAiL3NtYWxsL2dldD9pZD0iICsgdGhpcy5pZDsKICAgICAgcmVxdWVzdC5wb3N0KHVybCwgcGFyYSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuZm9ybURhdGEgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHJlcy5yZXNkYXRhKSk7CiAgICAgICAgdGhpcy5saXN0TG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDov5Tlm54KICAgIGJhY2soKSB7CiAgICAgIC8v6L+U5Zue5LiK5LiA6aG1CiAgICAgIHRoaXMuJHJvdXRlci5nbygtMSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["request", "base", "name", "components", "data", "id", "formData", "created", "$route", "query", "getDatas", "methods", "para", "listLoading", "url", "post", "then", "res", "JSON", "parse", "stringify", "resdata", "back", "$router", "go"], "sources": ["J:\\auto2025\\auto2025-web\\src\\views\\admin\\small\\SmallDetail.vue"], "sourcesContent": ["<template>\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n       <el-form :model=\"formData\" label-width=\"20%\" align=\"left\">\r\n<el-form-item label=\"小类ID\">\r\n{{formData.sid}}</el-form-item>\r\n<el-form-item label=\"大类\">\r\n{{formData.bname}}</el-form-item>\r\n<el-form-item label=\"小类名称\">\r\n{{formData.sname}}</el-form-item>\r\n<el-form-item label=\"内容1\">\r\n{{formData.memo1}}</el-form-item>\r\n<el-form-item label=\"内容2\">\r\n{{formData.memo2}}</el-form-item>\r\n<el-form-item label=\"内容3\">\r\n{{formData.memo3}}</el-form-item>\r\n<el-form-item label=\"内容4\" prop=\"memo4\">\r\n<div v-html=\"formData.memo4\"></div>\r\n</el-form-item>\r\n<el-form-item label=\"备用1\">\r\n{{formData.by1}}</el-form-item>\r\n<el-form-item label=\"备用2\">\r\n{{formData.by2}}</el-form-item>\r\n<el-form-item label=\"备用3\">\r\n{{formData.by3}}</el-form-item>\r\n<el-form-item label=\"备用4\">\r\n{{formData.by4}}</el-form-item>\r\n<el-form-item label=\"备用5\">\r\n{{formData.by5}}</el-form-item>\r\n<el-form-item label=\"备用6\">\r\n{{formData.by6}}</el-form-item>\r\n<el-form-item label=\"备用7\">\r\n{{formData.by7}}</el-form-item>\r\n<el-form-item label=\"备用8\">\r\n{{formData.by8}}</el-form-item>\r\n<el-form-item label=\"备用9\">\r\n{{formData.by9}}</el-form-item>\r\n<el-form-item label=\"备用10\">\r\n{{formData.by10}}</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"info\" size=\"small\" @click=\"back\" icon=\"el-icon-back\">返 回</el-button>\r\n</el-form-item>\r\n</el-form>\r\n\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n        \r\n        import request, { base } from \"../../../../utils/http\";\r\n        export default {\r\n            name: 'SmallDetail',\r\n            components: {\r\n            },\r\n            data() {\r\n                return {\r\n                    id: '',\r\n                    formData: {}, //表单数据         \r\n        \r\n                };\r\n            },\r\n            created() {\r\n                this.id = this.$route.query.id; //获取参数\r\n                this.getDatas();\r\n            },\r\n        \r\n        \r\n            methods: {\r\n        \r\n                //获取列表数据\r\n                getDatas() {\r\n                    let para = {\r\n                    };\r\n                    this.listLoading = true;\r\n                    let url = base + \"/small/get?id=\" + this.id;\r\n                    request.post(url, para).then((res) => {\r\n                        this.formData = JSON.parse(JSON.stringify(res.resdata));\r\n                        this.listLoading = false;\r\n                    });\r\n                },\r\n        \r\n                // 返回\r\n                back() {\r\n                    //返回上一页\r\n                    this.$router.go(-1);\r\n                },\r\n        \r\n            },\r\n        }\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"], "mappings": "AAiDQ,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,eAAe;EACXC,IAAI,EAAE,aAAa;EACnBC,UAAU,EAAE,CACZ,CAAC;EACDC,IAAIA,CAAA,EAAG;IACH,OAAO;MACHC,EAAE,EAAE,EAAE;MACNC,QAAQ,EAAE,CAAC,CAAC,CAAE;IAElB,CAAC;EACL,CAAC;EACDC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACF,EAAC,GAAI,IAAI,CAACG,MAAM,CAACC,KAAK,CAACJ,EAAE,EAAE;IAChC,IAAI,CAACK,QAAQ,CAAC,CAAC;EACnB,CAAC;EAGDC,OAAO,EAAE;IAEL;IACAD,QAAQA,CAAA,EAAG;MACP,IAAIE,IAAG,GAAI,CACX,CAAC;MACD,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAIC,GAAE,GAAIb,IAAG,GAAI,gBAAe,GAAI,IAAI,CAACI,EAAE;MAC3CL,OAAO,CAACe,IAAI,CAACD,GAAG,EAAEF,IAAI,CAAC,CAACI,IAAI,CAAEC,GAAG,IAAK;QAClC,IAAI,CAACX,QAAO,GAAIY,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC;QACvD,IAAI,CAACR,WAAU,GAAI,KAAK;MAC5B,CAAC,CAAC;IACN,CAAC;IAED;IACAS,IAAIA,CAAA,EAAG;MACH;MACA,IAAI,CAACC,OAAO,CAACC,EAAE,CAAC,CAAC,CAAC,CAAC;IACvB;EAEJ;AACJ", "ignoreList": []}]}