<!doctype html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>ColorPicker Examples</title>
		<link th:href="@{/../themes/default/default.css}" rel="stylesheet">
		<script th:src="@{/../kindeditor-min.js}"></script>
		<script>
			KindEditor.ready(function(K) {
				var colorpicker;
				K('#colorpicker').bind('click', function(e) {
					e.stopPropagation();
					if (colorpicker) {
						colorpicker.remove();
						colorpicker = null;
						return;
					}
					var colorpickerPos = K('#colorpicker').pos();
					colorpicker = K.colorpicker({
						x : colorpickerPos.x,
						y : colorpickerPos.y + K('#colorpicker').height(),
						z : 19811214,
						selectedColor : 'default',
						noColor : '无颜色',
						click : function(color) {
							K('#color').val(color);
							colorpicker.remove();
							colorpicker = null;
						}
					});
				});
				K(document).click(function() {
					if (colorpicker) {
						colorpicker.remove();
						colorpicker = null;
					}
				});
			});
		</script>
	</head>
	<body>
		<input type="text" id="color" value="" /> <input type="button" id="colorpicker" value="打开取色器" />
	</body>
</html>
