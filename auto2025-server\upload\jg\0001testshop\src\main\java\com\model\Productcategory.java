package com.model;

import java.util.Date;
import java.math.BigDecimal;
import java.io.Serializable;

/**
 * 产品分类实体类
 *
 * 功能说明：
 * 1. 对应数据库表：productcategory
 * 2. 继承ComData基类，包含通用字段和方法
 * 3. 实现Serializable接口，支持序列化
 */
public class Productcategory extends ComData{

	/** 序列化版本号 */
	private static final long serialVersionUID = 1L;

	// ==================== 字段定义 ====================
	/** 分类id */
	private Integer cid;

	/** 分类名称 */
	private String cname;



	// ==================== 构造方法 ====================

	/**
	 * 默认构造方法
	 */
	public Productcategory()
	{
		super();
	}

	// ==================== Getter和Setter方法 ====================
		public Integer getCid() {
		return cid;
	}

	public void setCid(Integer cid) {
		this.cid = cid;
	}

	public String getCname() {
		return cname;
	}

	public void setCname(String cname) {
		this.cname = cname;
	}



}
