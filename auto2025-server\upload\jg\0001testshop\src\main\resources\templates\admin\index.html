<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网站后台管理系统</title>

    <!-- Bootstrap CSS -->
    <link href="/admin/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="/admin/css/bootstrap-icons.css" rel="stylesheet">

    <!-- 自定义样式 -->
    <link href="/admin/css/custom.css" rel="stylesheet">

    
</head>
<body>
    <div class="admin-wrapper">
        <!-- 侧边栏 -->
        <nav class="sidebar">
            <!-- 侧边栏头部 -->
            <div class="sidebar-header">
                <img src="/admin/image/logo.svg" alt="Logo" class="sidebar-logo">
                <h4 class="sidebar-title">网站后台管理系统</h4>
            </div>

            <!-- 菜单 -->
            <div class="sidebar-menu">
                <!-- 欢迎页面 -->
                <div class="menu-item">
                    <a href="toright" class="menu-link active" data-menu-id="dashboard" target="main_iframe">
                        <i class="bi bi-speedometer2 menu-icon"></i>
                        <span class="menu-text">欢迎页面</span>
                    </a>
                </div>

            <div class="menu-item">
    <a href="#" class="menu-link" data-bs-toggle="collapse" data-bs-target="#userinfoMenu" aria-expanded="false">
        <i class="bi bi-table menu-icon"></i>
        <span class="menu-text">用户信息管理</span>
        <i class="bi bi-chevron-down menu-arrow"></i>
    </a>
    <div class="submenu" id="userinfoMenu">
       <a href="/userinfoToAdd" class="menu-link" target="main_iframe">
    <span class="menu-text">添加用户信息</span>
</a>
        <a href="/userinfoList" class="menu-link" target="main_iframe">
    <span class="menu-text">管理用户信息</span>
</a>
    </div>
</div>

<div class="menu-item">
    <a href="#" class="menu-link" data-bs-toggle="collapse" data-bs-target="#productcategoryMenu" aria-expanded="false">
        <i class="bi bi-table menu-icon"></i>
        <span class="menu-text">产品分类管理</span>
        <i class="bi bi-chevron-down menu-arrow"></i>
    </a>
    <div class="submenu" id="productcategoryMenu">
       <a href="/productcategoryToAdd" class="menu-link" target="main_iframe">
    <span class="menu-text">添加产品分类</span>
</a>
        <a href="/productcategoryList" class="menu-link" target="main_iframe">
    <span class="menu-text">管理产品分类</span>
</a>
    </div>
</div>

<div class="menu-item">
    <a href="#" class="menu-link" data-bs-toggle="collapse" data-bs-target="#productsMenu" aria-expanded="false">
        <i class="bi bi-table menu-icon"></i>
        <span class="menu-text">产品管理</span>
        <i class="bi bi-chevron-down menu-arrow"></i>
    </a>
    <div class="submenu" id="productsMenu">
       <a href="/productsToAdd" class="menu-link" target="main_iframe">
    <span class="menu-text">添加产品</span>
</a>
        <a href="/productsList" class="menu-link" target="main_iframe">
    <span class="menu-text">管理产品</span>
</a>
    </div>
</div>

<div class="menu-item">
    <a href="#" class="menu-link" data-bs-toggle="collapse" data-bs-target="#systemnoticeMenu" aria-expanded="false">
        <i class="bi bi-table menu-icon"></i>
        <span class="menu-text">系统公告管理</span>
        <i class="bi bi-chevron-down menu-arrow"></i>
    </a>
    <div class="submenu" id="systemnoticeMenu">
       <a href="/systemnoticeList2" class="menu-link" target="main_iframe">
    <span class="menu-text">系统公告列表</span>
</a>
        <a href="/systemnoticeToAdd" class="menu-link" target="main_iframe">
    <span class="menu-text">添加系统公告</span>
</a>
        <a href="/systemnoticeList" class="menu-link" target="main_iframe">
    <span class="menu-text">管理系统公告</span>
</a>
    </div>
</div>

<div class="menu-item">
    <a href="#" class="menu-link" data-bs-toggle="collapse" data-bs-target="#adminMenu" aria-expanded="false">
        <i class="bi bi-table menu-icon"></i>
        <span class="menu-text">管理员管理</span>
        <i class="bi bi-chevron-down menu-arrow"></i>
    </a>
    <div class="submenu" id="adminMenu">
       <a href="/adminToAdd" class="menu-link" target="main_iframe">
    <span class="menu-text">添加管理员</span>
</a>
        <a href="/adminList" class="menu-link" target="main_iframe">
    <span class="menu-text">管理管理员</span>
</a>
    </div>
</div>

<div class="menu-item">
    <a href="#" class="menu-link" data-bs-toggle="collapse" data-bs-target="#statisticsMenu" aria-expanded="false">
        <i class="bi bi-table menu-icon"></i>
        <span class="menu-text">统计报表</span>
        <i class="bi bi-chevron-down menu-arrow"></i>
    </a>
    <div class="submenu" id="statisticsMenu">
       <a href="/categoryReport" class="menu-link" target="main_iframe">
    <span class="menu-text">商品分类销售统计</span>
</a>
        <a href="/goodsRanking" class="menu-link" target="main_iframe">
    <span class="menu-text">商品销售排行</span>
</a>
        <a href="/dailySales" class="menu-link" target="main_iframe">
    <span class="menu-text">日销售统计</span>
</a>
        <a href="/monthlySales" class="menu-link" target="main_iframe">
    <span class="menu-text">月销售统计</span>
</a>
    </div>
</div>

<div class="menu-item">
    <a href="#" class="menu-link" data-bs-toggle="collapse" data-bs-target="#systemMenu" aria-expanded="false">
        <i class="bi bi-table menu-icon"></i>
        <span class="menu-text">系统管理</span>
        <i class="bi bi-chevron-down menu-arrow"></i>
    </a>
    <div class="submenu" id="systemMenu">
       <a href="/toadminpass" class="menu-link" target="main_iframe">
    <span class="menu-text">修改密码</span>
</a>
    </div>
</div>



            </div>
        </nav>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航栏 -->
            <header class="top-navbar">
                <button class="navbar-toggle" type="button">
                    <i class="bi bi-list"></i>
                </button>

                <div class="navbar-user">
                 
                    <!-- 用户信息 -->
                    <div class="user-info">
                        <p class="user-name"><span th:text="${session.lname}"></span></p>
                        <p class="user-role"><span th:text="${session.role}"></span></p>
                    </div>

                    <!-- 用户头像下拉菜单 -->
                    <div class="dropdown">
                        <button class="btn btn-link p-0" type="button" data-bs-toggle="dropdown">
                            <img src="/admin/image/avatar.svg" alt="Avatar" class="user-avatar">
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <!-- <li><a class="dropdown-item" href="/" target="_blank"><i class="bi bi-person me-2"></i>网站首页</a></li> -->
                 
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="toquit"><i class="bi bi-box-arrow-right me-2"></i>退出登录</a></li>
                        </ul>
                    </div>
                </div>
            </header>

            <!-- 内容区域 -->


                <iframe name="main_iframe" width="100%" style="overflow-x: hidden;overflow-y: auto;height: 91%;" class="main_iframe"
                    id="default" src="toright" frameborder="0" data-id="right.html"></iframe>

        </main>
    </div>
    <!-- Bootstrap JS -->

    <script src="/admin/js/bootstrap.bundle.js"></script>
    <!-- 自定义JS -->
    <script src="/admin/js/admin.js">
    // 全局变量
    window.CommonUtils = {
        // Bootstrap Toast提示函数
        showToast: function (message, type, duration) {
            type = type || 'success';
            duration = duration || 3000;

            var toastClass = '';
            var iconClass = '';
            var title = '';

            switch (type) {
                case 'success':
                    toastClass = 'bg-success';
                    iconClass = 'bi bi-check-circle-fill';
                    title = '成功';
                    break;
                case 'error':
                case 'danger':
                    toastClass = 'bg-danger';
                    iconClass = 'bi bi-exclamation-circle-fill';
                    title = '错误';
                    break;
                case 'warning':
                    toastClass = 'bg-warning';
                    iconClass = 'bi bi-exclamation-triangle-fill';
                    title = '警告';
                    break;
                case 'info':
                    toastClass = 'bg-info';
                    iconClass = 'bi bi-info-circle-fill';
                    title = '提示';
                    break;
                default:
                    toastClass = 'bg-primary';
                    iconClass = 'bi bi-info-circle-fill';
                    title = '提示';
            }

            var toastHtml =
                '<div class="toast align-items-center text-white ' + toastClass + ' border-0" role="alert" aria-live="assertive" aria-atomic="true">' +
                '<div class="d-flex">' +
                '<div class="toast-body">' +
                '<i class="' + iconClass + ' me-2"></i>' + message +
                '</div>' +
                '<button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>' +
                '</div>' +
                '</div>';

            var toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                document.body.appendChild(toastContainer);
            }

            toastContainer.innerHTML = toastHtml;
            var toastElement = toastContainer.querySelector('.toast');
            var toast = new bootstrap.Toast(toastElement, { delay: duration });
            toast.show();
        },

        // 确认删除对话框
        confirmDelete: function (message, callback) {
            message = message || '确定要删除这条记录吗？此操作不可恢复！';

            // 创建Bootstrap Modal
            var modalId = 'deleteModal-' + Date.now();
            var modalHtml =
                '<div class="modal fade" id="' + modalId + '" tabindex="-1" aria-hidden="true">' +
                '<div class="modal-dialog modal-dialog-centered">' +
                '<div class="modal-content">' +
                '<div class="modal-header bg-danger text-white">' +
                '<h5 class="modal-title" style="font-weight: normal; font-size: 16px;">' +
                '<i class="bi bi-exclamation-triangle-fill me-2"></i>确认删除' +
                '</h5>' +
                '<button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>' +
                '</div>' +
                '<div class="modal-body">' +
                '<div class="text-center">' +
                '<i class="bi bi-trash3 text-danger" style="font-size: 3rem; margin-bottom: 1rem;"></i>' +
                '<p style="font-size: 14px; font-weight: normal;">' + message + '</p>' +
                '</div>' +
                '</div>' +
                '<div class="modal-footer">' +
                '<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">' +
                '<i class="bi bi-x-lg me-1"></i>取消' +
                '</button>' +
                '<button type="button" class="btn btn-danger" id="confirmDeleteBtn">' +
                '<i class="bi bi-trash3 me-1"></i>确认删除' +
                '</button>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '</div>';

            // 添加modal到页面
            $('body').append(modalHtml);
            var modal = new bootstrap.Modal(document.getElementById(modalId));

            // 绑定确认按钮事件
            $('#' + modalId + ' #confirmDeleteBtn').on('click', function () {
                modal.hide();
                if (typeof callback === 'function') {
                    callback();
                }
            });

            // 显示modal
            modal.show();

            // modal关闭后移除DOM元素
            $('#' + modalId).on('hidden.bs.modal', function () {
                $(this).remove();
            });
        }
    };
</script>
</body>
</html>
