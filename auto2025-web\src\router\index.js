import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/adminlogin',
    name: 'Login',
    component: () => import('../views/Login'),
    meta: {
      requireAuth: false
    }
  },

  {
    path: '/main',
    name: 'Main',
    component: () => import('../views/Main'),
    redirect: "/home",
    children: [
      {
        path: '/home',
        name: 'Home',
        component: () => import('../views/admin/Home'),
        meta: {
          requireAuth: true,title:'首页'
        }

      },
  

      {
      path: '/bigAdd',
      name: 'BigAdd',
      component: () => import('../views/admin/big/BigAdd'),
      meta: { requiresAuth: true,title: '大类添加' }
    },
 {
      path: '/bigEdit',
      name: 'BigEdit',
      component: () => import('../views/admin/big/BigEdit'),
      meta: { requiresAuth: true,title: '大类修改' }
    },
 {
      path: '/bigManage',
      name: 'BigManage',
      component: () => import('../views/admin/big/BigManage'),
      meta: { requiresAuth: true,title: '大类管理' }
    },
{
    path: '/bigDetail',
    name: 'BigDetail',
    component: () => import('../views/admin/big/BigDetail'),
    meta: { requiresAuth: true,title: '大类详情' }
  },
{
      path: '/moresAdd',
      name: 'MoresAdd',
      component: () => import('../views/admin/mores/MoresAdd'),
      meta: { requiresAuth: true,title: '字段添加' }
    },
 {
      path: '/moresEdit',
      name: 'MoresEdit',
      component: () => import('../views/admin/mores/MoresEdit'),
      meta: { requiresAuth: true,title: '字段修改' }
    },
 {
      path: '/moresManage',
      name: 'MoresManage',
      component: () => import('../views/admin/mores/MoresManage'),
      meta: { requiresAuth: true,title: '字段管理' }
    },
{
    path: '/moresDetail',
    name: 'MoresDetail',
    component: () => import('../views/admin/mores/MoresDetail'),
    meta: { requiresAuth: true,title: '字段详情' }
  },
{
      path: '/tablesAdd',
      name: 'TablesAdd',
      component: () => import('../views/admin/tables/TablesAdd'),
      meta: { requiresAuth: true,title: '表名添加' }
    },
 {
      path: '/tablesEdit',
      name: 'TablesEdit',
      component: () => import('../views/admin/tables/TablesEdit'),
      meta: { requiresAuth: true,title: '表名修改' }
    },
 {
      path: '/tablesManage',
      name: 'TablesManage',
      component: () => import('../views/admin/tables/TablesManage'),
      meta: { requiresAuth: true,title: '表名管理' }
    },
{
    path: '/tablesDetail',
    name: 'TablesDetail',
    component: () => import('../views/admin/tables/TablesDetail'),
    meta: { requiresAuth: true,title: '表名详情' }
  },
{
      path: '/projectsAdd',
      name: 'ProjectsAdd',
      component: () => import('../views/admin/projects/ProjectsAdd'),
      meta: { requiresAuth: true,title: '项目添加' }
    },
 {
      path: '/projectsEdit',
      name: 'ProjectsEdit',
      component: () => import('../views/admin/projects/ProjectsEdit'),
      meta: { requiresAuth: true,title: '项目修改' }
    },
 {
      path: '/projectsManage',
      name: 'ProjectsManage',
      component: () => import('../views/admin/projects/ProjectsManage'),
      meta: { requiresAuth: true,title: '项目管理' }
    },
{
    path: '/projectsDetail',
    name: 'ProjectsDetail',
    component: () => import('../views/admin/projects/ProjectsDetail'),
    meta: { requiresAuth: true,title: '项目详情' }
  },
{
      path: '/adminAdd',
      name: 'AdminAdd',
      component: () => import('../views/admin/admin/AdminAdd'),
      meta: { requiresAuth: true,title: '管理员添加' }
    },
 {
      path: '/adminEdit',
      name: 'AdminEdit',
      component: () => import('../views/admin/admin/AdminEdit'),
      meta: { requiresAuth: true,title: '管理员修改' }
    },
 {
      path: '/adminManage',
      name: 'AdminManage',
      component: () => import('../views/admin/admin/AdminManage'),
      meta: { requiresAuth: true,title: '管理员管理' }
    },
{
      path: '/smallAdd',
      name: 'SmallAdd',
      component: () => import('../views/admin/small/SmallAdd'),
      meta: { requiresAuth: true,title: '小类添加' }
    },
 {
      path: '/smallEdit',
      name: 'SmallEdit',
      component: () => import('../views/admin/small/SmallEdit'),
      meta: { requiresAuth: true,title: '小类修改' }
    },
 {
      path: '/smallManage',
      name: 'SmallManage',
      component: () => import('../views/admin/small/SmallManage'),
      meta: { requiresAuth: true,title: '小类管理' }
    },
{
    path: '/smallDetail',
    name: 'SmallDetail',
    component: () => import('../views/admin/small/SmallDetail'),
    meta: { requiresAuth: true,title: '小类详情' }
  },
{
    path: '/total1',
    name: 'Total1',
    component: () => import('../views/admin/total/Total1'),
    meta: { requiresAuth: true,title: '图表1' }
  },
{
    path: '/total2',
    name: 'Total2',
    component: () => import('../views/admin/total/Total2'),
    meta: { requiresAuth: true,title: '图表2' }
  },
{
    path: '/total3',
    name: 'Total3',
    component: () => import('../views/admin/total/Total3'),
    meta: { requiresAuth: true,title: '图表3' }
  },
{
    path: '/total4',
    name: 'Total4',
    component: () => import('../views/admin/total/Total4'),
    meta: { requiresAuth: true,title: '图表4' }
  },

     {
          path: '/password',
          name: 'Password',
          component: () => import('../views/admin/system/Password'),
          meta: {
            requireAuth: true,title:'修改密码'
          }
     },
    ]
  },
  {
    path: '/',
    name: '/',
    component: () => import('../views/Index'),
    redirect: "/codeGenerator",
    children: [
    {
        path: '/codeGenerator',
        name: 'codeGenerator',
        component: () => import('../views/web/CodeGenerator'),
         
    },
    ]
},

]



const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})



router.beforeEach((to, from, next) => {
  if (to.path == '/') {
    sessionStorage.removeItem('userLname');
    sessionStorage.removeItem('role');
  }
  let currentUser = sessionStorage.getItem('userLname');
  console.log(to + "  to.meta.requireAuth");

  if (to.meta.requireAuth) {
    if (!currentUser && to.path != '/login') {
      next({ path: '/' });
    } else {
      next();
    }
  } else {

    next();
  }
})

export default router


