{"remainingRequest": "J:\\auto2025\\auto2025-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!J:\\auto2025\\auto2025-web\\src\\views\\web\\CodeGenerator.vue?vue&type=template&id=c61fc0be&scoped=true", "dependencies": [{"path": "J:\\auto2025\\auto2025-web\\src\\views\\web\\CodeGenerator.vue", "mtime": 1749465006927}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748675476717}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748675484569}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1748675491877}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748675476717}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1748675485115}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["J:\\auto2025\\auto2025-web\\src\\views\\web\\CodeGenerator.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CA<PERSON>;MAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACT,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACT,CAAC,CAAC,CAAC,CAAC;gBACN,CAAC,CAAC,CAAC,CAAC;gBACJ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClD,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1B,CAAC,CAAC,CAAC,CAAC,CAAC;;cAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACrE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;kBAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5C,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;kBACjE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrC,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;kBACzD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7C,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;kBAClD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxD,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC9C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrB,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;0BACtF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;0BACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;0BACrF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;0BACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;kBAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBACvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;kBAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;0BACvF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BAC3F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;0BACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACxB,CAAC,CAAC,CAAC,CAAC,CAAC;sBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;kBAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BAC5E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BAC7E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBACvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;kBAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;0BACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;0BAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;0BACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;0BAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;oBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;kBAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAET,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACtD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACb,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACT,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC;;cAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC1C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC1B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACR,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;;cAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACpE,CAAC,CAAC,CAAC;kBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACb,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzB,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC7E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACZ,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC5B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACpC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACxC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAClD,CAAC,CAAC,CAAC,CAAC,CAAC;oBACP,CAAC,CAAC,CAAC,CAAC,CAAC;oBACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC/B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;oBACvD,CAAC,CAAC,CAAC;oBACH,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAChD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClG,CAAC,CAAC,CAAC,CAAC,CAAC;kBACP,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;;cAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACpD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACN,CAAC,CAAC,CAAC,EAAE,CAAC;cACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACT,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrB,CAAC,CAAC,CAAC,CAAC,CAAC;;cAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACvC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnE,CAAC,CAAC,CAAC,CAAC,CAAC;kBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACxC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3E,CAAC,CAAC,CAAC,CAAC,CAAC;kBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACvC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC1E,CAAC,CAAC,CAAC,CAAC,CAAC;kBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACxC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChE,CAAC,CAAC,CAAC,CAAC,CAAC;kBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACvC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9E,CAAC,CAAC,CAAC,CAAC,CAAC;kBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACvC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC/E,CAAC,CAAC,CAAC,CAAC,CAAC;kBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC;;cAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnC,CAAC,CAAC,CAAC,EAAE,CAAC;kBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACT,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAEX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBAC1F,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9C,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;;cAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAEd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACrD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChE,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;kBAEjG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;kBACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjH,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC9B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC3B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BAChD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC1C,CAAC,CAAC,CAAC,CAAC;sBACN,CAAC,CAAC,CAAC,CAAC;oBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,CAAC;;kBAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;kBACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClH,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC9B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACzB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BACzE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BAC/C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BACjD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACjD,CAAC,CAAC,CAAC,CAAC;sBACN,CAAC,CAAC,CAAC,CAAC;oBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC1B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC/E,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC/F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9B,CAAC,CAAC,CAAC,CAAC,CAAC;sBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,CAAC;kBACP,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEb,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACT,CAAC;YACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzB,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACxH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC3B,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACT,CAAC,CAAC,CAAC,CAAC;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACtH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACT,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACb,CAAC,CAAC,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACzC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACzD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACjD,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACzG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpE,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAC5E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAC1E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACzF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBACxF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBACxF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBACpH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;kBAER,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BACnF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BACN,CAAC,CAAC,CAAC,EAAE,CAAC;0BACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC;;cAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;kBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAChF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACnF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAClF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACrF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACtF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACzF,CAAC,CAAC,CAAC,CAAC,CAAC;oBACP,CAAC,CAAC,CAAC,CAAC,CAAC;kBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;kBAER,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;kBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAClF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACpF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACpF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAClF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBAC1F,CAAC,CAAC,CAAC,CAAC,CAAC;oBACP,CAAC,CAAC,CAAC,CAAC,CAAC;kBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;kBAER,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;kBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC/E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACnF,CAAC,CAAC,CAAC,CAAC,CAAC;oBACP,CAAC,CAAC,CAAC,CAAC,CAAC;kBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;;YAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;kBAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACT,CAAC,CAAC,CAAC;kBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACT,CAAC,CAAC,CAAC;kBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACT,CAAC,CAAC,CAAC;kBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;kBACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC;;cAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC1E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;kBAEpE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;sBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACpF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;kBAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;sBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACpF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;kBAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;sBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC/E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;kBAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;sBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;wBACtF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;sBAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;kBAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;sBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;kBAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;sBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;kBAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;sBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;kBAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;sBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;kBAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;sBAClC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;0BACzF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;0BACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;0BACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BACnF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;0BACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0BACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;0BACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sBACb,CAAC,CAAC,CAAC,CAAC,CAAC;oBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACxC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAET,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtD,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChD,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEP,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrC,CAAC,CAAC,CAAC,EAAE,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACT,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;UAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cAClD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC7E,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACvE,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClB,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cAClD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC9E,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACvE,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC/B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpE,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAC3D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;cAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACvE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;cAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "J:/auto2025/auto2025-web/src/views/web/CodeGenerator.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"code-generator-container\">\n    <!-- 用户信息浮动显示 -->\n    <div v-if=\"isLoggedIn\" class=\"user-info-float\">\n      <el-dropdown @command=\"handleUserCommand\">\n        <span class=\"user-info-trigger\">\n          <el-icon>\n            <User />\n          </el-icon>\n          {{ userInfo.username }}\n          <el-icon class=\"el-icon--right\">\n            <ArrowDown />\n          </el-icon>\n        </span>\n        <template #dropdown>\n          <el-dropdown-menu>\n            <el-dropdown-item command=\"logout\">退出登录</el-dropdown-item>\n          </el-dropdown-menu>\n        </template>\n      </el-dropdown>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"main-content\">\n      <el-tabs v-model=\"activeTab\" type=\"card\" class=\"generator-tabs\" @tab-click=\"handleTabClick\">\n        <!-- 设置项目 -->\n        <el-tab-pane label=\"设置项目\" name=\"project\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <Folder />\n              </el-icon>\n              设置项目\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <!-- 页面标题 -->\n            <div class=\"page-header\">\n              <div class=\"header-content\">\n                <h1 class=\"page-title\">\n                  <el-icon class=\"title-icon\">\n                    <Setting />\n                  </el-icon>\n                  代码生成器\n                </h1>\n                <p class=\"page-description\">快速生成高质量的代码，提升开发效率</p>\n              </div>\n            </div>\n\n            <!-- 项目类型选择 -->\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>支持的项目类型</h3>\n                <p>选择您需要的技术栈，快速生成项目模板</p>\n              </div>\n\n              <!-- 功能卡片网格 -->\n              <div class=\"features-grid\">\n                <div class=\"feature-card\" @click=\"selectProject('springboot-thymeleaf')\"\n                  :class=\"{ active: selectedProject === 'springboot-thymeleaf' }\">\n                  <h3>🍃 SpringBoot + Thymeleaf</h3>\n                  <p>传统的服务端渲染架构，适合企业级应用开发，集成Thymeleaf模板引擎</p>\n                </div>\n\n                <div class=\"feature-card\" @click=\"selectProject('springboot-miniprogram')\"\n                  :class=\"{ active: selectedProject === 'springboot-miniprogram' }\">\n                  <h3>📱 SpringBoot + 小程序</h3>\n                  <p>微信小程序后端API开发，提供完整的用户认证和数据管理功能</p>\n                </div>\n\n                <div class=\"feature-card\" @click=\"selectProject('springboot-vue')\"\n                  :class=\"{ active: selectedProject === 'springboot-vue' }\">\n                  <h3>⚡ SpringBoot + Vue</h3>\n                  <p>现代化前后端分离架构，Vue.js前端 + SpringBoot后端API</p>\n                </div>\n\n                <div class=\"feature-card\" @click=\"selectProject('ssm-vue')\"\n                  :class=\"{ active: selectedProject === 'ssm-vue' }\">\n                  <h3>🔧 SSM + Vue</h3>\n                  <p>经典的SSM框架（Spring + SpringMVC + MyBatis）配合Vue.js前端</p>\n                </div>\n              </div>\n            </div>\n\n            <!-- 项目配置区域 -->\n            <div v-if=\"selectedProject\" class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>项目配置 - {{ getProjectName(selectedProject) }}</h3>\n                <p>配置项目基本信息和生成参数</p>\n              </div>\n              <div class=\"form-section\">\n                <el-form :model=\"projectForm\" label-width=\"120px\" class=\"project-form\">\n                  <el-row :gutter=\"20\">\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"数据库类型\">\n                        <el-select v-model=\"projectForm.databaseType\" placeholder=\"请选择数据库类型\" style=\"width: 100%\">\n                          <el-option label=\"MySQL\" value=\"mysql\" />\n                          <el-option label=\"SQL Server\" value=\"sqlserver\" />\n                        </el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"数据库模式\">\n                        <el-select v-model=\"projectForm.databaseMode\" placeholder=\"请选择数据库模式\" style=\"width: 100%\"\n                          @change=\"handleDatabaseModeChange\">\n                          <el-option label=\"新建数据库\" value=\"new\" />\n                          <el-option label=\"已有数据库\" value=\"existing\" />\n                        </el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"项目中文名称\">\n                        <el-input v-model=\"projectForm.name\" placeholder=\"请输入项目中文名称\" />\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n\n                  <el-row :gutter=\"20\" v-if=\"projectForm.databaseMode === 'new'\">\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"项目编号\">\n                        <el-input v-model=\"projectForm.projectCode\" placeholder=\"项目编号\" />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"数据库名称\">\n                        <el-input v-model=\"projectForm.databaseName\" placeholder=\"请输入数据库名称\" />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"学校名称\">\n                        <el-input v-model=\"projectForm.schoolName\" placeholder=\"请输入学校名称\" />\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n\n                  <el-row :gutter=\"20\" v-if=\"projectForm.databaseMode === 'existing'\">\n                    <el-col :span=\"24\">\n                      <el-form-item label=\"选择数据库\">\n                        <el-select v-model=\"projectForm.selectedDatabase\" placeholder=\"请选择数据库\" style=\"width: 100%\"\n                          @change=\"handleProjectSelect\" :loading=\"projectsLoading\">\n                          <el-option v-for=\"db in availableDatabases\" :key=\"db.value\" :label=\"db.text\" :value=\"db.value\"\n                            :disabled=\"!db.value\" />\n                        </el-select>\n                        <div class=\"form-text\" v-if=\"availableDatabases.length > 0\">\n                          格式：项目编号--数据库名称 (项目中文名称)\n                        </div>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n\n                  <el-row :gutter=\"20\">\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"后台模板\">\n                        <el-input v-model=\"projectForm.backendTemplate\" placeholder=\"点击选择后台模板\" readonly\n                          @click=\"openTemplateModal\" />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"前台模板\">\n                        <el-input v-model=\"projectForm.frontendTemplate\" placeholder=\"点击选择前台模板\" readonly\n                          @click=\"openFrontTemplateModal\" />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"复制项目\">\n                        <el-input v-model=\"projectForm.copyProject\" placeholder=\"请输入要复制的项目\" />\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n\n                  <el-row :gutter=\"20\">\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"layer弹出层\">\n                        <el-select v-model=\"projectForm.layer\" style=\"width: 100%\">\n                          <el-option label=\"否\" value=\"否\" />\n                          <el-option label=\"是\" value=\"是\" />\n                        </el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"统计图表\">\n                        <el-select v-model=\"projectForm.charts\" style=\"width: 100%\">\n                          <el-option label=\"否\" value=\"否\" />\n                          <el-option label=\"是\" value=\"是\" />\n                        </el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <!-- 空列，保持布局平衡 -->\n                    </el-col>\n                  </el-row>\n\n                  <el-form-item label=\"Session信息\">\n                    <el-row :gutter=\"10\">\n                      <el-col :span=\"6\">\n                        <el-input v-model=\"projectForm.adminId\" placeholder=\"管理员ID\" />\n                      </el-col>\n                      <el-col :span=\"6\">\n                        <el-input v-model=\"projectForm.adminName\" placeholder=\"管理员姓名\" />\n                      </el-col>\n                      <el-col :span=\"6\">\n                        <el-input v-model=\"projectForm.adminRole\" placeholder=\"管理员角色\" />\n                      </el-col>\n                      <el-col :span=\"6\">\n                        <el-input v-model=\"projectForm.adminLoginName\" placeholder=\"登录名\" />\n                      </el-col>\n                    </el-row>\n                  </el-form-item>\n                </el-form>\n\n                <div class=\"form-actions\">\n                  <el-button type=\"primary\" @click=\"nextStep\" size=\"large\">\n                    🎯 下一步：设计表单\n                  </el-button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 设计表单 -->\n        <el-tab-pane label=\"设计表单\" name=\"form\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <Edit />\n              </el-icon>\n              设计表单\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>数据表管理</h3>\n                <p>设计您的数据库表结构和表单字段</p>\n              </div>\n\n              <!-- 项目信息显示 -->\n              <div v-if=\"currentProjectInfo\" class=\"project-info-section\">\n                <div class=\"project-info-card\">\n                  <h4>当前项目：{{ currentProjectInfo.name }}</h4>\n                  <div class=\"project-details\">\n                    <span class=\"project-detail-item\">\n                      <strong>项目编号：</strong>{{ currentProjectInfo.projectCode }}\n                    </span>\n                    <span class=\"project-detail-item\">\n                      <strong>数据库：</strong>{{ currentProjectInfo.databaseName }}\n                    </span>\n                    <span class=\"project-detail-item\">\n                      <strong>类型：</strong>{{ getProjectName(selectedProject) }}\n                    </span>\n                  </div>\n                </div>\n              </div>\n\n              <!-- 表单列表 -->\n              <div class=\"table-designer\">\n                <div class=\"table-list-header\">\n                  <h4>数据表列表</h4>\n                  <el-button type=\"primary\" :icon=\"Plus\" @click=\"openTableDesignModal()\">\n                    添加新表\n                  </el-button>\n                </div>\n\n                <div v-if=\"projectTablesLoading\" class=\"loading-section\">\n                  <el-icon class=\"is-loading\">\n                    <Loading />\n                  </el-icon>\n                  <span>正在加载表单数据...</span>\n                </div>\n\n                <div v-else-if=\"projectTables.length === 0\" class=\"empty-section\">\n                  <el-empty description=\"暂无数据表\">\n                    <el-button type=\"primary\" @click=\"openTableDesignModal()\">创建第一个表</el-button>\n                  </el-empty>\n                </div>\n\n                <div v-else class=\"table-list\">\n                  <div class=\"table-item\" v-for=\"table in projectTables\" :key=\"table.tid\"\n                    @click=\"openTableDesignModal(table)\">\n                    <div class=\"table-item-header\">\n                      <h5>{{ table.tword || table.tname }}</h5>\n                      <div class=\"table-item-actions\">\n                        <el-button size=\"small\" :icon=\"Edit\" @click.stop=\"openTableDesignModal(table)\">编辑</el-button>\n                        <el-button size=\"small\" type=\"danger\" :icon=\"Delete\"\n                          @click.stop=\"deleteTable(table)\">删除</el-button>\n                      </div>\n                    </div>\n                    <p class=\"table-item-description\">\n                      {{ table.tname }} ({{ getTableFieldCount(table) }}个字段)\n                    </p>\n                    <div class=\"table-item-functions\" v-if=\"table.tgn\">\n                      <span class=\"function-tag\" v-for=\"func in getTableFunctions(table)\" :key=\"func\">{{ func }}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"form-actions\" style=\"margin-top: 30px;\">\n                <el-button @click=\"activeTab = 'project'\" size=\"large\">\n                  ← 上一步：项目配置\n                </el-button>\n                <el-button type=\"primary\" @click=\"nextStep\" size=\"large\">\n                  下一步：生成项目 →\n                </el-button>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 生成项目 -->\n        <el-tab-pane label=\"生成项目\" name=\"generate\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <Cpu />\n              </el-icon>\n              生成项目\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>生成项目</h3>\n                <p>确认配置信息并生成您的项目</p>\n              </div>\n\n              <!-- 项目配置摘要 -->\n              <div class=\"generation-summary\">\n                <h4>项目配置摘要</h4>\n                <el-row :gutter=\"20\">\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">项目名称:</span>\n                      <span class=\"summary-value\">{{ projectForm.name || '未设置' }}</span>\n                    </div>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">数据库名称:</span>\n                      <span class=\"summary-value\">{{ projectForm.databaseName || '未设置' }}</span>\n                    </div>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">项目编号:</span>\n                      <span class=\"summary-value\">{{ projectForm.projectCode || '未设置' }}</span>\n                    </div>\n                  </el-col>\n                </el-row>\n                <el-row :gutter=\"20\" style=\"margin-top: 15px;\">\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">数据表数量:</span>\n                      <span class=\"summary-value\">{{ projectTables.length }} 个</span>\n                    </div>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">后台模板:</span>\n                      <span class=\"summary-value\">{{ projectForm.backendTemplate || '未选择' }}</span>\n                    </div>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">前台模板:</span>\n                      <span class=\"summary-value\">{{ projectForm.frontendTemplate || '未选择' }}</span>\n                    </div>\n                  </el-col>\n                </el-row>\n              </div>\n\n              <!-- 生成操作区域 -->\n              <div class=\"generation-actions\">\n                <el-button\n                  type=\"primary\"\n                  size=\"large\"\n                  @click=\"generateProject\"\n                  :loading=\"generationInProgress\"\n                  :disabled=\"!canGenerate || generationInProgress\">\n                  <el-icon v-if=\"!generationInProgress\">\n                    <Cpu />\n                  </el-icon>\n                  <el-icon v-else>\n                    <Loading />\n                  </el-icon>\n                  {{ generationInProgress ? '正在生成项目，请稍候...' : '🚀 生成项目' }}\n                </el-button>\n\n                <!-- 生成进度提示 -->\n                <div v-if=\"generationInProgress\" class=\"generation-progress\">\n                  <el-progress :percentage=\"100\" :show-text=\"false\" status=\"success\" :indeterminate=\"true\" />\n                  <p class=\"progress-text\">正在生成代码文件和项目结构...</p>\n                </div>\n              </div>\n\n              <!-- 项目生成结果显示区域 -->\n              <div v-if=\"generationResult\" class=\"generation-result\">\n                <h4>项目生成结果</h4>\n\n                <!-- 生成状态 -->\n                <div class=\"generation-status\" :class=\"generationResult.status\">\n                  <el-icon class=\"status-icon\">\n                    <Check v-if=\"generationResult.status === 'success'\" />\n                    <Close v-else />\n                  </el-icon>\n                  <span class=\"status-text\">{{ generationResult.message }}</span>\n                </div>\n\n                <!-- 文件列表 -->\n                <div v-if=\"generationResult.files\" class=\"file-list-section\">\n                  <h5>生成的文件列表 ({{ generationResult.files.savedCount }}/{{ generationResult.files.totalCount }})</h5>\n\n                  <!-- 成功文件列表 -->\n                  <div v-if=\"generationResult.files.savedFiles && generationResult.files.savedFiles.length > 0\" class=\"success-files\">\n                    <h6>✅ 成功生成的文件:</h6>\n                    <el-scrollbar max-height=\"200px\">\n                      <ul class=\"file-list success\">\n                        <li v-for=\"file in generationResult.files.savedFiles\" :key=\"file\">\n                          <el-icon class=\"file-icon\"><Document /></el-icon>\n                          <span class=\"file-name\">{{ file }}</span>\n                        </li>\n                      </ul>\n                    </el-scrollbar>\n                  </div>\n\n                  <!-- 失败文件列表 -->\n                  <div v-if=\"generationResult.files.failedFiles && generationResult.files.failedFiles.length > 0\" class=\"failed-files\">\n                    <h6>❌ 生成失败的文件:</h6>\n                    <el-scrollbar max-height=\"200px\">\n                      <ul class=\"file-list error\">\n                        <li v-for=\"file in generationResult.files.failedFiles\" :key=\"file.fileName\">\n                          <el-icon class=\"file-icon\"><Warning /></el-icon>\n                          <span class=\"file-name\">{{ file.fileName }}</span>\n                          <span class=\"file-error\">{{ file.error }}</span>\n                        </li>\n                      </ul>\n                    </el-scrollbar>\n                  </div>\n                </div>\n\n                <!-- 压缩结果 -->\n                <div v-if=\"generationResult.compression\" class=\"compression-result\">\n                  <h5>项目压缩结果</h5>\n                  <div class=\"compression-info\" :class=\"generationResult.compression.status\">\n                    <el-icon class=\"status-icon\">\n                      <Box v-if=\"generationResult.compression.status === 'success'\" />\n                      <Close v-else />\n                    </el-icon>\n                    <span class=\"compression-text\">{{ generationResult.compression.message }}</span>\n                    <div v-if=\"generationResult.compression.data\" class=\"compression-details\">\n                      <p>文件名: {{ generationResult.compression.data.zipFileName }}</p>\n                      <p>文件大小: {{ generationResult.compression.data.fileSize }}</p>\n                      <el-button type=\"success\" @click=\"downloadProject(generationResult.compression.data.zipFilePath)\">\n                        <el-icon><Download /></el-icon>\n                        下载项目文件\n                      </el-button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 数据 -->\n        <el-tab-pane label=\"数据\" name=\"data\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <DataBoard />\n              </el-icon>\n              数据\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>数据脚本管理</h3>\n                <p>生成和管理数据库建表及插入数据脚本</p>\n              </div>\n              <div class=\"data-section\">\n                <div class=\"data-toolbar\">\n                  <el-button type=\"primary\" :icon=\"Plus\" @click=\"generateDataScript\" :loading=\"dataGenerationInProgress\">生成数据脚本</el-button>\n                  <el-button :icon=\"DocumentCopy\" @click=\"copyDataScript\">复制脚本</el-button>\n                </div>\n                <div class=\"data-editor\">\n                  <el-input v-model=\"dataContent\" type=\"textarea\" :rows=\"15\" placeholder=\"数据脚本将在这里显示...\"\n                    class=\"data-textarea\" />\n                </div>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- SQL脚本 -->\n        <el-tab-pane label=\"SQL脚本\" name=\"sql\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <DocumentCopy />\n              </el-icon>\n              SQL脚本\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>SQL脚本管理</h3>\n                <p>生成和管理数据库脚本</p>\n              </div>\n              <div class=\"sql-section\">\n                <div class=\"sql-toolbar\">\n                  <el-button type=\"primary\" :icon=\"Plus\" @click=\"generateSqlScript\" :loading=\"sqlGenerationInProgress\">生成建表脚本</el-button>\n                  <el-button :icon=\"Download\" @click=\"exportSqlScript\">导出脚本</el-button>\n                  <el-button :icon=\"DocumentCopy\" @click=\"copySqlScript\">复制脚本</el-button>\n                </div>\n                <div class=\"sql-editor\">\n                  <el-input v-model=\"sqlContent\" type=\"textarea\" :rows=\"25\" placeholder=\"SQL脚本将在这里显示...\"\n                    class=\"sql-textarea\" />\n                </div>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 错误日志 -->\n        <el-tab-pane label=\"错误日志\" name=\"logs\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <Warning />\n              </el-icon>\n              错误日志\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>错误日志</h3>\n                <p>查看生成过程中的错误和警告信息</p>\n              </div>\n              <div class=\"logs-section\">\n                <div class=\"logs-toolbar\">\n                  <el-button :icon=\"Refresh\">刷新日志</el-button>\n                  <el-button :icon=\"Delete\">清空日志</el-button>\n                  <el-select v-model=\"logLevel\" placeholder=\"日志级别\" style=\"width: 120px\">\n                    <el-option label=\"全部\" value=\"all\" />\n                    <el-option label=\"错误\" value=\"error\" />\n                    <el-option label=\"警告\" value=\"warning\" />\n                    <el-option label=\"信息\" value=\"info\" />\n                  </el-select>\n                </div>\n                <div class=\"logs-content\">\n                  <div class=\"log-item\" v-for=\"(log, index) in logs\" :key=\"index\" :class=\"log.level\">\n                    <div class=\"log-time\">{{ log.time }}</div>\n                    <div class=\"log-level\">{{ log.level.toUpperCase() }}</div>\n                    <div class=\"log-message\">{{ log.message }}</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n      </el-tabs>\n    </div>\n\n    <!-- 登录对话框 -->\n    <el-dialog v-model=\"loginDialogVisible\" title=\"用户登录\" width=\"400px\" :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\" :show-close=\"false\">\n      <el-form :model=\"loginForm\" :rules=\"loginRules\" ref=\"loginFormRef\" label-width=\"80px\">\n        <el-form-item label=\"用户名\" prop=\"username\">\n          <el-input v-model=\"loginForm.username\" placeholder=\"请输入用户名\" />\n        </el-form-item>\n        <el-form-item label=\"密码\" prop=\"password\">\n          <el-input v-model=\"loginForm.password\" type=\"password\" placeholder=\"请输入密码\" @keyup.enter=\"handleLogin\" />\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button type=\"primary\" @click=\"handleLogin\" :loading=\"loginLoading\">\n            登录\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n\n    <!-- 表设计弹窗 -->\n    <el-dialog v-model=\"showTableDesignModal\" :title=\"currentTableDesign.id ? '编辑数据表' : '创建数据表'\" width=\"85%\"\n      :close-on-click-modal=\"false\" :before-close=\"closeTableDesignModal\" class=\"table-design-dialog\" top=\"5vh\"\n      destroy-on-close>\n      <div class=\"table-design-container\">\n        <!-- 自定义Tab导航 -->\n        <div class=\"custom-tabs\">\n          <div class=\"tab-nav\">\n            <div class=\"tab-item\" :class=\"{ active: currentTableTab === 'table-settings' }\"\n              @click=\"switchTableTab('table-settings')\">\n              <el-icon class=\"tab-icon\">\n                <Setting />\n              </el-icon>\n              <span class=\"tab-text\">表设置</span>\n            </div>\n            <div class=\"tab-item\" :class=\"{ active: currentTableTab === 'field-design' }\"\n              @click=\"switchTableTab('field-design')\">\n              <el-icon class=\"tab-icon\">\n                <DataBoard />\n              </el-icon>\n              <span class=\"tab-text\">字段设计</span>\n            </div>\n          </div>\n\n          <!-- Tab内容 -->\n          <div class=\"tab-content-wrapper\">\n            <!-- 表设置内容 -->\n            <div v-show=\"currentTableTab === 'table-settings'\" class=\"tab-content\">\n              <!-- 基本信息 -->\n              <div class=\"basic-info-section\">\n                <el-form :model=\"currentTableDesign\" label-width=\"100px\" class=\"design-form\" size=\"default\">\n                  <el-row :gutter=\"16\">\n                    <el-col :span=\"6\">\n                      <el-form-item label=\"表中文名称\" required>\n                        <el-input v-model=\"currentTableDesign.chineseName\" placeholder=\"请输入表的中文名称\" clearable />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"6\">\n                      <el-form-item label=\"表英文名称\" required>\n                        <el-input v-model=\"currentTableDesign.englishName\" placeholder=\"请输入表的英文名称\" clearable />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"6\">\n                      <el-form-item label=\"菜单显示顺序\">\n                        <el-input-number v-model=\"currentTableDesign.menuOrder\" :min=\"1\" :max=\"999\" placeholder=\"1\" style=\"width: 100%;\" />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"6\">\n                      <el-form-item label=\"生成数据条数\">\n                        <el-input-number v-model=\"currentTableDesign.generateDataCount\" :min=\"0\" :max=\"1000\"\n                          placeholder=\"生成数据条数\" style=\"width: 100%;\" />\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n\n                  <el-row :gutter=\"16\">\n                    <el-col :span=\"24\">\n                      <el-form-item label=\"AI助手\">\n                        <el-button type=\"primary\" @click=\"showAiGeneratorModal('table')\" style=\"width: 100%;\">\n                          <el-icon>\n                            <Cpu />\n                          </el-icon>\n                          AI生成表结构\n                        </el-button>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                </el-form>\n              </div>\n\n              <!-- 功能选择 -->\n              <div class=\"function-selection-section\">\n                <h4 style=\"margin: 20px 0 15px 0; color: #2c3e50; font-size: 16px;\">功能选择</h4>\n                <el-row :gutter=\"20\">\n                  <!-- 后台功能 -->\n                  <el-col :span=\"8\">\n                    <div class=\"function-group\">\n                      <h5>后台功能</h5>\n                      <div class=\"function-checkboxes\">\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendAdd\">后台添加</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendEdit\">后台修改</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendDelete\">后台删除</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendDetail\">后台详情</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendList\">后台列表</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.batchImport\">批量导入</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.batchExport\">批量导出</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendLogin\">后台登录</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendRegister\">后台注册</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendProfile\">后台个人信息</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendPassword\">后台修改密码</el-checkbox>\n                      </div>\n                    </div>\n                  </el-col>\n\n                  <!-- 前台功能 -->\n                  <el-col :span=\"8\">\n                    <div class=\"function-group\">\n                      <h5>前台功能</h5>\n                      <div class=\"function-checkboxes\">\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendAdd\">前台添加</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendEdit\">前台修改</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendDelete\">前台删除</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendDetail\">前台详情</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendList\">前台列表</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendLogin\">前台个人信息和密码</el-checkbox>\n                      </div>\n                    </div>\n                  </el-col>\n\n                  <!-- 小程序功能 -->\n                  <el-col :span=\"8\">\n                    <div class=\"function-group\">\n                      <h5>小程序功能</h5>\n                      <div class=\"function-checkboxes\">\n                        <el-checkbox v-model=\"currentTableDesign.functions.miniAdd\">小程序添加</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.miniEdit\">小程序编辑</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.miniDelete\">小程序删除</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.miniDetail\">小程序详情</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.miniList\">小程序List</el-checkbox>\n                      </div>\n                    </div>\n                  </el-col>\n                </el-row>\n              </div>\n            </div>\n\n            <!-- 字段设计内容 -->\n            <div v-show=\"currentTableTab === 'field-design'\" class=\"tab-content\">\n              <!-- 工具栏 -->\n              <div class=\"field-toolbar-compact\">\n                <el-space size=\"default\" wrap>\n                  <el-button type=\"success\" @click=\"addNewFieldToDesign\">\n                    <el-icon>\n                      <Plus />\n                    </el-icon>\n                    添加字段\n                  </el-button>\n                  <el-button type=\"warning\" @click=\"resetFields\">\n                    <el-icon>\n                      <Refresh />\n                    </el-icon>\n                    重置字段\n                  </el-button>\n                  <el-button type=\"danger\" @click=\"clearAllFields\">\n                    <el-icon>\n                      <Delete />\n                    </el-icon>\n                    清空字段\n                  </el-button>\n                  <el-divider direction=\"vertical\" />\n                  <el-text type=\"info\">\n                    当前字段数量: {{ currentTableDesign.fields?.length || 0 }}\n                  </el-text>\n                </el-space>\n              </div>\n\n              <!-- 字段表格 -->\n              <div class=\"field-table-section\">\n                <el-table :data=\"currentTableDesign.fields\" class=\"field-table\" border stripe\n                  empty-text=\"暂无字段，请点击添加字段按钮\" size=\"small\">\n                  <el-table-column label=\"序号\" type=\"index\" width=\"60\" align=\"center\" />\n\n                  <el-table-column label=\"中文名称\" min-width=\"100\">\n                    <template #default=\"{ row }\">\n                      <el-input v-model=\"row.chineseName\" placeholder=\"请输入中文名称\" size=\"small\" clearable />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"英文名称\" min-width=\"100\">\n                    <template #default=\"{ row }\">\n                      <el-input v-model=\"row.englishName\" placeholder=\"请输入英文名称\" size=\"small\" clearable />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"字段类型\" min-width=\"80\">\n                    <template #default=\"{ row }\">\n                      <el-select v-model=\"row.type\" placeholder=\"选择类型\" size=\"small\" style=\"width: 100%\">\n                        <el-option label=\"int\" value=\"int\" />\n                        <el-option label=\"varchar(50)\" value=\"varchar(50)\" />\n                        <el-option label=\"varchar(100)\" value=\"varchar(100)\" />\n                        <el-option label=\"varchar(200)\" value=\"varchar(200)\" />\n                        <el-option label=\"varchar(500)\" value=\"varchar(500)\" />\n                        <el-option label=\"text\" value=\"text\" />\n                        <el-option label=\"datetime\" value=\"datetime\" />\n                        <el-option label=\"decimal(10,2)\" value=\"decimal(10,2)\" />\n                      </el-select>\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"控件类型\" min-width=\"100\">\n                    <template #default=\"{ row }\">\n                      <el-select v-model=\"row.controlType\" placeholder=\"选择控件\" size=\"small\" style=\"width: 100%\">\n                        <el-option label=\"文本框\" value=\"文本框\" />\n                        <el-option label=\"多行文本\" value=\"多行文本\" />\n                        <el-option label=\"下拉框\" value=\"下拉框\" />\n                        <el-option label=\"单选按钮\" value=\"单选按钮\" />\n                        <el-option label=\"复选框\" value=\"复选框\" />\n                        <el-option label=\"日期选择\" value=\"日期选择\" />\n                        <el-option label=\"时间选择\" value=\"时间选择\" />\n                        <el-option label=\"文件上传\" value=\"文件上传\" />\n                        <el-option label=\"图片上传\" value=\"图片上传\" />\n                        <el-option label=\"编辑器\" value=\"编辑器\" />\n                        <el-option label=\"自动当前时间\" value=\"自动当前时间\" />\n                      </el-select>\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"必填\" width=\"60\" align=\"center\">\n                    <template #default=\"{ row }\">\n                      <el-checkbox v-model=\"row.required\" />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"搜索\" width=\"60\" align=\"center\">\n                    <template #default=\"{ row }\">\n                      <el-checkbox v-model=\"row.searchable\" />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"显示\" width=\"60\" align=\"center\">\n                    <template #default=\"{ row }\">\n                      <el-checkbox v-model=\"row.visible\" />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"存在\" width=\"60\" align=\"center\">\n                    <template #default=\"{ row }\">\n                      <el-checkbox v-model=\"row.existsCheck\" />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"操作\" width=\"160\" align=\"center\" fixed=\"right\">\n                    <template #default=\"{ row, $index }\">\n                      <div class=\"field-actions\">\n                        <el-button size=\"small\" type=\"primary\" @click=\"moveFieldUp($index)\" :disabled=\"$index === 0\"\n                          circle>\n                          <el-icon>\n                            <ArrowUp />\n                          </el-icon>\n                        </el-button>\n                        <el-button size=\"small\" type=\"primary\" @click=\"moveFieldDown($index)\"\n                          :disabled=\"$index === currentTableDesign.fields.length - 1\" circle>\n                          <el-icon>\n                            <ArrowDown />\n                          </el-icon>\n                        </el-button>\n                        <el-button size=\"small\" type=\"warning\" @click=\"editFieldSettings(row, $index)\" circle>\n                          <el-icon>\n                            <Edit />\n                          </el-icon>\n                        </el-button>\n                        <el-button size=\"small\" type=\"danger\" @click=\"deleteFieldFromDesign($index)\" circle>\n                          <el-icon>\n                            <Delete />\n                          </el-icon>\n                        </el-button>\n                      </div>\n                    </template>\n                  </el-table-column>\n                </el-table>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-space size=\"large\">\n            <el-button @click=\"closeTableDesignModal\" size=\"large\">\n              <el-icon>\n                <Close />\n              </el-icon>\n              <span>取消</span>\n            </el-button>\n            <el-button type=\"primary\" @click=\"saveTableDesign\" size=\"large\">\n              <el-icon>\n                <Check />\n              </el-icon>\n              <span>保存表设计</span>\n            </el-button>\n          </el-space>\n        </div>\n      </template>\n    </el-dialog>\n\n    <!-- AI生成器弹窗 -->\n    <el-dialog v-model=\"showAiGeneratorDialog\" title=\"AI生成表结构\" width=\"600px\" :close-on-click-modal=\"false\"\n      :before-close=\"hideAiGeneratorModal\" class=\"ai-generator-dialog\" destroy-on-close>\n      <div class=\"ai-generator-container\">\n        <div class=\"ai-generator-header\">\n          <h4>🤖 AI智能生成表结构</h4>\n          <p>描述您要生成的表结构，AI将自动为您创建完整的数据表设计</p>\n        </div>\n\n        <div class=\"ai-generator-body\">\n          <el-form label-width=\"100px\">\n            <el-form-item label=\"表描述\">\n              <el-input v-model=\"aiGeneratorInput\" type=\"textarea\" :rows=\"4\"\n                placeholder=\"请输入表的描述，例如：学生信息管理表、商品管理系统、订单管理表等...\" class=\"ai-generator-input\"\n                :disabled=\"aiGenerationInProgress\" />\n            </el-form-item>\n          </el-form>\n\n          <div class=\"ai-generator-example\">\n            <h5>💡 示例：</h5>\n            <div class=\"example-list\">\n              <div class=\"example-item\">\n                <strong>表结构描述：</strong>\"学生信息管理表\" 或 \"商品管理系统\" 或 \"订单管理表\"\n              </div>\n              <div class=\"example-item\">\n                <strong>功能说明：</strong>AI会根据您的描述自动生成包含合适字段的完整表结构\n              </div>\n            </div>\n          </div>\n        </div>\n\n      </div>\n\n      <template #footer>\n        <div class=\"ai-generator-footer\">\n          <el-button @click=\"hideAiGeneratorModal\" :disabled=\"aiGenerationInProgress\">取消</el-button>\n          <el-button type=\"primary\" @click=\"confirmAiGeneration\" :loading=\"aiGenerationInProgress\">\n            <el-icon v-if=\"!aiGenerationInProgress\">\n              <Cpu />\n            </el-icon>\n            {{ aiGenerationInProgress ? '生成中...' : '🚀 生成' }}\n          </el-button>\n        </div>\n      </template>\n    </el-dialog>\n\n    <!-- 后台模板选择弹窗 -->\n    <el-dialog v-model=\"showBackendTemplateDialog\" title=\"选择后台模板\" width=\"1000px\" :close-on-click-modal=\"false\"\n      class=\"template-dialog\" destroy-on-close>\n      <div class=\"template-container\">\n        <div class=\"template-grid-4col\" v-loading=\"templatesLoading\">\n          <div v-for=\"template in backendTemplates\" :key=\"template.sid\" class=\"template-item-4col\">\n            <div class=\"template-image\" @click=\"selectBackendTemplate(template)\">\n              <img v-if=\"template.memo4\" :src=\"getTemplateImageUrl(template.memo4)\" :alt=\"template.sname\"\n                @error=\"handleImageError\" />\n              <div v-else class=\"no-image\">\n                <el-icon>\n                  <Picture />\n                </el-icon>\n                <span>暂无预览</span>\n              </div>\n            </div>\n            <div class=\"template-info\">\n              <h4 @click=\"selectBackendTemplate(template)\">{{ template.sname }}</h4>\n              <p class=\"template-id\">模板ID: {{ template.sid }}</p>\n              <div class=\"template-actions\">\n                <el-button type=\"primary\" size=\"small\" @click=\"selectBackendTemplate(template)\">\n                  选择模板\n                </el-button>\n                <el-button type=\"info\" size=\"small\" @click=\"viewTemplateDetail(template)\">\n                  查看详情\n                </el-button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-dialog>\n\n    <!-- 前台模板选择弹窗 -->\n    <el-dialog v-model=\"showFrontendTemplateDialog\" title=\"选择前台模板\" width=\"1000px\" :close-on-click-modal=\"false\"\n      class=\"template-dialog\" destroy-on-close>\n      <div class=\"template-container\">\n        <div class=\"template-grid-4col\" v-loading=\"templatesLoading\">\n          <div v-for=\"template in frontendTemplates\" :key=\"template.sid\" class=\"template-item-4col\">\n            <div class=\"template-image\" @click=\"selectFrontendTemplate(template)\">\n              <img v-if=\"template.memo4\" :src=\"getTemplateImageUrl(template.memo4)\" :alt=\"template.sname\"\n                @error=\"handleImageError\" />\n              <div v-else class=\"no-image\">\n                <el-icon>\n                  <Picture />\n                </el-icon>\n                <span>暂无预览</span>\n              </div>\n            </div>\n            <div class=\"template-info\">\n              <h4 @click=\"selectFrontendTemplate(template)\">{{ template.sname }}</h4>\n              <p class=\"template-id\">模板ID: {{ template.sid }}</p>\n              <div class=\"template-actions\">\n                <el-button type=\"primary\" size=\"small\" @click=\"selectFrontendTemplate(template)\">\n                  选择模板\n                </el-button>\n                <el-button type=\"info\" size=\"small\" @click=\"viewTemplateDetail(template)\">\n                  查看详情\n                </el-button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-dialog>\n\n    <!-- 模板详情查看弹窗 -->\n    <el-dialog v-model=\"showTemplateDetailDialog\" title=\"模板详情\" width=\"90%\" :close-on-click-modal=\"false\"\n      class=\"template-detail-dialog\" destroy-on-close top=\"5vh\">\n      <div class=\"template-detail-container\" v-if=\"currentTemplateDetail\">\n        <div class=\"template-detail-header\">\n          <h3>{{ currentTemplateDetail.sname }}</h3>\n          <p class=\"template-detail-id\">模板ID: {{ currentTemplateDetail.sid }}</p>\n        </div>\n        <div class=\"template-detail-image-container\">\n          <div v-if=\"currentTemplateDetail.memo4\" class=\"template-detail-image\">\n            <el-image\n              :src=\"getTemplateImageUrl(currentTemplateDetail.memo4)\"\n              :alt=\"currentTemplateDetail.sname\"\n              fit=\"contain\"\n              :preview-src-list=\"[getTemplateImageUrl(currentTemplateDetail.memo4)]\"\n              :initial-index=\"0\"\n              preview-teleported\n              class=\"template-preview-image\"\n            />\n          </div>\n          <div v-else class=\"no-image-large\">\n            <el-icon>\n              <Picture />\n            </el-icon>\n            <span>暂无预览图片</span>\n          </div>\n        </div>\n        <div class=\"template-detail-tips\">\n          <el-alert\n            title=\"提示\"\n            type=\"info\"\n            :closable=\"false\"\n            show-icon>\n            <template #default>\n              点击图片可以放大查看，支持缩放和拖拽操作\n            </template>\n          </el-alert>\n        </div>\n      </div>\n      <template #footer>\n        <div class=\"template-detail-footer\">\n          <el-button @click=\"showTemplateDetailDialog = false\" size=\"large\">关闭</el-button>\n        </div>\n      </template>\n    </el-dialog>\n\n    <!-- 字段设置弹窗 -->\n    <el-dialog v-model=\"showFieldSettingsDialog\" title=\"字段设置\" width=\"600px\" :close-on-click-modal=\"false\"\n      class=\"field-settings-dialog\" destroy-on-close>\n      <div class=\"field-settings-container\" v-if=\"currentFieldSettings\">\n        <el-form :model=\"currentFieldSettings\" label-width=\"100px\" size=\"default\">\n          <el-form-item label=\"字段名称\">\n            <el-input v-model=\"currentFieldSettings.chineseName\" readonly />\n          </el-form-item>\n\n          <el-form-item label=\"关联表\">\n            <el-input v-model=\"currentFieldSettings.relatedTable\"\n              placeholder=\"例如：doro,dbid,dormitory,1\"\n              @click=\"showRelatedTableSelector\"\n              readonly\n              style=\"cursor: pointer;\">\n              <template #suffix>\n                <el-icon style=\"cursor: pointer;\">\n                  <View />\n                </el-icon>\n              </template>\n            </el-input>\n          </el-form-item>\n\n          <el-form-item label=\"是否必填\">\n            <el-checkbox v-model=\"showInSearchList\">搜索列表显示</el-checkbox>\n          </el-form-item>\n\n          <el-form-item label=\"自定义选项\">\n            <el-input v-model=\"currentFieldSettings.customOptions\"\n              type=\"textarea\"\n              :rows=\"6\"\n              placeholder=\"一行一个选项，例如：&#10;选项1&#10;选项2&#10;选项3\" />\n          </el-form-item>\n        </el-form>\n      </div>\n\n      <template #footer>\n        <div class=\"field-settings-footer\">\n          <el-button @click=\"closeFieldSettingsDialog\">取消</el-button>\n          <el-button type=\"primary\" @click=\"saveFieldSettings\">保存</el-button>\n        </div>\n      </template>\n    </el-dialog>\n\n    <!-- 关联表选择弹窗 -->\n    <el-dialog v-model=\"showRelatedTableDialog\" title=\"选择关联表\" width=\"800px\" :close-on-click-modal=\"false\"\n      class=\"related-table-dialog\" destroy-on-close>\n      <div class=\"related-table-container\">\n        <el-table :data=\"availableRelatedTables\" border stripe size=\"small\" max-height=\"400\">\n          <el-table-column label=\"主键ID\" prop=\"primaryKey\" width=\"80\" align=\"center\" />\n          <el-table-column label=\"名称\" prop=\"displayName\" width=\"120\" />\n          <el-table-column label=\"表名称\" prop=\"tableName\" width=\"150\" />\n          <el-table-column label=\"联动\" width=\"100\" align=\"center\">\n            <template #default=\"{ row }\">\n              <el-input v-model=\"row.linkValue\" size=\"small\" style=\"width: 60px;\" />\n            </template>\n          </el-table-column>\n          <el-table-column label=\"选择\" width=\"80\" align=\"center\">\n            <template #default=\"{ row }\">\n              <el-checkbox v-model=\"row.selected\" />\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n\n      <template #footer>\n        <div class=\"related-table-footer\">\n          <el-button @click=\"closeRelatedTableDialog\">取消</el-button>\n          <el-button type=\"primary\" @click=\"confirmRelatedTableSelection\">确定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<style scoped>\n  @import '../../styles/CodeGenerator.css';\n\n  /* 生成进度样式 */\n  .generation-progress {\n    margin-top: 20px;\n    padding: 20px;\n    background: #f8f9fa;\n    border-radius: 8px;\n    text-align: center;\n  }\n\n  .progress-text {\n    margin-top: 10px;\n    color: #606266;\n    font-size: 14px;\n  }\n\n  /* 生成结果样式 */\n  .generation-result {\n    margin-top: 30px;\n    padding: 20px;\n    border: 1px solid #e4e7ed;\n    border-radius: 8px;\n    background: #fff;\n  }\n\n  .generation-status {\n    display: flex;\n    align-items: center;\n    margin-bottom: 20px;\n    padding: 15px;\n    border-radius: 6px;\n  }\n\n  .generation-status.success {\n    background: #f0f9ff;\n    border: 1px solid #67c23a;\n    color: #67c23a;\n  }\n\n  .generation-status.error {\n    background: #fef0f0;\n    border: 1px solid #f56c6c;\n    color: #f56c6c;\n  }\n\n  .status-icon {\n    margin-right: 10px;\n    font-size: 18px;\n  }\n\n  .status-text {\n    font-weight: 500;\n    font-size: 16px;\n  }\n\n  /* 文件列表样式 */\n  .file-list-section {\n    margin-top: 20px;\n  }\n\n  .file-list {\n    list-style: none;\n    padding: 0;\n    margin: 0;\n  }\n\n  .file-list li {\n    display: flex;\n    align-items: center;\n    padding: 8px 12px;\n    margin-bottom: 4px;\n    border-radius: 4px;\n    background: #f8f9fa;\n  }\n\n  .file-list.success li {\n    background: #f0f9ff;\n    border-left: 3px solid #67c23a;\n  }\n\n  .file-list.error li {\n    background: #fef0f0;\n    border-left: 3px solid #f56c6c;\n  }\n\n  .file-icon {\n    margin-right: 8px;\n    color: #909399;\n  }\n\n  .file-name {\n    flex: 1;\n    font-family: 'Courier New', monospace;\n    font-size: 13px;\n  }\n\n  .file-error {\n    color: #f56c6c;\n    font-size: 12px;\n    margin-left: 10px;\n  }\n\n  /* 压缩结果样式 */\n  .compression-result {\n    margin-top: 20px;\n    padding: 15px;\n    border: 1px solid #e4e7ed;\n    border-radius: 6px;\n    background: #fafafa;\n  }\n\n  .compression-info {\n    display: flex;\n    align-items: center;\n    flex-wrap: wrap;\n  }\n\n  .compression-text {\n    margin-left: 10px;\n    font-weight: 500;\n  }\n\n  .compression-details {\n    width: 100%;\n    margin-top: 15px;\n    padding-top: 15px;\n    border-top: 1px solid #e4e7ed;\n  }\n\n  .compression-details p {\n    margin: 5px 0;\n    color: #606266;\n  }\n</style>\n\n<script>\n  import { ref, reactive, onMounted, nextTick, computed } from 'vue'\n  import { ElMessage, ElMessageBox } from 'element-plus'\n  import request, { base } from \"../../../utils/http\"\n  import {\n    Setting, Folder, Edit, Cpu, DataBoard, DocumentCopy, Warning,\n    Plus, View, Download, Delete, Document, Monitor, Box, Refresh,\n    User, ArrowDown, ArrowUp, Loading, Close, Check, Picture\n  } from '@element-plus/icons-vue'\n\n  export default {\n    name: 'CodeGenerator',\n    components: {\n      Setting, Folder, Edit, Cpu, DataBoard, DocumentCopy, Warning,\n      Plus, View, Download, Delete, Document, Monitor, Box, Refresh,\n      User, ArrowDown, ArrowUp, Loading, Close, Check, Picture\n    },\n    setup() {\n      const activeTab = ref('project')\n      const logLevel = ref('all')\n\n      // 登录相关状态\n      const isLoggedIn = ref(false)\n      const loginDialogVisible = ref(false)\n      const loginLoading = ref(false)\n      const loginFormRef = ref(null)\n\n      // 用户信息\n      const userInfo = reactive({\n        username: '',\n        loginTime: ''\n      })\n\n      // 登录表单\n      const loginForm = reactive({\n        username: '',\n        password: ''\n      })\n\n      // 登录表单验证规则\n      const loginRules = {\n        username: [\n          { required: true, message: '请输入用户名', trigger: 'blur' }\n        ],\n        password: [\n          { required: true, message: '请输入密码', trigger: 'blur' }\n        ]\n      }\n\n      // 项目选择和配置\n      const selectedProject = ref('')\n      const projectsLoading = ref(false)\n      const availableDatabases = ref([\n        { value: '', text: '请选择数据库' }\n      ])\n\n      const projectForm = reactive({\n        databaseType: 'mysql',\n        databaseMode: 'new',\n        projectCode: '',\n        databaseName: '',\n        selectedDatabase: '',\n        name: '',\n        packageName: 'com',\n        backendTemplate: '',\n        frontendTemplate: '',\n        layer: '否',\n        charts: '否',\n        schoolName: '',\n        adminId: '',\n        adminName: '',\n        adminRole: '',\n        adminLoginName: '',\n        copyProject: ''\n      })\n\n      // 项目表单相关\n      const currentProjectInfo = ref(null)\n      const projectTables = ref([])\n      const projectTablesLoading = ref(false)\n\n      const formFields = ref([\n        { name: 'id', type: 'Long' },\n        { name: 'name', type: 'String' },\n        { name: 'email', type: 'String' },\n        { name: 'createTime', type: 'Date' }\n      ])\n\n      const tableData = ref([\n        { name: 'user', fields: 8, status: '已配置', updateTime: '2024-01-15 10:30:00' },\n        { name: 'role', fields: 5, status: '未配置', updateTime: '2024-01-15 09:15:00' },\n        { name: 'permission', fields: 6, status: '已配置', updateTime: '2024-01-14 16:45:00' }\n      ])\n\n      const sqlContent = ref(``)\n\n      const logs = ref([\n        { time: '2024-01-15 10:30:15', level: 'info', message: '开始生成代码...' },\n        { time: '2024-01-15 10:30:16', level: 'success', message: '实体类生成成功' },\n        { time: '2024-01-15 10:30:17', level: 'warning', message: '字段名称建议使用驼峰命名' },\n        { time: '2024-01-15 10:30:18', level: 'error', message: '数据库连接失败，请检查配置' }\n      ])\n\n      // Cookie操作工具函数\n      const setCookie = (name, value, days) => {\n        const expires = new Date()\n        expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000))\n        document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`\n      }\n\n      const getCookie = (name) => {\n        const nameEQ = name + \"=\"\n        const ca = document.cookie.split(';')\n        for (let i = 0; i < ca.length; i++) {\n          let c = ca[i]\n          while (c.charAt(0) === ' ') c = c.substring(1, c.length)\n          if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length)\n        }\n        return null\n      }\n\n      const deleteCookie = (name) => {\n        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`\n      }\n\n      // 检查登录状态\n      const checkLoginStatus = () => {\n        // 首先检查sessionStorage中的用户信息\n        const sessionUser = sessionStorage.getItem(\"user\")\n        const sessionUserLname = sessionStorage.getItem(\"userLname\")\n\n        if (sessionUser && sessionUserLname) {\n          try {\n            JSON.parse(sessionUser) // 验证JSON格式\n            userInfo.username = sessionUserLname\n            userInfo.loginTime = new Date().toLocaleString()\n            isLoggedIn.value = true\n            return\n          } catch (error) {\n            console.error('解析sessionStorage用户信息失败:', error)\n          }\n        }\n\n        // 如果sessionStorage中没有，再检查Cookie\n        const savedUser = getCookie('codeGeneratorUser')\n        if (savedUser) {\n          try {\n            const userData = JSON.parse(decodeURIComponent(savedUser))\n            userInfo.username = userData.username\n            userInfo.loginTime = userData.loginTime\n            isLoggedIn.value = true\n          } catch (error) {\n            console.error('解析Cookie用户信息失败:', error)\n            deleteCookie('codeGeneratorUser')\n          }\n        } else {\n          loginDialogVisible.value = true\n        }\n      }\n\n      // 处理登录\n      const handleLogin = async () => {\n        if (!loginFormRef.value) return\n\n        // 基本验证\n        if (!loginForm.username) {\n          ElMessage.warning('请输入用户名')\n          return\n        }\n        if (!loginForm.password) {\n          ElMessage.warning('请输入密码')\n          return\n        }\n\n        try {\n          loginLoading.value = true\n\n          // 调用登录API\n          const url = base + \"/admin/login\"\n          const loginData = {\n            lname: loginForm.username,\n            pwd: loginForm.password\n          }\n\n          const res = await request.post(url, loginData)\n          loginLoading.value = false\n\n          if (res.code == 200) {\n            console.log('登录成功:', JSON.stringify(res.resdata))\n\n            // 保存用户信息到sessionStorage（参考管理员登录）\n            sessionStorage.setItem(\"user\", JSON.stringify(res.resdata))\n            sessionStorage.setItem(\"userLname\", res.resdata.lname)\n            sessionStorage.setItem(\"role\", \"管理员\")\n\n            // 更新本地状态\n            userInfo.username = res.resdata.lname\n            userInfo.loginTime = new Date().toLocaleString()\n\n            // 保存到Cookie，有效期15天\n            const userData = {\n              username: res.resdata.lname,\n              loginTime: userInfo.loginTime,\n              userId: res.resdata.id\n            }\n            setCookie('codeGeneratorUser', encodeURIComponent(JSON.stringify(userData)), 15)\n\n            isLoggedIn.value = true\n            loginDialogVisible.value = false\n\n            // 重置表单\n            loginForm.username = ''\n            loginForm.password = ''\n\n            ElMessage.success('登录成功！')\n          } else {\n            ElMessage.error(res.msg || '登录失败')\n          }\n        } catch (error) {\n          loginLoading.value = false\n          console.error('登录失败:', error)\n          ElMessage.error('登录失败，请检查网络连接')\n        }\n      }\n\n      // 处理用户下拉菜单命令\n      const handleUserCommand = (command) => {\n        if (command === 'logout') {\n          handleLogout()\n        }\n      }\n\n      // 退出登录\n      const handleLogout = () => {\n        // 清除Cookie\n        deleteCookie('codeGeneratorUser')\n\n        // 清除sessionStorage\n        sessionStorage.removeItem(\"user\")\n        sessionStorage.removeItem(\"userLname\")\n        sessionStorage.removeItem(\"role\")\n\n        // 重置状态\n        isLoggedIn.value = false\n        userInfo.username = ''\n        userInfo.loginTime = ''\n        loginDialogVisible.value = true\n\n        ElMessage.success('已退出登录')\n      }\n\n      // 项目类型选择\n      const selectProject = (projectType) => {\n        selectedProject.value = projectType\n        console.log('选择项目类型:', projectType)\n      }\n\n      // 获取项目名称\n      const getProjectName = (projectType) => {\n        const projectNames = {\n          'springboot-thymeleaf': 'SpringBoot + Thymeleaf',\n          'springboot-miniprogram': 'SpringBoot + 小程序',\n          'springboot-vue': 'SpringBoot + Vue',\n          'ssm-vue': 'SSM + Vue'\n        }\n        return projectNames[projectType] || projectType\n      }\n\n      // 打开模板选择弹窗\n      const openTemplateModal = () => {\n        console.log('打开后台模板选择弹窗')\n        showBackendTemplateSelector()\n      }\n\n      const openFrontTemplateModal = () => {\n        console.log('打开前台模板选择弹窗')\n        showFrontendTemplateSelector()\n      }\n\n      // 处理数据库模式变化\n      const handleDatabaseModeChange = (mode) => {\n        if (mode === 'existing') {\n          loadProjects()\n        } else {\n          // 清空已有数据库选项\n          availableDatabases.value = [{ value: '', text: '请选择数据库' }]\n          projectForm.selectedDatabase = ''\n        }\n      }\n\n      // 加载项目列表\n      const loadProjects = async () => {\n        try {\n          projectsLoading.value = true\n          const url = base + \"/projects/list?currentPage=1&pageSize=1000\"\n          const params = {\n\n          }\n\n          const res = await request.post(url, { params })\n\n          if (res.code === 200) {\n            const projects = res.resdata || []\n            availableDatabases.value = [\n              { value: '', text: '请选择数据库' },\n              ...projects.map(project => ({\n                value: project.pid,\n                text: `${project.pno}--${project.daname} (${project.pname})`\n              }))\n            ]\n          } else {\n            ElMessage.error('加载项目列表失败')\n          }\n        } catch (error) {\n          console.error('加载项目列表失败:', error)\n          ElMessage.error('加载项目列表失败，请检查网络连接')\n        } finally {\n          projectsLoading.value = false\n        }\n      }\n\n      // 处理项目选择\n      const handleProjectSelect = async (projectId) => {\n        if (!projectId) return\n\n        try {\n          const url = base + \"/projects/get?id=\" + projectId;\n\n\n          const res = await request.post(url, {})\n\n          if (res.code === 200) {\n            const project = res.resdata\n            // 初始化表单数据\n            projectForm.projectCode = project.pno || ''\n            projectForm.databaseName = project.daname || ''\n            projectForm.name = project.pname || ''\n            projectForm.databaseType = project.dtype || 'mysql'\n            projectForm.backendTemplate = project.by1 || ''\n            projectForm.frontendTemplate = project.by2 || ''\n            projectForm.layer = project.by4 || '否'\n            projectForm.charts = project.by5 || '否'\n            projectForm.schoolName = project.by6 || ''\n\n            // 解析Session信息\n            if (project.by3) {\n              const sessionInfo = project.by3.split(',')\n              projectForm.adminId = sessionInfo[0] || ''\n              projectForm.adminName = sessionInfo[1] || ''\n              projectForm.adminRole = sessionInfo[2] || ''\n              projectForm.adminLoginName = sessionInfo[3] || ''\n            }\n\n            ElMessage.success('项目信息加载成功')\n          } else {\n            ElMessage.error('加载项目详情失败')\n          }\n        } catch (error) {\n          console.error('加载项目详情失败:', error)\n          ElMessage.error('加载项目详情失败，请检查网络连接')\n        }\n      }\n\n      // 保存或更新项目到数据库\n      const saveOrUpdateProject = async () => {\n        try {\n          // 构建Session信息\n          const sessionInfo = [\n            projectForm.adminId,\n            projectForm.adminName,\n            projectForm.adminRole,\n            projectForm.adminLoginName\n          ].join(',')\n\n          const projectData = {\n            ptype: selectedProject.value,\n            dtype: projectForm.databaseType,\n            pflag: projectForm.databaseMode === 'new' ? '1' : '2',\n            pno: projectForm.projectCode,\n            daname: projectForm.databaseName,\n            pname: projectForm.name,\n            by1: projectForm.backendTemplate,\n            by2: projectForm.frontendTemplate,\n            by3: sessionInfo,\n            by4: projectForm.layer,\n            by5: projectForm.charts,\n            by6: projectForm.schoolName,\n            by7: projectForm.copyProject,\n            lname: userInfo.username\n          }\n\n          // 判断是新建还是更新\n          const isUpdate = currentProjectInfo.value && currentProjectInfo.value.pid\n          let url, res\n\n          if (isUpdate) {\n            // 更新项目\n            projectData.pid = currentProjectInfo.value.pid\n            url = base + \"/projects/update\"\n            res = await request.post(url, projectData)\n          } else {\n            // 新建项目\n            url = base + \"/projects/add\"\n            res = await request.post(url, projectData)\n          }\n\n          if (res.code === 200) {\n            const message = isUpdate ? '项目更新成功' : '项目保存成功'\n            ElMessage.success(message)\n\n            // 如果是新建项目，保存返回的项目ID\n            if (!isUpdate && res.resdata && res.resdata.pid) {\n              currentProjectInfo.value = {\n                ...currentProjectInfo.value,\n                pid: res.resdata.pid\n              }\n            }\n\n            return { success: true, projectId: res.resdata?.pid || currentProjectInfo.value?.pid }\n          } else {\n            ElMessage.error(res.msg || '项目保存失败')\n            return { success: false }\n          }\n        } catch (error) {\n          console.error('保存项目失败:', error)\n          ElMessage.error('保存项目失败，请检查网络连接')\n          return { success: false }\n        }\n      }\n\n      // 加载项目表单列表\n      const loadProjectTables = async (projectId) => {\n        try {\n          projectTablesLoading.value = true\n          const url = base + \"/tables/list?currentPage=1&pageSize=1000\"\n          const params = {\n            pid: projectId\n          }\n\n          const res = await request.post(url, params)\n\n          if (res.code === 200) {\n            projectTables.value = res.resdata || []\n\n            // 为每个表加载字段数据\n            for (let table of projectTables.value) {\n              try {\n                const fieldsUrl = base + \"/mores/list?currentPage=1&pageSize=100\"\n                const fieldsRes = await request.post(fieldsUrl, { tid: table.tid })\n\n                if (fieldsRes.code === 200 && fieldsRes.resdata) {\n                  // 将字段数据转换为前端格式\n                  table.fields = fieldsRes.resdata.map(field => ({\n                    id: field.mid,\n                    tid: field.tid,\n                    chineseName: field.mozname,\n                    englishName: field.moname,\n                    type: field.motype,\n                    controlType: field.moflag,\n                    required: field.moyz === '1',\n                    searchable: field.mobt === '1',\n                    visible: field.by1 === '1',\n                    existsCheck: field.by2 === '1',\n                    relatedTable: field.by3 || '', // 关联表\n                    customOptions: field.by4 || '' // 自定义选项\n                  }))\n\n                  // 设置表的生成数据条数\n                  table.generateDataCount = parseInt(table.by1 || '0')\n                } else {\n                  table.fields = []\n                }\n              } catch (error) {\n                console.warn('加载表字段失败:', table.tname, error)\n                table.fields = []\n              }\n            }\n\n            console.log('加载项目表单成功:', projectTables.value)\n          } else {\n            ElMessage.error('加载项目表单失败')\n          }\n        } catch (error) {\n          console.error('加载项目表单失败:', error)\n          ElMessage.error('加载项目表单失败，请检查网络连接')\n        } finally {\n          projectTablesLoading.value = false\n        }\n      }\n\n      // 表设计相关\n      const showTableDesignModal = ref(false)\n      const currentTableTab = ref('table-settings')\n      const currentTableDesign = ref({\n        id: null,\n        chineseName: '',\n        englishName: '',\n        menuOrder: 1,\n        generateData: '0',\n        generateDataCount: 0,\n        functions: {\n          backendAdd: true, backendEdit: true, backendDelete: true,\n          backendDetail: true, backendList: false, batchImport: false,\n          batchExport: false, backendLogin: false, backendRegister: false,\n          backendProfile: false, backendPassword: false,\n          frontendAdd: false, frontendEdit: false, frontendDelete: false,\n          frontendDetail: false, frontendList: false, frontendLogin: false,\n          miniAdd: false, miniEdit: false, miniDelete: false,\n          miniDetail: false, miniList: false\n        },\n        fields: [\n          {\n            id: 1,\n            chineseName: '主键ID',\n            englishName: 'id',\n            type: 'int',\n            controlType: '文本框',\n            required: true,\n            searchable: false,\n            visible: true,\n            existsCheck: false,\n            relatedTable: '',\n            customOptions: ''\n          }\n        ]\n      })\n\n      // 功能选择的响应式数组\n      const backendFunctions = ref([])\n      const frontendFunctions = ref([])\n      const miniFunctions = ref([])\n\n      // 重置字段\n      const resetFields = () => {\n        currentTableDesign.value.fields = [\n          {\n            id: 1,\n            chineseName: '主键ID',\n            englishName: 'id',\n            type: 'int',\n            controlType: '文本框',\n            required: true,\n            searchable: false,\n            visible: true,\n            existsCheck: false\n          }\n        ]\n        ElMessage.success('字段已重置')\n      }\n\n      // AI生成器相关状态\n      const showAiGeneratorDialog = ref(false)\n      const aiGeneratorInput = ref('')\n      const aiGenerationInProgress = ref(false)\n\n      // 模板选择相关状态\n      const showBackendTemplateDialog = ref(false)\n      const showFrontendTemplateDialog = ref(false)\n      const showTemplateDetailDialog = ref(false)\n      const backendTemplates = ref([])\n      const frontendTemplates = ref([])\n      const templatesLoading = ref(false)\n      const currentTemplateDetail = ref(null)\n\n      // 项目生成相关状态\n      const generationInProgress = ref(false)\n      const generationResult = ref(null)\n\n      // SQL脚本生成相关状态\n      const sqlGenerationInProgress = ref(false)\n\n      // 数据脚本生成相关状态\n      const dataGenerationInProgress = ref(false)\n      const dataContent = ref('')\n\n      // 字段设置相关状态\n      const showFieldSettingsDialog = ref(false)\n      const currentFieldSettings = ref(null)\n      const currentFieldIndex = ref(-1)\n      const showInSearchList = ref(false)\n\n      // 关联表选择相关状态\n      const showRelatedTableDialog = ref(false)\n      const availableRelatedTables = ref([])\n\n      // 显示AI生成器弹窗\n      const showAiGeneratorModal = () => {\n        aiGeneratorInput.value = ''\n        showAiGeneratorDialog.value = true\n        nextTick(() => {\n          // 聚焦到输入框\n          const inputElement = document.querySelector('.ai-generator-input')\n          if (inputElement) {\n            inputElement.focus()\n          }\n        })\n      }\n\n      // 隐藏AI生成器弹窗\n      const hideAiGeneratorModal = () => {\n        showAiGeneratorDialog.value = false\n        aiGeneratorInput.value = ''\n      }\n\n      // 确认AI生成\n      const confirmAiGeneration = async () => {\n        const description = aiGeneratorInput.value.trim()\n        if (!description) {\n          ElMessage.warning('请输入描述内容')\n          return\n        }\n\n        // 隐藏弹窗\n        hideAiGeneratorModal()\n\n        // 调用AI生成\n        await doubaoGenerate(description)\n      }\n\n      // 调用豆包AI生成表单\n      const doubaoGenerate = async (str) => {\n        if (aiGenerationInProgress.value) {\n          ElMessage.warning('AI正在生成中，请稍候...')\n          return\n        }\n\n        // 构建prompt\n        const input_text = \"用户:users\\n\" +\n          \"aid|lname|password|role\\n\" +\n          \"用户id|用户名|密码|身份\\n\" +\n          \"int|varchar(50)|varchar(50)|int\\n\" +\n          \"\\n\" +\n          \"学习上面的格式。格式说明如下。\\n\" +\n          \"第1行表中文名称:表英文名称。\\n\" +\n          \"第2行字段列表，字段简写\\n\" +\n          \"第3行字段对应中文\\n\" +\n          \"第4行字段类型。如果是字符型加上长度。\\n\" +\n          \"\\n\" +\n          \"按上面的格式生成下面的内容。不要注释，只返回格式的内容\\n\" + str\n\n        console.log('发送给豆包AI的内容:', input_text)\n\n        const settings = {\n          url: \"https://ark.cn-beijing.volces.com/api/v3/chat/completions\",\n          method: \"POST\",\n          timeout: 30000,\n          headers: {\n            \"Authorization\": \"Bearer 8d71b27a-b4c9-484e-896b-247f7dda5412\",\n            \"Content-Type\": \"application/json\"\n          },\n          data: JSON.stringify({\n            \"model\": \"doubao-1.5-pro-32k-250115\",\n            \"messages\": [\n              {\n                \"role\": \"system\",\n                \"content\": \"你是一个数据库设计专家，专门帮助用户设计数据表结构。请严格按照指定的格式返回结果，不要添加任何额外的说明或注释，表名和字段中不要用下划线，不要大写字母。不要用关键字和保留字\"\n              },\n              {\n                \"role\": \"user\",\n                \"content\": input_text\n              }\n            ]\n          })\n        }\n\n        // 显示生成中状态\n        aiGenerationInProgress.value = true\n        ElMessage.info('正在调用豆包AI生成表结构，请稍候...')\n\n        try {\n          const response = await fetch(settings.url, {\n            method: settings.method,\n            headers: settings.headers,\n            body: settings.data\n          })\n\n          if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`)\n          }\n\n          const result = await response.json()\n          aiGenerationInProgress.value = false\n\n          // 从豆包AI响应中提取内容\n          const generatedContent = result.choices[0].message.content\n          console.log('豆包AI生成的原始内容:', generatedContent)\n\n          // 清理返回内容\n          const cleanedContent = generatedContent\n            .replace(/```[\\s\\S]*?\\n/, '') // 移除开头的markdown代码块标记\n            .replace(/\\n```[\\s\\S]*?$/, '') // 移除结尾的markdown代码块标记\n            .replace(/^\\s+|\\s+$/g, '') // 移除首尾空白\n            .replace(/\\r\\n/g, '\\n') // 统一换行符\n            .replace(/\\r/g, '\\n') // 处理Mac格式换行符\n\n          console.log('清理后的内容:', cleanedContent)\n\n          // 检查内容是否为空\n          if (!cleanedContent || cleanedContent.trim() === '') {\n            throw new Error('豆包AI返回的内容为空')\n          }\n\n          createFormFromAI(cleanedContent)\n          ElMessage.success('AI生成成功！')\n\n        } catch (error) {\n          aiGenerationInProgress.value = false\n          console.error('处理豆包AI响应时出错:', error)\n          ElMessage.error('AI生成失败：' + error.message)\n        }\n      }\n\n      // 解析AI生成的内容并创建表单\n      const createFormFromAI = (content) => {\n        try {\n          console.log('开始解析AI生成的内容:', content)\n\n          // 按行分割内容，移除空行\n          const allLines = content.split('\\n')\n          const lines = allLines.map(line => line.trim()).filter(line => line !== '')\n          console.log('过滤后的有效行:', lines)\n\n          if (lines.length < 4) {\n            throw new Error(`豆包AI返回的格式不完整，需要4行内容，实际只有${lines.length}行`)\n          }\n\n          // 解析第1行：表名\n          const tableNameLine = lines[0]\n          const tableNameMatch = tableNameLine.match(/^(.+):(.+)$/)\n          if (!tableNameMatch) {\n            throw new Error('表名格式不正确，应为：中文名:英文名，实际为：' + tableNameLine)\n          }\n\n          const chineseName = tableNameMatch[1].trim()\n          const englishName = tableNameMatch[2].trim()\n\n          // 解析第2行：英文字段名\n          const englishFields = lines[1].split('|').map(field => field.trim()).filter(field => field !== '')\n\n          // 解析第3行：中文字段名\n          const chineseFields = lines[2].split('|').map(field => field.trim()).filter(field => field !== '')\n\n          // 解析第4行：字段类型\n          const fieldTypes = lines[3].split('|').map(type => type.trim()).filter(type => type !== '')\n\n          // 验证字段数量一致性\n          if (englishFields.length !== chineseFields.length || englishFields.length !== fieldTypes.length) {\n            throw new Error(`字段数量不匹配：英文名${englishFields.length}个，中文名${chineseFields.length}个，类型${fieldTypes.length}个`)\n          }\n\n          // 构建字段数据\n          const fields = []\n          for (let i = 0; i < englishFields.length; i++) {\n            const field = {\n              id: Date.now() + i,\n              chineseName: chineseFields[i],\n              englishName: englishFields[i],\n              type: fieldTypes[i],\n              controlType: inferControlType(fieldTypes[i]),\n              required: true, // AI生成的字段默认必填项选中\n              searchable: i > 0 && i < 3, // 前几个字段设为可搜索\n              visible: true,\n              existsCheck: false\n            }\n            fields.push(field)\n          }\n\n          // 完整替换表结构\n          currentTableDesign.value.chineseName = chineseName\n          currentTableDesign.value.englishName = englishName\n          currentTableDesign.value.fields = fields\n\n          // 确保functions对象存在\n          if (!currentTableDesign.value.functions) {\n            currentTableDesign.value.functions = {}\n          }\n\n          // 默认选中后台功能\n          currentTableDesign.value.functions.backendAdd = true\n          currentTableDesign.value.functions.backendEdit = true\n          currentTableDesign.value.functions.backendDelete = true\n          currentTableDesign.value.functions.backendDetail = true\n          currentTableDesign.value.functions.backendList = false\n\n          console.log('表单创建成功:', {\n            chineseName: chineseName,\n            englishName: englishName,\n            fieldsCount: fields.length\n          })\n\n        } catch (error) {\n          console.error('解析AI内容失败:', error)\n          ElMessage.error('解析AI生成的内容失败：' + error.message)\n        }\n      }\n\n      // 根据字段类型推断控件类型\n      const inferControlType = (fieldType) => {\n        const type = fieldType.toLowerCase()\n\n        if (type.includes('int') || type.includes('bigint')) {\n          return '文本框'\n        } else if (type.includes('decimal') || type.includes('float') || type.includes('double')) {\n          return '文本框'\n        } else if (type.includes('text') || type.includes('longtext')) {\n          return '多行文本'\n        } else if (type.includes('datetime') || type.includes('timestamp')) {\n          return '日期时间'\n        } else if (type.includes('date')) {\n          return '日期选择'\n        } else if (type.includes('varchar') && type.includes('200')) {\n          return '多行文本'\n        } else if (type.includes('varchar')) {\n          return '文本框'\n        } else {\n          return '文本框'\n        }\n      }\n\n      // 模板选择相关函数\n\n      // 显示后台模板选择弹窗\n      const showBackendTemplateSelector = async () => {\n        showBackendTemplateDialog.value = true\n        await loadBackendTemplates()\n      }\n\n      // 显示前台模板选择弹窗\n      const showFrontendTemplateSelector = async () => {\n        showFrontendTemplateDialog.value = true\n        await loadFrontendTemplates()\n      }\n\n      // 加载后台模板\n      const loadBackendTemplates = async () => {\n        try {\n          templatesLoading.value = true\n          const response = await fetch(base+'/small/backend-templates')\n          const result = await response.json()\n          if (result.code === 200) {\n            backendTemplates.value = result.resdata || []\n          } else {\n            ElMessage.error('加载后台模板失败')\n          }\n        } catch (error) {\n          console.error('加载后台模板失败:', error)\n          ElMessage.error('加载后台模板失败')\n        } finally {\n          templatesLoading.value = false\n        }\n      }\n\n      // 加载前台模板\n      const loadFrontendTemplates = async () => {\n        try {\n          templatesLoading.value = true\n          const response = await fetch(base+'/small/frontend-templates')\n          const result = await response.json()\n          if (result.code === 200) {\n            frontendTemplates.value = result.resdata || []\n          } else {\n            ElMessage.error('加载前台模板失败')\n          }\n        } catch (error) {\n          console.error('加载前台模板失败:', error)\n          ElMessage.error('加载前台模板失败')\n        } finally {\n          templatesLoading.value = false\n        }\n      }\n\n      // 选择后台模板\n      const selectBackendTemplate = (template) => {\n        // 设置到项目表单的后台模板字段 - 保存模板ID\n        projectForm.backendTemplate = template.sid\n        showBackendTemplateDialog.value = false\n        ElMessage.success(`已选择后台模板: ${template.sname} (ID: ${template.sid})`)\n      }\n\n      // 选择前台模板\n      const selectFrontendTemplate = (template) => {\n        // 设置到项目表单的前台模板字段 - 保存模板ID\n        projectForm.frontendTemplate = template.sid\n        showFrontendTemplateDialog.value = false\n        ElMessage.success(`已选择前台模板: ${template.sname} (ID: ${template.sid})`)\n      }\n\n      // 查看模板详情\n      const viewTemplateDetail = (template) => {\n        currentTemplateDetail.value = template\n        showTemplateDetailDialog.value = true\n      }\n\n      // 计算属性：是否可以生成项目\n      const canGenerate = computed(() => {\n        return projectForm.name &&\n               projectForm.databaseName &&\n               projectForm.projectCode &&\n               projectTables.value.length > 0 &&\n               !generationInProgress.value\n      })\n\n      // 生成项目\n      const generateProject = async () => {\n        if (!canGenerate.value) {\n          ElMessage.warning('请完善项目配置信息')\n          return\n        }\n\n        try {\n          generationInProgress.value = true\n          generationResult.value = null\n\n          // 构建项目数据\n          const projectData = {\n            projectNumber: projectForm.projectCode,\n            databaseName: projectForm.databaseName,\n            projectName: projectForm.name,\n            packageName: projectForm.packageName || 'com',\n            databaseType: projectForm.databaseType || 'mysql',\n            backendTemplate: projectForm.backendTemplate,\n            frontendTemplate: projectForm.frontendTemplate,\n            tables: projectTables.value.map(table => ({\n              id: table.tid,\n              chineseName: table.tword,\n              englishName: table.tname,\n              functions: table.tgn ? JSON.parse(table.tgn) : {},\n              fields: table.fields || []\n            }))\n          }\n\n          console.log('开始生成项目:', projectData)\n\n          // 调用Java后端API生成项目\n          const response = await fetch(base + '/projects/generate', {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(projectData)\n          })\n\n          const result = await response.json()\n\n          if (result.code === 200) {\n            // 适配后端返回的数据结构\n            const filesData = result.resdata.files && result.resdata.files.data ? result.resdata.files.data : {}\n            const compressionData = result.resdata.compression || null\n\n            generationResult.value = {\n              status: 'success',\n              message: '项目生成成功！',\n              files: filesData,\n              compression: compressionData\n            }\n\n            // 生成项目成功后，自动生成SQL脚本和数据脚本\n            await generateSqlScript()\n            await generateDataScript()\n\n            ElMessage.success('项目生成成功！')\n          } else {\n            throw new Error(result.msg || '项目生成失败')\n          }\n\n        } catch (error) {\n          console.error('项目生成失败:', error)\n          generationResult.value = {\n            status: 'error',\n            message: '项目生成失败：' + error.message,\n            files: null,\n            compression: null\n          }\n          ElMessage.error('项目生成失败：' + error.message)\n        } finally {\n          generationInProgress.value = false\n        }\n      }\n\n      // 生成SQL脚本\n      const generateSqlScript = async () => {\n        if (!currentProjectInfo.value || !projectTables.value || projectTables.value.length === 0) {\n          ElMessage.warning('请先配置项目和数据表')\n          return\n        }\n\n        try {\n          sqlGenerationInProgress.value = true\n\n          // 根据数据库类型生成对应的SQL脚本\n          const databaseType = projectForm.databaseType || 'mysql'\n          let script = ''\n\n          if (databaseType === 'mysql') {\n            script = generateMySqlScript()\n          } else if (databaseType === 'sqlserver') {\n            script = generateSqlServerScript()\n          }\n\n          sqlContent.value = script\n          ElMessage.success('SQL脚本生成成功！')\n\n        } catch (error) {\n          console.error('生成SQL脚本失败:', error)\n          ElMessage.error('生成SQL脚本失败：' + error.message)\n        } finally {\n          sqlGenerationInProgress.value = false\n        }\n      }\n\n      // 生成MySQL脚本\n      const generateMySqlScript = () => {\n        let script = `-- ${projectForm.name || '项目'} 数据库脚本 (MySQL)\\n`\n        script += `-- 数据库名称: ${projectForm.databaseName}\\n`\n        script += `-- 生成时间: ${new Date().toLocaleString()}\\n\\n`\n\n        // 创建数据库\n        script += `CREATE DATABASE IF NOT EXISTS \\`${projectForm.databaseName}\\` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\\n`\n        script += `USE \\`${projectForm.databaseName}\\`;\\n\\n`\n\n        // 为每个表生成建表语句\n        projectTables.value.forEach(table => {\n          script += generateMySqlTableScript(table)\n          script += '\\n'\n        })\n\n        return script\n      }\n\n      // 生成SQL Server脚本\n      const generateSqlServerScript = () => {\n        let script = `-- ${projectForm.name || '项目'} 数据库脚本 (SQL Server)\\n`\n        script += `-- 数据库名称: ${projectForm.databaseName}\\n`\n        script += `-- 生成时间: ${new Date().toLocaleString()}\\n\\n`\n\n        // 创建数据库\n        script += `IF NOT EXISTS (SELECT * FROM sys.databases WHERE name = '${projectForm.databaseName}')\\n`\n        script += `CREATE DATABASE [${projectForm.databaseName}];\\n`\n        script += `GO\\n\\n`\n        script += `USE [${projectForm.databaseName}];\\n`\n        script += `GO\\n\\n`\n\n        // 为每个表生成建表语句\n        projectTables.value.forEach(table => {\n          script += generateSqlServerTableScript(table)\n          script += '\\n'\n        })\n\n        return script\n      }\n\n      // 生成MySQL表脚本\n      const generateMySqlTableScript = (table) => {\n        let script = `-- 表: ${table.tword || table.tname}\\n`\n        script += `DROP TABLE IF EXISTS \\`${table.tname}\\`;\\n`\n        script += `CREATE TABLE \\`${table.tname}\\` (\\n`\n\n        const fields = table.fields || []\n        const fieldScripts = fields.map((field, index) => {\n          let fieldScript = `  \\`${field.englishName}\\` ${convertToMySqlType(field.type)}`\n\n          // 主键处理 - 修复第一个字段的AUTO_INCREMENT问题\n          if (field.englishName.toLowerCase() === 'id' || index === 0) {\n            // 只有int类型的字段才能使用AUTO_INCREMENT\n            if (field.type && field.type.toLowerCase() === 'int') {\n              fieldScript += ' AUTO_INCREMENT NOT NULL'\n            } else {\n              fieldScript += ' NOT NULL'\n            }\n          } else if (field.required) {\n            fieldScript += ' NOT NULL'\n          }\n\n          // 注释\n          if (field.chineseName) {\n            fieldScript += ` COMMENT '${field.chineseName}'`\n          }\n\n          return fieldScript\n        })\n\n        script += fieldScripts.join(',\\n')\n\n        // 主键约束\n        if (fields.length > 0) {\n          const primaryKey = fields[0].englishName\n          script += `,\\n  PRIMARY KEY (\\`${primaryKey}\\`)`\n        }\n\n        script += `\\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='${table.tword || table.tname}';\\n\\n`\n\n        return script\n      }\n\n      // 生成SQL Server表脚本\n      const generateSqlServerTableScript = (table) => {\n        let script = `-- 表: ${table.tword || table.tname}\\n`\n        script += `IF OBJECT_ID('[${table.tname}]', 'U') IS NOT NULL DROP TABLE [${table.tname}];\\n`\n        script += `CREATE TABLE [${table.tname}] (\\n`\n\n        const fields = table.fields || []\n        const fieldScripts = fields.map((field, index) => {\n          let fieldScript = `  [${field.englishName}] ${convertToSqlServerType(field.type)}`\n\n          // 主键处理\n          if (field.englishName.toLowerCase() === 'id' || index === 0) {\n            fieldScript += ' IDENTITY(1,1)'\n          }\n\n          // 非空约束\n          if (field.required) {\n            fieldScript += ' NOT NULL'\n          }\n\n          return fieldScript\n        })\n\n        script += fieldScripts.join(',\\n')\n\n        // 主键约束\n        if (fields.length > 0) {\n          const primaryKey = fields[0].englishName\n          script += `,\\n  CONSTRAINT [PK_${table.tname}] PRIMARY KEY ([${primaryKey}])`\n        }\n\n        script += `\\n);\\n`\n\n        // 添加表注释\n        if (table.tword) {\n          script += `EXEC sp_addextendedproperty 'MS_Description', '${table.tword}', 'SCHEMA', 'dbo', 'TABLE', '${table.tname}';\\n`\n        }\n\n        // 添加字段注释\n        fields.forEach(field => {\n          if (field.chineseName) {\n            script += `EXEC sp_addextendedproperty 'MS_Description', '${field.chineseName}', 'SCHEMA', 'dbo', 'TABLE', '${table.tname}', 'COLUMN', '${field.englishName}';\\n`\n          }\n        })\n\n        script += `GO\\n\\n`\n\n        return script\n      }\n\n      // 转换为MySQL数据类型\n      const convertToMySqlType = (type) => {\n        if (!type) return 'VARCHAR(100)'\n\n        const lowerType = type.toLowerCase()\n        if (lowerType === 'int') return 'INT'\n        if (lowerType.includes('varchar')) return type.toUpperCase()\n        if (lowerType === 'text') return 'TEXT'\n        if (lowerType === 'datetime') return 'DATETIME'\n        if (lowerType.includes('decimal')) return type.toUpperCase()\n\n        return type.toUpperCase()\n      }\n\n      // 转换为SQL Server数据类型\n      const convertToSqlServerType = (type) => {\n        if (!type) return 'NVARCHAR(100)'\n\n        const lowerType = type.toLowerCase()\n        if (lowerType === 'int') return 'INT'\n        if (lowerType.includes('varchar')) {\n          // 将varchar转换为nvarchar\n          return type.replace(/varchar/i, 'NVARCHAR')\n        }\n        if (lowerType === 'text') return 'NTEXT'\n        if (lowerType === 'datetime') return 'DATETIME'\n        if (lowerType.includes('decimal')) return type.toUpperCase()\n\n        return type.toUpperCase()\n      }\n\n      // 导出SQL脚本\n      const exportSqlScript = () => {\n        if (!sqlContent.value || sqlContent.value.trim() === '') {\n          ElMessage.warning('请先生成SQL脚本')\n          return\n        }\n\n        try {\n          const blob = new Blob([sqlContent.value], { type: 'text/plain;charset=utf-8' })\n          const url = window.URL.createObjectURL(blob)\n          const link = document.createElement('a')\n          link.href = url\n\n          const databaseType = projectForm.databaseType || 'mysql'\n          const fileName = `${projectForm.databaseName || 'database'}_${databaseType}.sql`\n          link.download = fileName\n\n          document.body.appendChild(link)\n          link.click()\n          document.body.removeChild(link)\n          window.URL.revokeObjectURL(url)\n\n          ElMessage.success('SQL脚本导出成功！')\n        } catch (error) {\n          console.error('导出SQL脚本失败:', error)\n          ElMessage.error('导出SQL脚本失败：' + error.message)\n        }\n      }\n\n      // 复制SQL脚本到剪切板\n      const copySqlScript = async () => {\n        if (!sqlContent.value || sqlContent.value.trim() === '') {\n          ElMessage.warning('请先生成SQL脚本')\n          return\n        }\n\n        try {\n          await navigator.clipboard.writeText(sqlContent.value)\n          ElMessage.success('SQL脚本已复制到剪切板！')\n        } catch (error) {\n          console.error('复制到剪切板失败:', error)\n          // 降级方案：使用传统的复制方法\n          try {\n            const textArea = document.createElement('textarea')\n            textArea.value = sqlContent.value\n            document.body.appendChild(textArea)\n            textArea.select()\n            document.execCommand('copy')\n            document.body.removeChild(textArea)\n            ElMessage.success('SQL脚本已复制到剪切板！')\n          } catch (fallbackError) {\n            console.error('降级复制方法也失败:', fallbackError)\n            ElMessage.error('复制到剪切板失败，请手动复制')\n          }\n        }\n      }\n\n      // 生成数据脚本\n      const generateDataScript = async () => {\n        if (!currentProjectInfo.value || !projectTables.value || projectTables.value.length === 0) {\n          ElMessage.warning('请先配置项目和数据表')\n          return\n        }\n\n        try {\n          dataGenerationInProgress.value = true\n\n          // 筛选出需要生成数据的表（生成数据条数大于0）\n          const tablesWithData = projectTables.value.filter(table => {\n            const dataCount = parseInt(table.by1 || '0')\n            return dataCount > 0\n          })\n\n          if (tablesWithData.length === 0) {\n            ElMessage.warning('没有设置生成数据条数的表，请先在表设计中设置生成数据条数')\n            return\n          }\n\n          let script = ''\n\n          // 为每个需要生成数据的表生成建表语句和插入数据\n          for (const table of tablesWithData) {\n            script += generateTableWithDataScript(table)\n            script += '\\n'\n          }\n\n          // 添加生成要求说明\n          script += `项目名称是：${projectForm.name || '智慧社区网格化管理系统'}\\n`\n          script += `按要求生成的条数，生成上面所有的数据，数据内容要多一些，数据模拟真实的数据\\n`\n          script += `如果有密码，密码为123456。\\n`\n          script += `时间字段为当前时间\\n`\n          script += `生成的数据为中文，只生成insert into数据，不要注释说明\\n`\n\n          dataContent.value = script\n          ElMessage.success('数据脚本生成成功！')\n\n        } catch (error) {\n          console.error('生成数据脚本失败:', error)\n          ElMessage.error('生成数据脚本失败：' + error.message)\n        } finally {\n          dataGenerationInProgress.value = false\n        }\n      }\n\n      // 生成单个表的建表语句和数据\n      const generateTableWithDataScript = (table) => {\n        const fields = table.fields || []\n        const dataCount = parseInt(table.by1 || '0')\n\n        let script = `create table if NOT EXISTS ${table.tname} \\n(\\n`\n\n        // 生成字段定义\n        const fieldScripts = fields.map((field, index) => {\n          let fieldScript = `${field.englishName}   ${convertToMySqlType(field.type)}`\n\n          // 主键处理 - 修复第一个字段的AUTO_INCREMENT问题\n          if (field.englishName.toLowerCase() === 'id' || index === 0) {\n            // 只有int类型的字段才能使用AUTO_INCREMENT\n            if (field.type && field.type.toLowerCase() === 'int') {\n              fieldScript += ' auto_increment  primary key'\n            } else {\n              fieldScript += ' not null    primary key'\n            }\n          } else if (field.englishName.toLowerCase().includes('account') ||\n                     field.englishName.toLowerCase().includes('username')) {\n            fieldScript += ' not null    primary key'\n          } else if (field.required) {\n            fieldScript += ' not null   '\n          } else {\n            fieldScript += '  null   '\n          }\n\n          // 注释\n          if (field.chineseName) {\n            fieldScript += ` comment '${field.chineseName}'`\n          }\n\n          return fieldScript\n        })\n\n        script += fieldScripts.join(' ,\\n') + ' \\n'\n        script += `) ENGINE=MyISAM AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;\\n\\n`\n\n        // 添加生成数据条数说明\n        if (dataCount > 0) {\n          script += `生成${dataCount}条insert into数据\\n\\n`\n        }\n\n        return script\n      }\n\n      // 复制数据脚本到剪切板\n      const copyDataScript = async () => {\n        if (!dataContent.value || dataContent.value.trim() === '') {\n          ElMessage.warning('请先生成数据脚本')\n          return\n        }\n\n        try {\n          await navigator.clipboard.writeText(dataContent.value)\n          ElMessage.success('数据脚本已复制到剪切板！')\n        } catch (error) {\n          console.error('复制到剪切板失败:', error)\n          // 降级方案：使用传统的复制方法\n          try {\n            const textArea = document.createElement('textarea')\n            textArea.value = dataContent.value\n            document.body.appendChild(textArea)\n            textArea.select()\n            document.execCommand('copy')\n            document.body.removeChild(textArea)\n            ElMessage.success('数据脚本已复制到剪切板！')\n          } catch (fallbackError) {\n            console.error('降级复制方法也失败:', fallbackError)\n            ElMessage.error('复制到剪切板失败，请手动复制')\n          }\n        }\n      }\n\n      // 下载项目文件\n      const downloadProject = (filePath) => {\n        if (filePath) {\n          // 从filePath中提取文件名\n          const fileName = filePath.split('/').pop()\n          // 使用新的下载接口\n          const downloadUrl = `${base}/projects/download?fileName=${encodeURIComponent(fileName)}`\n          window.open(downloadUrl, '_blank')\n        } else {\n          ElMessage.error('下载链接无效')\n        }\n      }\n\n      // 获取模板图片URL\n      const getTemplateImageUrl = (memo4) => {\n        if (!memo4) return ''\n\n        // 从HTML中提取图片URL\n        const imgMatch = memo4.match(/<img[^>]+src=\"([^\"]+)\"/)\n        if (imgMatch && imgMatch[1]) {\n          return imgMatch[1]\n        }\n\n        return ''\n      }\n\n      // 处理图片加载错误\n      const handleImageError = (event) => {\n        event.target.style.display = 'none'\n        const parent = event.target.parentElement\n        if (parent) {\n          parent.innerHTML = '<div class=\"no-image\"><span>图片加载失败</span></div>'\n        }\n      }\n\n      // 获取表格字段数量\n      const getTableFieldCount = (table) => {\n        // 如果有字段数据，返回字段数量\n        if (table.fields && Array.isArray(table.fields)) {\n          return table.fields.length\n        }\n        // 否则返回0\n        return 0\n      }\n\n      // 获取表格功能列表\n      const getTableFunctions = (table) => {\n        if (!table.tgn) return []\n\n        try {\n          const functions = JSON.parse(table.tgn)\n          const activeFunctions = []\n\n          // 检查各种功能是否启用\n          if (functions.backendAdd) activeFunctions.push('后台添加')\n          if (functions.backendEdit) activeFunctions.push('后台修改')\n          if (functions.backendDelete) activeFunctions.push('后台删除')\n          if (functions.backendDetail) activeFunctions.push('后台详情')\n          if (functions.backendList) activeFunctions.push('后台列表')\n          if (functions.frontendAdd) activeFunctions.push('前台添加')\n          if (functions.frontendEdit) activeFunctions.push('前台修改')\n          if (functions.frontendDelete) activeFunctions.push('前台删除')\n          if (functions.miniAdd) activeFunctions.push('小程序添加')\n\n          return activeFunctions.slice(0, 3) // 只显示前3个功能\n        } catch (error) {\n          console.error('解析表功能配置失败:', error)\n          return []\n        }\n      }\n\n      // 打开表设计弹窗\n      const openTableDesignModal = (table = null) => {\n        if (table) {\n          // 编辑现有表\n          currentTableDesign.value = {\n            id: table.tid,\n            chineseName: table.tword || '',\n            englishName: table.tname || '',\n            menuOrder: parseInt(table.by2 || '1'),\n            generateData: '0',\n            generateDataCount: parseInt(table.by1 || '0'),\n            functions: table.tgn ? JSON.parse(table.tgn) : {\n              backendAdd: true, backendEdit: true, backendDelete: true,\n              backendDetail: true, backendList: false, batchImport: false,\n              batchExport: false, backendLogin: false, backendRegister: false,\n              backendProfile: false, backendPassword: false,\n              frontendAdd: false, frontendEdit: false, frontendDelete: false,\n              frontendDetail: false, frontendList: false, frontendLogin: false,\n              miniAdd: false, miniEdit: false, miniDelete: false,\n              miniDetail: false, miniList: false\n            },\n            fields: table.fields || [\n              {\n                id: 1,\n                chineseName: '主键ID',\n                englishName: 'id',\n                type: 'int',\n                controlType: '文本框',\n                required: true,\n                searchable: false,\n                visible: true,\n                existsCheck: false\n              }\n            ]\n          }\n        } else {\n          // 创建新表\n          resetTableDesign()\n        }\n        showTableDesignModal.value = true\n      }\n\n      // 重置表设计数据\n      const resetTableDesign = () => {\n        currentTableDesign.value = {\n          id: null,\n          chineseName: '',\n          englishName: '',\n          menuOrder: 1,\n          generateData: '0',\n          generateDataCount: 0,\n          functions: {\n            backendAdd: true, backendEdit: true, backendDelete: true,\n            backendDetail: true, backendList: false, batchImport: false,\n            batchExport: false, backendLogin: false, backendRegister: false,\n            backendProfile: false, backendPassword: false,\n            frontendAdd: false, frontendEdit: false, frontendDelete: false,\n            frontendDetail: false, frontendList: false, frontendLogin: false,\n            miniAdd: false, miniEdit: false, miniDelete: false,\n            miniDetail: false, miniList: false\n          },\n          fields: [\n            {\n              id: 1,\n              chineseName: '主键ID',\n              englishName: 'id',\n              type: 'int',\n              controlType: '文本框',\n              required: true,\n              searchable: false,\n              visible: true,\n              existsCheck: false\n            }\n          ]\n        }\n      }\n\n      // 关闭表设计弹窗\n      const closeTableDesignModal = () => {\n        showTableDesignModal.value = false\n        currentTableTab.value = 'table-settings'\n      }\n\n      // 表设计弹窗Tab切换功能\n      const switchTableTab = (tabId) => {\n        currentTableTab.value = tabId\n      }\n\n      // 添加字段到设计中\n      const addNewFieldToDesign = () => {\n        const newField = {\n          id: Date.now(),\n          chineseName: '',\n          englishName: '',\n          type: 'varchar(100)',\n          controlType: '文本框',\n          required: true, // 默认必填项选中\n          searchable: false,\n          visible: true,\n          existsCheck: false,\n          relatedTable: '', // 关联表\n          customOptions: '' // 自定义选项\n        }\n        currentTableDesign.value.fields.push(newField)\n      }\n\n      // 清空所有字段\n      const clearAllFields = async () => {\n        try {\n          await ElMessageBox.confirm(\n            '确定要清空所有字段吗？此操作不可撤销。',\n            '清空字段',\n            {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              type: 'warning',\n            }\n          )\n\n          currentTableDesign.value.fields = []\n          ElMessage.success('字段清空成功')\n        } catch (error) {\n          if (error !== 'cancel') {\n            console.error('清空字段失败:', error)\n          }\n        }\n      }\n\n      // 删除设计中的字段\n      const deleteFieldFromDesign = async (index) => {\n        try {\n          await ElMessageBox.confirm(\n            '确定要删除这个字段吗？此操作不可撤销。',\n            '删除字段',\n            {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              type: 'warning',\n            }\n          )\n\n          currentTableDesign.value.fields.splice(index, 1)\n          ElMessage.success('字段删除成功')\n        } catch (error) {\n          if (error !== 'cancel') {\n            console.error('删除字段失败:', error)\n          }\n        }\n      }\n\n      // 上移字段\n      const moveFieldUp = (index) => {\n        if (index > 0) {\n          const field = currentTableDesign.value.fields.splice(index, 1)[0]\n          currentTableDesign.value.fields.splice(index - 1, 0, field)\n        }\n      }\n\n      // 下移字段\n      const moveFieldDown = (index) => {\n        if (index < currentTableDesign.value.fields.length - 1) {\n          const field = currentTableDesign.value.fields.splice(index, 1)[0]\n          currentTableDesign.value.fields.splice(index + 1, 0, field)\n        }\n      }\n\n      // 编辑字段设置\n      const editFieldSettings = (field, index) => {\n        currentFieldSettings.value = { ...field }\n        currentFieldIndex.value = index\n        showInSearchList.value = field.searchable || false\n        showFieldSettingsDialog.value = true\n      }\n\n      // 关闭字段设置弹窗\n      const closeFieldSettingsDialog = () => {\n        showFieldSettingsDialog.value = false\n        currentFieldSettings.value = null\n        currentFieldIndex.value = -1\n      }\n\n      // 保存字段设置\n      const saveFieldSettings = () => {\n        if (currentFieldIndex.value >= 0 && currentFieldSettings.value) {\n          // 更新字段数据\n          const field = currentTableDesign.value.fields[currentFieldIndex.value]\n          field.relatedTable = currentFieldSettings.value.relatedTable\n          field.customOptions = currentFieldSettings.value.customOptions\n          field.searchable = showInSearchList.value\n\n          ElMessage.success('字段设置保存成功')\n          closeFieldSettingsDialog()\n        }\n      }\n\n      // 显示关联表选择器\n      const showRelatedTableSelector = async () => {\n        if (!currentProjectInfo.value || !currentProjectInfo.value.pid) {\n          ElMessage.warning('请先保存项目信息')\n          return\n        }\n\n        try {\n          // 加载项目的所有表\n          await loadAvailableRelatedTables()\n          showRelatedTableDialog.value = true\n        } catch (error) {\n          console.error('加载关联表失败:', error)\n          ElMessage.error('加载关联表失败')\n        }\n      }\n\n      // 加载可用的关联表\n      const loadAvailableRelatedTables = async () => {\n        try {\n          const url = base + \"/tables/list?currentPage=1&pageSize=100\"\n          const res = await request.post(url, { pid: currentProjectInfo.value.pid })\n\n          if (res.code === 200 && res.resdata) {\n            availableRelatedTables.value = res.resdata.map(table => {\n              // 获取表的第二个字段作为显示名称，如果第二个字段是密码则使用第三个字段\n              let displayName = table.tword || table.tname\n              if (table.fields && table.fields.length > 1) {\n                const secondField = table.fields[1]\n                if (secondField && secondField.chineseName && !secondField.chineseName.includes('密码')) {\n                  displayName = secondField.chineseName\n                } else if (table.fields.length > 2) {\n                  const thirdField = table.fields[2]\n                  if (thirdField && thirdField.chineseName) {\n                    displayName = thirdField.chineseName\n                  }\n                }\n              }\n\n              return {\n                primaryKey: table.fields && table.fields.length > 0 ? table.fields[0].englishName : 'id',\n                displayName: displayName,\n                tableName: table.tname,\n                linkValue: '1',\n                selected: false\n              }\n            })\n          }\n        } catch (error) {\n          console.error('加载关联表失败:', error)\n          throw error\n        }\n      }\n\n      // 关闭关联表选择弹窗\n      const closeRelatedTableDialog = () => {\n        showRelatedTableDialog.value = false\n        availableRelatedTables.value = []\n      }\n\n      // 确认关联表选择\n      const confirmRelatedTableSelection = () => {\n        const selectedTables = availableRelatedTables.value.filter(table => table.selected)\n        if (selectedTables.length === 0) {\n          ElMessage.warning('请至少选择一个关联表')\n          return\n        }\n\n        // 构建关联表字符串，格式：doro,dbid,dormitory,1\n        const relatedTableStr = selectedTables.map(table =>\n          `${table.primaryKey},${table.displayName},${table.tableName},${table.linkValue}`\n        ).join(';')\n\n        if (currentFieldSettings.value) {\n          currentFieldSettings.value.relatedTable = relatedTableStr\n        }\n\n        closeRelatedTableDialog()\n        ElMessage.success('关联表设置成功')\n      }\n\n      // 保存表设计\n      const saveTableDesign = async () => {\n        // 验证必填字段\n        if (!currentTableDesign.value.chineseName.trim()) {\n          ElMessage.warning('请输入表的中文名称')\n          return\n        }\n\n        if (!currentTableDesign.value.englishName.trim()) {\n          ElMessage.warning('请输入表的英文名称')\n          return\n        }\n\n        // 验证字段\n        for (let field of currentTableDesign.value.fields) {\n          if (!field.chineseName.trim() || !field.englishName.trim()) {\n            ElMessage.warning('请完善所有字段的中文名称和英文名称')\n            return\n          }\n        }\n\n        // 确保有项目ID\n        if (!currentProjectInfo.value || !currentProjectInfo.value.pid) {\n          ElMessage.error('项目信息缺失，请重新配置项目')\n          return\n        }\n\n        try {\n          // 准备表数据\n          const tableData = {\n            pid: currentProjectInfo.value.pid,\n            tword: currentTableDesign.value.chineseName,\n            tname: currentTableDesign.value.englishName,\n            tgn: JSON.stringify(currentTableDesign.value.functions),\n            tlist: '',\n            vlist: '',\n            by1: (currentTableDesign.value.generateDataCount || 0).toString(),\n            by2: (currentTableDesign.value.menuOrder || 1).toString()\n          }\n\n          // 如果是编辑模式，添加tid\n          if (currentTableDesign.value.id) {\n            tableData.tid = currentTableDesign.value.id\n          }\n\n          // 保存表信息\n          const isUpdate = currentTableDesign.value.id\n          const url = base + (isUpdate ? \"/tables/update\" : \"/tables/add\")\n          const res = await request.post(url, tableData)\n\n          if (res.code === 200) {\n            let tableId = currentTableDesign.value.id\n            if (!isUpdate && res.resdata) {\n              // 后端返回的是新创建的表ID\n              tableId = res.resdata\n              currentTableDesign.value.id = tableId\n            }\n\n            // 如果有字段，保存字段信息\n            if (currentTableDesign.value.fields.length > 0) {\n              // 先删除原有字段（如果是更新模式）\n              if (isUpdate) {\n                try {\n                  await request.post(base + \"/mores/deleteByTid\", { tid: tableId })\n                } catch (error) {\n                  console.warn('删除原有字段失败:', error)\n                }\n              }\n\n              // 保存新字段\n              for (let field of currentTableDesign.value.fields) {\n                const fieldData = {\n                  tid: tableId,\n                  moname: field.englishName,\n                  mozname: field.chineseName,\n                  motype: field.type,\n                  moflag: field.controlType,\n                  molong: '',\n                  moyz: field.required ? '1' : '0',\n                  mobt: field.searchable ? '1' : '0',\n                  by1: field.visible ? '1' : '0',\n                  by2: field.existsCheck ? '1' : '0',\n                  by3: field.relatedTable || '',\n                  by4: field.customOptions || '',\n                  by5: '',\n                  by6: ''\n                }\n\n                try {\n                  await request.post(base + \"/mores/add\", fieldData)\n                } catch (error) {\n                  console.error('保存字段失败:', field, error)\n                }\n              }\n            }\n\n            ElMessage.success('表设计保存成功')\n            closeTableDesignModal()\n            // 重新加载项目表单列表\n            if (currentProjectInfo.value.pid) {\n              loadProjectTables(currentProjectInfo.value.pid)\n            }\n          } else {\n            ElMessage.error(res.msg || '表保存失败')\n          }\n        } catch (error) {\n          console.error('保存表设计失败:', error)\n          ElMessage.error('保存表设计失败，请检查网络连接')\n        }\n      }\n\n      // 删除表\n      const deleteTable = async (table) => {\n        try {\n          await ElMessageBox.confirm(\n            `确定要删除表 \"${table.tword || table.tname}\" 吗？`,\n            '确认删除',\n            {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              type: 'warning',\n            }\n          )\n\n          const url = base + \"/tables/del?id=\" + table.tid\n          const res = await request.post(url, {})\n\n          if (res.code === 200) {\n            ElMessage.success('删除成功')\n            // 重新加载表单列表\n            if (currentProjectInfo.value && currentProjectInfo.value.pid) {\n              loadProjectTables(currentProjectInfo.value.pid)\n            }\n          } else {\n            ElMessage.error(res.msg || '删除失败')\n          }\n        } catch (error) {\n          if (error !== 'cancel') {\n            console.error('删除表失败:', error)\n            ElMessage.error('删除失败，请检查网络连接')\n          }\n        }\n      }\n\n      // 下一步操作\n      const nextStep = async () => {\n        if (activeTab.value === 'project') {\n          // 从项目配置到表单设计\n          if (!selectedProject.value) {\n            ElMessage.warning('请先选择项目类型')\n            return\n          }\n          if (!projectForm.name) {\n            ElMessage.warning('请输入项目中文名称')\n            return\n          }\n\n          // 验证必填字段\n          if (projectForm.databaseMode === 'new') {\n            if (!projectForm.projectCode) {\n              ElMessage.warning('请输入项目编号')\n              return\n            }\n            if (!projectForm.databaseName) {\n              ElMessage.warning('请输入数据库名称')\n              return\n            }\n          } else if (projectForm.databaseMode === 'existing') {\n            if (!projectForm.selectedDatabase) {\n              ElMessage.warning('请选择已有数据库')\n              return\n            }\n          }\n\n          // 设置当前项目信息\n          currentProjectInfo.value = {\n            name: projectForm.name,\n            projectCode: projectForm.projectCode,\n            databaseName: projectForm.databaseName,\n            pid: projectForm.selectedDatabase || null\n          }\n\n          // 保存或更新项目到数据库\n          const result = await saveOrUpdateProject()\n          if (!result.success) return\n\n          // 更新项目ID\n          if (result.projectId) {\n            currentProjectInfo.value.pid = result.projectId\n          }\n\n          // 如果是已有数据库模式，加载项目表单\n          if (projectForm.databaseMode === 'existing' && projectForm.selectedDatabase) {\n            loadProjectTables(projectForm.selectedDatabase)\n          } else if (currentProjectInfo.value.pid) {\n            // 新建项目也加载表单（可能为空）\n            loadProjectTables(currentProjectInfo.value.pid)\n          }\n\n          activeTab.value = 'form'\n          ElMessage.success('项目配置保存成功，请继续设计表单')\n        } else if (activeTab.value === 'form') {\n          // 从表单设计到项目生成\n          if (!projectTables.value || projectTables.value.length === 0) {\n            ElMessage.warning('请先设计数据表，不能为空')\n            return\n          }\n          activeTab.value = 'generate'\n          ElMessage.success('表单设计完成，请生成项目')\n        }\n      }\n\n      const handleTabClick = (tab) => {\n        console.log('切换到标签页:', tab.props.name)\n      }\n\n      // 组件挂载时检查登录状态\n      onMounted(() => {\n        checkLoginStatus()\n      })\n\n      return {\n        activeTab,\n        logLevel,\n        isLoggedIn,\n        loginDialogVisible,\n        loginLoading,\n        loginFormRef,\n        userInfo,\n        loginForm,\n        loginRules,\n        selectedProject,\n        projectsLoading,\n        availableDatabases,\n        projectForm,\n        currentProjectInfo,\n        projectTables,\n        projectTablesLoading,\n        showTableDesignModal,\n        currentTableTab,\n        currentTableDesign,\n        formFields,\n        tableData,\n        sqlContent,\n        logs,\n        handleTabClick,\n        handleLogin,\n        handleUserCommand,\n        handleLogout,\n        selectProject,\n        getProjectName,\n        openTemplateModal,\n        openFrontTemplateModal,\n        handleDatabaseModeChange,\n        handleProjectSelect,\n        loadProjectTables,\n        openTableDesignModal,\n        closeTableDesignModal,\n        switchTableTab,\n        addNewFieldToDesign,\n        clearAllFields,\n        deleteFieldFromDesign,\n        moveFieldUp,\n        moveFieldDown,\n        saveTableDesign,\n        deleteTable,\n        nextStep,\n        getTableFieldCount,\n        getTableFunctions,\n        backendFunctions,\n        frontendFunctions,\n        miniFunctions,\n        resetFields,\n        showAiGeneratorModal,\n\n        // AI生成器相关\n        showAiGeneratorDialog,\n        aiGeneratorInput,\n        aiGenerationInProgress,\n        hideAiGeneratorModal,\n        confirmAiGeneration,\n        doubaoGenerate,\n        createFormFromAI,\n        inferControlType,\n\n        // 模板选择相关\n        showBackendTemplateDialog,\n        showFrontendTemplateDialog,\n        showTemplateDetailDialog,\n        backendTemplates,\n        frontendTemplates,\n        templatesLoading,\n        currentTemplateDetail,\n        showBackendTemplateSelector,\n        showFrontendTemplateSelector,\n        selectBackendTemplate,\n        selectFrontendTemplate,\n        viewTemplateDetail,\n        getTemplateImageUrl,\n        handleImageError,\n\n        // 项目生成相关\n        generationInProgress,\n        generationResult,\n        canGenerate,\n        generateProject,\n        downloadProject,\n\n        // SQL脚本生成相关\n        sqlGenerationInProgress,\n        generateSqlScript,\n        exportSqlScript,\n        copySqlScript,\n\n        // 数据脚本生成相关\n        dataGenerationInProgress,\n        dataContent,\n        generateDataScript,\n        copyDataScript,\n\n        // 字段设置相关\n        showFieldSettingsDialog,\n        currentFieldSettings,\n        showInSearchList,\n        editFieldSettings,\n        closeFieldSettingsDialog,\n        saveFieldSettings,\n        showRelatedTableSelector,\n\n        // 关联表选择相关\n        showRelatedTableDialog,\n        availableRelatedTables,\n        closeRelatedTableDialog,\n        confirmRelatedTableSelection\n      }\n    }\n  }\n</script>"]}]}