<!doctype html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>Auto Height Examples</title>
		<style>
			form {
				margin: 0;
			}
			textarea {
				display: block;
			}
		</style>
		<link th:href="@{/../themes/default/default.css}" rel="stylesheet">
		<script th:src="@{/../kindeditor-min.js}"></script>
		<script th:src="@{/../lang/zh_CN.js}"></script>
		<script>
			KindEditor.ready(function(K) {
				K.create('textarea[name="content"]', {
					autoHeightMode : true,
					afterCreate : function() {
						this.loadPlugin('autoheight');
					}
				});
			});
		</script>
	</head>
	<body>
		<h3>自动调整高度</h3>
		<form>
			<textarea name="content" style="width:800px;height:200px;"></textarea>
		</form>
	</body>
</html>
