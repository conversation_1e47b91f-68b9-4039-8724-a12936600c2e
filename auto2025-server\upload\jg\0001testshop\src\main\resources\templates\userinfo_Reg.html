<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
<head th:replace="@{/admin/head.html}"></head>
  
</head>

<body>
    <div class="content-area" style="height: 100%; padding: 20px;">
        <h4 class="page-title">用户信息注册</h4>

        <div>
        <div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">用户信息注册</h5>
        </div>
        <div class="card-body">
            <form id="userinfoRegForm" method="post">
                <div class="form-group row mb-3">
                    <label for="account" class="col-sm-3 col-form-label">用户账号 <span class="required">*</span></label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="account" name="account">
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label for="password" class="col-sm-3 col-form-label">登录密码 <span class="required">*</span></label>
                    <div class="col-sm-9">
                        <input type="password" class="form-control" id="password" name="password">
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label for="pwd2" class="col-sm-3 col-form-label">确认密码</label>
                    <div class="col-sm-9">
                        <input type="password" class="form-control" id="pwd2" name="pwd2">
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label for="uname" class="col-sm-3 col-form-label">姓名 <span class="required">*</span></label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="uname" name="uname">
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label for="gender" class="col-sm-3 col-form-label">性别 <span class="required">*</span></label>
                    <div class="col-sm-9">
                        <div class="form-check-inline">
                            <input type="radio" name="gender" value="男" checked="checked" /> 男
                        </div>
                        <div class="form-check-inline">
                            <input type="radio" name="gender" value="女" /> 女
                        </div>
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label for="phone" class="col-sm-3 col-form-label">手机号码 <span class="required">*</span></label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="phone" name="phone" placeholder="请输入手机号码">
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label for="email" class="col-sm-3 col-form-label">电子邮箱</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="email" name="email" placeholder="请输入邮箱地址">
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label for="photo" class="col-sm-3 col-form-label">照片</label>
                    <div class="col-sm-9">
                        <div class="image-upload-container">
                            <input type="file" class="form-control" id="photo_file" accept="image/*" onchange="uploadImage(this, 'photo')">
                            <input type="hidden" id="photo_hidden" name="photo">
                        </div>
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-9 offset-sm-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-floppy-fill"></i> 注册
                        </button>
                        <a href="/qtologin" class="btn btn-secondary ml-2">
                            <i class="bi bi-arrow-left"></i> 返回登录
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
        <script>
        $(document).ready(function() {
            $('#userinfoRegForm').on('submit', function(e) {
                e.preventDefault();
                
                // 使用通用表单提交方法
                CommonUtils.submitForm('#userinfoRegForm', 
                    '/userinfoReg', 
                    function(response) {
                        // 成功回调
                        CommonUtils.showToast('注册成功！', 'success');
                        setTimeout(function() {
                            window.location.href = '/qtologin';
                        }, 1500);
                    },
                    function(error) {
                        // 错误回调
                        if (typeof error === 'string') {
                            if (error.indexOf('用户名已存在') !== -1) {
                                CommonUtils.showToast('该用户名已存在，请重新输入', 'error');
                            } else if (error.indexOf('两次密码输入不一致') !== -1) {
                                CommonUtils.showToast('两次密码输入不一致，请重新输入', 'error');
                            } else {
                                CommonUtils.showToast('注册失败', 'error');
                            }
                        } else {
                            CommonUtils.showToast('网络错误，请重试', 'error');
                        }
                    }
                );
            });
        });
        </script>

        </div>
    </div>

    <!-- Bootstrap JS -->
</body>

</html>