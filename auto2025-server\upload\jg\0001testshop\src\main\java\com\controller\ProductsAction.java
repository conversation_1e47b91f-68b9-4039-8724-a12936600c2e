package com.controller;

import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import com.response.Response;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.model.*;
import com.service.*;
import com.util.*;

/**
 * 产品控制器类
 *
 * 功能说明：
 * 1. 处理产品相关的HTTP请求
 * 2. 提供完整的CRUD操作接口
 * 3. 支持分页查询和条件查询
 * 4. 返回对应的视图页面或JSON数据
 */
@Controller
public class ProductsAction{

	@Autowired private ProductsService productsService;

	// 查询所有产品
	@RequestMapping(value = "/productsList")
	public String productsList(Products ser,HttpServletRequest req)throws Exception
	{

		int offset = 0; // 记录偏移量
		int counts = 0; // 总记录数
		try {
			offset = Integer.parseInt(req.getParameter("pager.offset"));
		} catch (Exception e) {
		}
		int pageSize = 10; // 每页显示条记录数
		PageBean page = new PageBean(offset); // 实例化分页类
		page.setPageSize(pageSize); // 设置每页显示记录数

		counts = productsService.getCount(ser);
		List<Products> productsList = productsService.queryProductsList(ser, page); // 查询所有产品

		//遍历
		for (Products products : productsList) {
			products.setPmemo(removeHTML.Html2Text(products.getPmemo()));
		}

		req.setAttribute("list", productsList);

		/** 分页代码开始 **/
		req.setAttribute("itemSize", counts); // 总记录数
		int page_count = (counts + pageSize - 1) / pageSize; // 计算总页数
		req.setAttribute("pageItem", pageSize); // 每页记录数
		req.setAttribute("pageTotal", page_count); // 总页数
		req.setAttribute("currentPageNumber", (offset / pageSize) + 1); // 当前页码
		req.setAttribute("pageUrl", "productsList"); // 跳转路径
		/** 分页代码 结束 **/
		return "/admin/products/products_Manage";
	}

	// 查询所有产品（列表页面）
	@RequestMapping(value = "/productsList2")
	public String productsList2(Products ser,HttpServletRequest req)throws Exception
	{

		int offset = 0; // 记录偏移量
		int counts = 0; // 总记录数
		try {
			offset = Integer.parseInt(req.getParameter("pager.offset"));
		} catch (Exception e) {
		}
		int pageSize = 10; // 每页显示条记录数
		PageBean page = new PageBean(offset); // 实例化分页类
		page.setPageSize(pageSize); // 设置每页显示记录数

		counts = productsService.getCount(ser);
		List<Products> productsList = productsService.queryProductsList(ser, page); // 查询所有产品

		//遍历
		for (Products products : productsList) {
			products.setPmemo(removeHTML.Html2Text(products.getPmemo()));
		}

		req.setAttribute("list", productsList);

		/** 分页代码开始 **/
		req.setAttribute("itemSize", counts); // 总记录数
		int page_count = (counts + pageSize - 1) / pageSize; // 计算总页数
		req.setAttribute("pageItem", pageSize); // 每页记录数
		req.setAttribute("pageTotal", page_count); // 总页数
		req.setAttribute("currentPageNumber", (offset / pageSize) + 1); // 当前页码
		req.setAttribute("pageUrl", "productsList2"); // 跳转路径
		/** 分页代码 结束 **/
		return "/admin/products/products_Manage2";
	}

	// 跳转到产品添加页面
	@RequestMapping(value = "/productsToAdd")
	public String productsToAdd(HttpServletRequest req) throws Exception {

		return "/admin/products/products_Add";
	}

	// 添加产品
	@RequestMapping(value = "/productsAdd")
	@ResponseBody
	public Response productsAdd(Products products, HttpServletRequest req) throws Exception {
		productsService.insertProducts(products); // 添加产品

		return Response.success();
	}

	// 删除产品
	@RequestMapping(value = "/productsDel")
	public String productsDel(HttpServletRequest req) throws Exception {
		int id = Integer.parseInt(req.getParameter("id"));
		productsService.deleteProducts(id); // 删除产品
		req.setAttribute("message", "操作成功");
		req.setAttribute("path", "productsList");
		return "common/succeed";
	}

	// 跳转到产品修改页面
	@RequestMapping(value = "/productsToEdit")
	public String productsToEdit(HttpServletRequest req) throws Exception {
		int id = Integer.parseInt(req.getParameter("id"));
		Products products = productsService.queryProductsById(id); // 根据ID查询产品详情
		req.setAttribute("item", products);

		return "/admin/products/products_Edit";
	}

	// 跳转到产品详情页面
	@RequestMapping(value = "/productsToDetail")
	public String productsToDetail(HttpServletRequest req) throws Exception {
		int id = Integer.parseInt(req.getParameter("id"));
		Products products = productsService.queryProductsById(id); // 根据ID查询产品详情
		req.setAttribute("item", products); // 设置产品对象到请求属性中
		return "/admin/products/products_Detail";
	}

	// 修改产品
	@RequestMapping(value = "/productsEdit")
	@ResponseBody
	public Response productsEdit(Products products, HttpServletRequest req) throws Exception {
		productsService.updateProducts(products); // 更新产品
		return Response.success();
	}



}
