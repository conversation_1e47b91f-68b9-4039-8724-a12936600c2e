<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
<head th:replace="@{/admin/head.html}"></head>
  
</head>

<body>
    <div class="content-area" style="height: 100%; padding: 20px;">
        <h4 class="page-title">详情产品</h4>

        <div>
        <div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">产品详情</h5>
        </div>
        <div class="card-body">
                <div class="form-group row mb-3">
                    <label class="col-sm-3 col-form-label fw-bold">产品id:</label>
                    <div class="col-sm-9">
                        <p class="form-control-plaintext border-bottom pb-2" th:text="${item.pid}"></p>
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label class="col-sm-3 col-form-label fw-bold">产品名称:</label>
                    <div class="col-sm-9">
                        <p class="form-control-plaintext border-bottom pb-2" th:text="${item.pname}"></p>
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label class="col-sm-3 col-form-label fw-bold">产品分类:</label>
                    <div class="col-sm-9">
                        <p class="form-control-plaintext border-bottom pb-2" th:text="${item.cid}"></p>
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label class="col-sm-3 col-form-label fw-bold">产品图片:</label>
                    <div class="col-sm-9">
                        <div th:if="${item.photo}">
                            <img th:src="@{'/upload/' + ${item.photo}}" alt="产品图片" style="width: 180px; height: 180px; object-fit: cover; border: 1px solid #ddd; border-radius: 4px; cursor: pointer;" onclick="previewImage(this.src)">
                        </div>
                        <span th:unless="${item.photo}">无图片</span>
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label class="col-sm-3 col-form-label fw-bold">价格:</label>
                    <div class="col-sm-9">
                        <p class="form-control-plaintext border-bottom pb-2" th:text="${item.price}"></p>
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label class="col-sm-3 col-form-label fw-bold">数量:</label>
                    <div class="col-sm-9">
                        <p class="form-control-plaintext border-bottom pb-2" th:text="${item.quantity}"></p>
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label class="col-sm-3 col-form-label fw-bold">产品介绍:</label>
                    <div class="col-sm-9">
                        <p class="form-control-plaintext border-bottom pb-2" th:text="${item.pmemo}"></p>
                    </div>
                </div>
            <div class="form-group row">
                <div class="col-sm-9 offset-sm-3">
                    <a href="#" onclick="history.back()" class="btn btn-secondary ml-2">
                        <i class="bi bi-arrow-left"></i> 返回
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
        <script>
        $(document).ready(function() {
            // 页面加载时的初始化操作
            console.log('products 页面加载完成');
        });
        </script>

        </div>
    </div>

    <!-- Bootstrap JS -->
</body>

</html>