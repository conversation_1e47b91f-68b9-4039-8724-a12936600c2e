package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mapper.ProductcategoryMapper;
import com.model.Productcategory;
import com.service.ProductcategoryService;
import com.util.PageBean;

/**
 * 产品分类业务逻辑服务实现类
 *
 * 功能说明：
 * 1. 实现产品分类相关的业务逻辑
 * 2. 提供完整的CRUD操作实现
 * 3. 处理数据查询和分页逻辑
 * 4. 调用Mapper层进行数据库操作
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024
 */
@Service
public class ProductcategoryServiceImpl implements ProductcategoryService {

	/**
	 * 产品分类数据访问层
	 * 通过Spring自动注入
	 */
	@Autowired
	private ProductcategoryMapper productcategoryMapper;

	// 查询多条记录
	public List<Productcategory> queryProductcategoryList(Productcategory productcategory, PageBean page) throws Exception {
		Map<String, Object> map = getQueryMap(productcategory, page);

		List<Productcategory> getProductcategory = productcategoryMapper.query(map);

		return getProductcategory;
	}

	// 得到记录总数
	@Override
	public int getCount(Productcategory productcategory) {
		Map<String, Object> map = getQueryMap(productcategory, null);
		int count = productcategoryMapper.getCount(map);
		return count;
	}

	private Map<String, Object> getQueryMap(Productcategory productcategory, PageBean page) {
		Map<String, Object> map = new HashMap<String, Object>();
		if (productcategory != null) {
						map.put("cid", productcategory.getCid());
			map.put("cname", productcategory.getCname());
			map.put("sort", productcategory.getSort());
			map.put("condition", productcategory.getCondition());

		}
		PageBean.setPageMap(map, page);
		return map;
	}

	// 添加
	public int insertProductcategory(Productcategory productcategory) throws Exception {
		return productcategoryMapper.insertProductcategory(productcategory);
	}

	/**
	 * 根据ID删除产品分类
	 * @param id 主键ID
	 * @return 影响的行数
	 * @throws Exception 异常
	 */
	public int deleteProductcategory(Integer id) throws Exception {
		return productcategoryMapper.deleteProductcategory(id);
	}

	/**
	 * 更新产品分类
	 * @param productcategory 产品分类实体对象
	 * @return 影响的行数
	 * @throws Exception 异常
	 */
	public int updateProductcategory(Productcategory productcategory) throws Exception {
		return productcategoryMapper.updateProductcategory(productcategory);
	}

	/**
	 * 根据ID查询产品分类详情
	 * @param id 主键ID
	 * @return 产品分类实体对象，如果不存在则返回null
	 * @throws Exception 异常
	 */
	public Productcategory queryProductcategoryById(Integer id) throws Exception {
		Productcategory po = productcategoryMapper.queryProductcategoryById(id);
		return po;
	}
}
