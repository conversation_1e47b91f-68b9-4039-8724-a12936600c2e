package com.model;

import java.util.Date;
import java.math.BigDecimal;
import java.io.Serializable;

/**
 * 产品实体类
 *
 * 功能说明：
 * 1. 对应数据库表：products
 * 2. 继承ComData基类，包含通用字段和方法
 * 3. 实现Serializable接口，支持序列化
 */
public class Products extends ComData{

	/** 序列化版本号 */
	private static final long serialVersionUID = 1L;

	// ==================== 字段定义 ====================
	/** 产品id */
	private Integer pid;

	/** 产品名称 */
	private String pname;

	/** 产品分类 */
	private Integer cid;

	/** 产品图片 */
	private String photo;

	/** 价格 */
	private Double price;

	/** 数量 */
	private Integer quantity;

	/** 产品介绍 */
	private String pmemo;



	// ==================== 构造方法 ====================

	/**
	 * 默认构造方法
	 */
	public Products()
	{
		super();
	}

	// ==================== Getter和Setter方法 ====================
		public Integer getPid() {
		return pid;
	}

	public void setPid(Integer pid) {
		this.pid = pid;
	}

	public String getPname() {
		return pname;
	}

	public void setPname(String pname) {
		this.pname = pname;
	}

	public Integer getCid() {
		return cid;
	}

	public void setCid(Integer cid) {
		this.cid = cid;
	}

	public String getPhoto() {
		return photo;
	}

	public void setPhoto(String photo) {
		this.photo = photo;
	}

	public Double getPrice() {
		return price;
	}

	public void setPrice(Double price) {
		this.price = price;
	}

	public Integer getQuantity() {
		return quantity;
	}

	public void setQuantity(Integer quantity) {
		this.quantity = quantity;
	}

	public String getPmemo() {
		return pmemo;
	}

	public void setPmemo(String pmemo) {
		this.pmemo = pmemo;
	}



}
