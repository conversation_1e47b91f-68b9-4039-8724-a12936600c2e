<template>
  <div class="code-generator-container">
    <!-- 用户信息浮动显示 -->
    <div v-if="isLoggedIn" class="user-info-float">
      <el-dropdown @command="handleUserCommand">
        <span class="user-info-trigger">
          <el-icon>
            <User />
          </el-icon>
          {{ userInfo.username }}
          <el-icon class="el-icon--right">
            <ArrowDown />
          </el-icon>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="logout">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-tabs v-model="activeTab" type="card" class="generator-tabs" @tab-click="handleTabClick">
        <!-- 设置项目 -->
        <el-tab-pane label="设置项目" name="project">
          <template #label>
            <span class="tab-label">
              <el-icon>
                <Folder />
              </el-icon>
              设置项目
            </span>
          </template>
          <div class="tab-content">
            <!-- 页面标题 -->
            <div class="page-header">
              <div class="header-content">
                <h1 class="page-title">
                  <el-icon class="title-icon">
                    <Setting />
                  </el-icon>
                  代码生成器
                </h1>
                <p class="page-description">快速生成高质量的代码，提升开发效率</p>
              </div>
            </div>

            <!-- 项目类型选择 -->
            <div class="content-card">
              <div class="card-header">
                <h3>支持的项目类型</h3>
                <p>选择您需要的技术栈，快速生成项目模板</p>
              </div>

              <!-- 功能卡片网格 -->
              <div class="features-grid">
                <div class="feature-card" @click="selectProject('springboot-thymeleaf')"
                  :class="{ active: selectedProject === 'springboot-thymeleaf' }">
                  <h3>🍃 SpringBoot + Thymeleaf</h3>
                  <p>传统的服务端渲染架构，适合企业级应用开发，集成Thymeleaf模板引擎</p>
                </div>

                <div class="feature-card" @click="selectProject('springboot-miniprogram')"
                  :class="{ active: selectedProject === 'springboot-miniprogram' }">
                  <h3>📱 SpringBoot + 小程序</h3>
                  <p>微信小程序后端API开发，提供完整的用户认证和数据管理功能</p>
                </div>

                <div class="feature-card" @click="selectProject('springboot-vue')"
                  :class="{ active: selectedProject === 'springboot-vue' }">
                  <h3>⚡ SpringBoot + Vue</h3>
                  <p>现代化前后端分离架构，Vue.js前端 + SpringBoot后端API</p>
                </div>

                <div class="feature-card" @click="selectProject('ssm-vue')"
                  :class="{ active: selectedProject === 'ssm-vue' }">
                  <h3>🔧 SSM + Vue</h3>
                  <p>经典的SSM框架（Spring + SpringMVC + MyBatis）配合Vue.js前端</p>
                </div>
              </div>
            </div>

            <!-- 项目配置区域 -->
            <div v-if="selectedProject" class="content-card">
              <div class="card-header">
                <h3>项目配置 - {{ getProjectName(selectedProject) }}</h3>
                <p>配置项目基本信息和生成参数</p>
              </div>
              <div class="form-section">
                <el-form :model="projectForm" label-width="120px" class="project-form">
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="数据库类型">
                        <el-select v-model="projectForm.databaseType" placeholder="请选择数据库类型" style="width: 100%">
                          <el-option label="MySQL" value="mysql" />
                          <el-option label="SQL Server" value="sqlserver" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="数据库模式">
                        <el-select v-model="projectForm.databaseMode" placeholder="请选择数据库模式" style="width: 100%"
                          @change="handleDatabaseModeChange">
                          <el-option label="新建数据库" value="new" />
                          <el-option label="已有数据库" value="existing" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="项目中文名称">
                        <el-input v-model="projectForm.name" placeholder="请输入项目中文名称" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20" v-if="projectForm.databaseMode === 'new'">
                    <el-col :span="8">
                      <el-form-item label="项目编号">
                        <el-input v-model="projectForm.projectCode" placeholder="项目编号" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="数据库名称">
                        <el-input v-model="projectForm.databaseName" placeholder="请输入数据库名称" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="学校名称">
                        <el-input v-model="projectForm.schoolName" placeholder="请输入学校名称" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20" v-if="projectForm.databaseMode === 'existing'">
                    <el-col :span="24">
                      <el-form-item label="选择数据库">
                        <el-select v-model="projectForm.selectedDatabase" placeholder="请选择数据库" style="width: 100%"
                          @change="handleProjectSelect" :loading="projectsLoading">
                          <el-option v-for="db in availableDatabases" :key="db.value" :label="db.text" :value="db.value"
                            :disabled="!db.value" />
                        </el-select>
                        <div class="form-text" v-if="availableDatabases.length > 0">
                          格式：项目编号--数据库名称 (项目中文名称)
                        </div>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="后台模板">
                        <el-input v-model="projectForm.backendTemplate" placeholder="点击选择后台模板" readonly
                          @click="openTemplateModal" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="前台模板">
                        <el-input v-model="projectForm.frontendTemplate" placeholder="点击选择前台模板" readonly
                          @click="openFrontTemplateModal" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="复制项目">
                        <el-input v-model="projectForm.copyProject" placeholder="请输入要复制的项目" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="layer弹出层">
                        <el-select v-model="projectForm.layer" style="width: 100%">
                          <el-option label="否" value="否" />
                          <el-option label="是" value="是" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="统计图表">
                        <el-select v-model="projectForm.charts" style="width: 100%">
                          <el-option label="否" value="否" />
                          <el-option label="是" value="是" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <!-- 空列，保持布局平衡 -->
                    </el-col>
                  </el-row>

                  <el-form-item label="Session信息">
                    <el-row :gutter="10">
                      <el-col :span="6">
                        <el-input v-model="projectForm.adminId" placeholder="管理员ID" />
                      </el-col>
                      <el-col :span="6">
                        <el-input v-model="projectForm.adminName" placeholder="管理员姓名" />
                      </el-col>
                      <el-col :span="6">
                        <el-input v-model="projectForm.adminRole" placeholder="管理员角色" />
                      </el-col>
                      <el-col :span="6">
                        <el-input v-model="projectForm.adminLoginName" placeholder="登录名" />
                      </el-col>
                    </el-row>
                  </el-form-item>
                </el-form>

                <div class="form-actions">
                  <el-button type="primary" @click="nextStep" size="large">
                    🎯 下一步：设计表单
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 设计表单 -->
        <el-tab-pane label="设计表单" name="form">
          <template #label>
            <span class="tab-label">
              <el-icon>
                <Edit />
              </el-icon>
              设计表单
            </span>
          </template>
          <div class="tab-content">
            <div class="content-card">
              <div class="card-header">
                <h3>数据表管理</h3>
                <p>设计您的数据库表结构和表单字段</p>
              </div>

              <!-- 项目信息显示 -->
              <div v-if="currentProjectInfo" class="project-info-section">
                <div class="project-info-card">
                  <h4>当前项目：{{ currentProjectInfo.name }}</h4>
                  <div class="project-details">
                    <span class="project-detail-item">
                      <strong>项目编号：</strong>{{ currentProjectInfo.projectCode }}
                    </span>
                    <span class="project-detail-item">
                      <strong>数据库：</strong>{{ currentProjectInfo.databaseName }}
                    </span>
                    <span class="project-detail-item">
                      <strong>类型：</strong>{{ getProjectName(selectedProject) }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 表单列表 -->
              <div class="table-designer">
                <div class="table-list-header">
                  <h4>数据表列表</h4>
                  <el-button type="primary" :icon="Plus" @click="openTableDesignModal()">
                    添加新表
                  </el-button>
                </div>

                <div v-if="projectTablesLoading" class="loading-section">
                  <el-icon class="is-loading">
                    <Loading />
                  </el-icon>
                  <span>正在加载表单数据...</span>
                </div>

                <div v-else-if="projectTables.length === 0" class="empty-section">
                  <el-empty description="暂无数据表">
                    <el-button type="primary" @click="openTableDesignModal()">创建第一个表</el-button>
                  </el-empty>
                </div>

                <div v-else class="table-list">
                  <div class="table-item" v-for="table in projectTables" :key="table.tid"
                    @click="openTableDesignModal(table)">
                    <div class="table-item-header">
                      <h5>{{ table.tword || table.tname }}</h5>
                      <div class="table-item-actions">
                        <el-button size="small" :icon="Edit" @click.stop="openTableDesignModal(table)">编辑</el-button>
                        <el-button size="small" type="danger" :icon="Delete"
                          @click.stop="deleteTable(table)">删除</el-button>
                      </div>
                    </div>
                    <p class="table-item-description">
                      {{ table.tname }} ({{ getTableFieldCount(table) }}个字段)
                    </p>
                    <div class="table-item-functions" v-if="table.tgn">
                      <span class="function-tag" v-for="func in getTableFunctions(table)" :key="func">{{ func }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="form-actions" style="margin-top: 30px;">
                <el-button @click="activeTab = 'project'" size="large">
                  ← 上一步：项目配置
                </el-button>
                <el-button type="primary" @click="nextStep" size="large">
                  下一步：生成项目 →
                </el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 生成项目 -->
        <el-tab-pane label="生成项目" name="generate">
          <template #label>
            <span class="tab-label">
              <el-icon>
                <Cpu />
              </el-icon>
              生成项目
            </span>
          </template>
          <div class="tab-content">
            <div class="content-card">
              <div class="card-header">
                <h3>生成项目</h3>
                <p>确认配置信息并生成您的项目</p>
              </div>

              <!-- 项目配置摘要 -->
              <div class="generation-summary">
                <h4>项目配置摘要</h4>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <div class="summary-item">
                      <span class="summary-label">项目名称:</span>
                      <span class="summary-value">{{ projectForm.name || '未设置' }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="summary-item">
                      <span class="summary-label">数据库名称:</span>
                      <span class="summary-value">{{ projectForm.databaseName || '未设置' }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="summary-item">
                      <span class="summary-label">项目编号:</span>
                      <span class="summary-value">{{ projectForm.projectCode || '未设置' }}</span>
                    </div>
                  </el-col>
                </el-row>
                <el-row :gutter="20" style="margin-top: 15px;">
                  <el-col :span="8">
                    <div class="summary-item">
                      <span class="summary-label">数据表数量:</span>
                      <span class="summary-value">{{ projectTables.length }} 个</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="summary-item">
                      <span class="summary-label">后台模板:</span>
                      <span class="summary-value">{{ projectForm.backendTemplate || '未选择' }}</span>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="summary-item">
                      <span class="summary-label">前台模板:</span>
                      <span class="summary-value">{{ projectForm.frontendTemplate || '未选择' }}</span>
                    </div>
                  </el-col>
                </el-row>
              </div>

              <!-- 生成操作区域 -->
              <div class="generation-actions">
                <el-button
                  type="primary"
                  size="large"
                  @click="generateProject"
                  :loading="generationInProgress"
                  :disabled="!canGenerate || generationInProgress">
                  <el-icon v-if="!generationInProgress">
                    <Cpu />
                  </el-icon>
                  <el-icon v-else>
                    <Loading />
                  </el-icon>
                  {{ generationInProgress ? '正在生成项目，请稍候...' : '🚀 生成项目' }}
                </el-button>

                <!-- 生成进度提示 -->
                <div v-if="generationInProgress" class="generation-progress">
                  <el-progress :percentage="100" :show-text="false" status="success" :indeterminate="true" />
                  <p class="progress-text">正在生成代码文件和项目结构...</p>
                </div>
              </div>

              <!-- 项目生成结果显示区域 -->
              <div v-if="generationResult" class="generation-result">
                <h4>项目生成结果</h4>

                <!-- 生成状态 -->
                <div class="generation-status" :class="generationResult.status">
                  <el-icon class="status-icon">
                    <Check v-if="generationResult.status === 'success'" />
                    <Close v-else />
                  </el-icon>
                  <span class="status-text">{{ generationResult.message }}</span>
                </div>

                <!-- 文件列表 -->
                <div v-if="generationResult.files" class="file-list-section">
                  <h5>生成的文件列表 ({{ generationResult.files.savedCount }}/{{ generationResult.files.totalCount }})</h5>

                  <!-- 成功文件列表 -->
                  <div v-if="generationResult.files.savedFiles && generationResult.files.savedFiles.length > 0" class="success-files">
                    <h6>✅ 成功生成的文件:</h6>
                    <el-scrollbar max-height="200px">
                      <ul class="file-list success">
                        <li v-for="file in generationResult.files.savedFiles" :key="file">
                          <el-icon class="file-icon"><Document /></el-icon>
                          <span class="file-name">{{ file }}</span>
                        </li>
                      </ul>
                    </el-scrollbar>
                  </div>

                  <!-- 失败文件列表 -->
                  <div v-if="generationResult.files.failedFiles && generationResult.files.failedFiles.length > 0" class="failed-files">
                    <h6>❌ 生成失败的文件:</h6>
                    <el-scrollbar max-height="200px">
                      <ul class="file-list error">
                        <li v-for="file in generationResult.files.failedFiles" :key="file.fileName">
                          <el-icon class="file-icon"><Warning /></el-icon>
                          <span class="file-name">{{ file.fileName }}</span>
                          <span class="file-error">{{ file.error }}</span>
                        </li>
                      </ul>
                    </el-scrollbar>
                  </div>
                </div>

                <!-- 压缩结果 -->
                <div v-if="generationResult.compression" class="compression-result">
                  <h5>项目压缩结果</h5>
                  <div class="compression-info" :class="generationResult.compression.status">
                    <el-icon class="status-icon">
                      <Box v-if="generationResult.compression.status === 'success'" />
                      <Close v-else />
                    </el-icon>
                    <span class="compression-text">{{ generationResult.compression.message }}</span>
                    <div v-if="generationResult.compression.data" class="compression-details">
                      <p>文件名: {{ generationResult.compression.data.zipFileName }}</p>
                      <p>文件大小: {{ generationResult.compression.data.fileSize }}</p>
                      <el-button type="success" @click="downloadProject(generationResult.compression.data.zipFilePath)">
                        <el-icon><Download /></el-icon>
                        下载项目文件
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 数据 -->
        <el-tab-pane label="数据" name="data">
          <template #label>
            <span class="tab-label">
              <el-icon>
                <DataBoard />
              </el-icon>
              数据
            </span>
          </template>
          <div class="tab-content">
            <div class="content-card">
              <div class="card-header">
                <h3>数据脚本管理</h3>
                <p>生成和管理数据库建表及插入数据脚本</p>
              </div>
              <div class="data-section">
                <div class="data-toolbar">
                  <el-button type="primary" :icon="Plus" @click="generateDataScript" :loading="dataGenerationInProgress">生成数据脚本</el-button>
                  <el-button :icon="DocumentCopy" @click="copyDataScript">复制脚本</el-button>
                </div>
                <div class="data-editor">
                  <el-input v-model="dataContent" type="textarea" :rows="15" placeholder="数据脚本将在这里显示..."
                    class="data-textarea" />
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- SQL脚本 -->
        <el-tab-pane label="SQL脚本" name="sql">
          <template #label>
            <span class="tab-label">
              <el-icon>
                <DocumentCopy />
              </el-icon>
              SQL脚本
            </span>
          </template>
          <div class="tab-content">
            <div class="content-card">
              <div class="card-header">
                <h3>SQL脚本管理</h3>
                <p>生成和管理数据库脚本</p>
              </div>
              <div class="sql-section">
                <div class="sql-toolbar">
                  <el-button type="primary" :icon="Plus" @click="generateSqlScript" :loading="sqlGenerationInProgress">生成建表脚本</el-button>
                  <el-button :icon="Download" @click="exportSqlScript">导出脚本</el-button>
                  <el-button :icon="DocumentCopy" @click="copySqlScript">复制脚本</el-button>
                </div>
                <div class="sql-editor">
                  <el-input v-model="sqlContent" type="textarea" :rows="25" placeholder="SQL脚本将在这里显示..."
                    class="sql-textarea" />
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 错误日志 -->
        <el-tab-pane label="错误日志" name="logs">
          <template #label>
            <span class="tab-label">
              <el-icon>
                <Warning />
              </el-icon>
              错误日志
            </span>
          </template>
          <div class="tab-content">
            <div class="content-card">
              <div class="card-header">
                <h3>错误日志</h3>
                <p>查看生成过程中的错误和警告信息</p>
              </div>
              <div class="logs-section">
                <div class="logs-toolbar">
                  <el-button :icon="Refresh">刷新日志</el-button>
                  <el-button :icon="Delete">清空日志</el-button>
                  <el-select v-model="logLevel" placeholder="日志级别" style="width: 120px">
                    <el-option label="全部" value="all" />
                    <el-option label="错误" value="error" />
                    <el-option label="警告" value="warning" />
                    <el-option label="信息" value="info" />
                  </el-select>
                </div>
                <div class="logs-content">
                  <div class="log-item" v-for="(log, index) in logs" :key="index" :class="log.level">
                    <div class="log-time">{{ log.time }}</div>
                    <div class="log-level">{{ log.level.toUpperCase() }}</div>
                    <div class="log-message">{{ log.message }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 登录对话框 -->
    <el-dialog v-model="loginDialogVisible" title="用户登录" width="400px" :close-on-click-modal="false"
      :close-on-press-escape="false" :show-close="false">
      <el-form :model="loginForm" :rules="loginRules" ref="loginFormRef" label-width="80px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="loginForm.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="loginForm.password" type="password" placeholder="请输入密码" @keyup.enter="handleLogin" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="handleLogin" :loading="loginLoading">
            登录
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 表设计弹窗 -->
    <el-dialog v-model="showTableDesignModal" :title="currentTableDesign.id ? '编辑数据表' : '创建数据表'" width="85%"
      :close-on-click-modal="false" :before-close="closeTableDesignModal" class="table-design-dialog" top="5vh"
      destroy-on-close>
      <div class="table-design-container">
        <!-- 自定义Tab导航 -->
        <div class="custom-tabs">
          <div class="tab-nav">
            <div class="tab-item" :class="{ active: currentTableTab === 'table-settings' }"
              @click="switchTableTab('table-settings')">
              <el-icon class="tab-icon">
                <Setting />
              </el-icon>
              <span class="tab-text">表设置</span>
            </div>
            <div class="tab-item" :class="{ active: currentTableTab === 'field-design' }"
              @click="switchTableTab('field-design')">
              <el-icon class="tab-icon">
                <DataBoard />
              </el-icon>
              <span class="tab-text">字段设计</span>
            </div>
          </div>

          <!-- Tab内容 -->
          <div class="tab-content-wrapper">
            <!-- 表设置内容 -->
            <div v-show="currentTableTab === 'table-settings'" class="tab-content">
              <!-- 基本信息 -->
              <div class="basic-info-section">
                <el-form :model="currentTableDesign" label-width="100px" class="design-form" size="default">
                  <el-row :gutter="16">
                    <el-col :span="6">
                      <el-form-item label="表中文名称" required>
                        <el-input v-model="currentTableDesign.chineseName" placeholder="请输入表的中文名称" clearable />
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="表英文名称" required>
                        <el-input v-model="currentTableDesign.englishName" placeholder="请输入表的英文名称" clearable />
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="菜单显示顺序">
                        <el-input-number v-model="currentTableDesign.menuOrder" :min="1" :max="999" placeholder="1" style="width: 100%;" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="生成数据条数">
                        <el-input-number v-model="currentTableDesign.generateDataCount" :min="0" :max="1000"
                          placeholder="生成数据条数" style="width: 100%;" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="16">
                    <el-col :span="24">
                      <el-form-item label="AI助手">
                        <el-button type="primary" @click="showAiGeneratorModal('table')" style="width: 100%;">
                          <el-icon>
                            <Cpu />
                          </el-icon>
                          AI生成表结构
                        </el-button>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </div>

              <!-- 功能选择 -->
              <div class="function-selection-section">
                <h4 style="margin: 20px 0 15px 0; color: #2c3e50; font-size: 16px;">功能选择</h4>
                <el-row :gutter="20">
                  <!-- 后台功能 -->
                  <el-col :span="8">
                    <div class="function-group">
                      <h5>后台功能</h5>
                      <div class="function-checkboxes">
                        <el-checkbox v-model="currentTableDesign.functions.backendAdd">后台添加</el-checkbox>
                        <el-checkbox v-model="currentTableDesign.functions.backendEdit">后台修改</el-checkbox>
                        <el-checkbox v-model="currentTableDesign.functions.backendDelete">后台删除</el-checkbox>
                        <el-checkbox v-model="currentTableDesign.functions.backendDetail">后台详情</el-checkbox>
                        <el-checkbox v-model="currentTableDesign.functions.backendList">后台列表</el-checkbox>
                        <el-checkbox v-model="currentTableDesign.functions.batchImport">批量导入</el-checkbox>
                        <el-checkbox v-model="currentTableDesign.functions.batchExport">批量导出</el-checkbox>
                        <el-checkbox v-model="currentTableDesign.functions.backendLogin">后台登录</el-checkbox>
                        <el-checkbox v-model="currentTableDesign.functions.backendRegister">后台注册</el-checkbox>
                        <el-checkbox v-model="currentTableDesign.functions.backendProfile">后台个人信息</el-checkbox>
                        <el-checkbox v-model="currentTableDesign.functions.backendPassword">后台修改密码</el-checkbox>
                      </div>
                    </div>
                  </el-col>

                  <!-- 前台功能 -->
                  <el-col :span="8">
                    <div class="function-group">
                      <h5>前台功能</h5>
                      <div class="function-checkboxes">
                        <el-checkbox v-model="currentTableDesign.functions.frontendAdd">前台添加</el-checkbox>
                        <el-checkbox v-model="currentTableDesign.functions.frontendEdit">前台修改</el-checkbox>
                        <el-checkbox v-model="currentTableDesign.functions.frontendDelete">前台删除</el-checkbox>
                        <el-checkbox v-model="currentTableDesign.functions.frontendDetail">前台详情</el-checkbox>
                        <el-checkbox v-model="currentTableDesign.functions.frontendList">前台列表</el-checkbox>
                        <el-checkbox v-model="currentTableDesign.functions.frontendLogin">前台个人信息和密码</el-checkbox>
                      </div>
                    </div>
                  </el-col>

                  <!-- 小程序功能 -->
                  <el-col :span="8">
                    <div class="function-group">
                      <h5>小程序功能</h5>
                      <div class="function-checkboxes">
                        <el-checkbox v-model="currentTableDesign.functions.miniAdd">小程序添加</el-checkbox>
                        <el-checkbox v-model="currentTableDesign.functions.miniEdit">小程序编辑</el-checkbox>
                        <el-checkbox v-model="currentTableDesign.functions.miniDelete">小程序删除</el-checkbox>
                        <el-checkbox v-model="currentTableDesign.functions.miniDetail">小程序详情</el-checkbox>
                        <el-checkbox v-model="currentTableDesign.functions.miniList">小程序List</el-checkbox>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </div>

            <!-- 字段设计内容 -->
            <div v-show="currentTableTab === 'field-design'" class="tab-content">
              <!-- 工具栏 -->
              <div class="field-toolbar-compact">
                <el-space size="default" wrap>
                  <el-button type="success" @click="addNewFieldToDesign">
                    <el-icon>
                      <Plus />
                    </el-icon>
                    添加字段
                  </el-button>
                  <el-button type="warning" @click="resetFields">
                    <el-icon>
                      <Refresh />
                    </el-icon>
                    重置字段
                  </el-button>
                  <el-button type="danger" @click="clearAllFields">
                    <el-icon>
                      <Delete />
                    </el-icon>
                    清空字段
                  </el-button>
                  <el-divider direction="vertical" />
                  <el-text type="info">
                    当前字段数量: {{ currentTableDesign.fields?.length || 0 }}
                  </el-text>
                </el-space>
              </div>

              <!-- 字段表格 -->
              <div class="field-table-section">
                <el-table :data="currentTableDesign.fields" class="field-table" border stripe
                  empty-text="暂无字段，请点击添加字段按钮" size="small">
                  <el-table-column label="序号" type="index" width="60" align="center" />

                  <el-table-column label="中文名称" min-width="100">
                    <template #default="{ row }">
                      <el-input v-model="row.chineseName" placeholder="请输入中文名称" size="small" clearable />
                    </template>
                  </el-table-column>

                  <el-table-column label="英文名称" min-width="100">
                    <template #default="{ row }">
                      <el-input v-model="row.englishName" placeholder="请输入英文名称" size="small" clearable />
                    </template>
                  </el-table-column>

                  <el-table-column label="字段类型" min-width="80">
                    <template #default="{ row }">
                      <el-select v-model="row.type" placeholder="选择类型" size="small" style="width: 100%">
                        <el-option label="int" value="int" />
                        <el-option label="varchar(50)" value="varchar(50)" />
                        <el-option label="varchar(100)" value="varchar(100)" />
                        <el-option label="varchar(200)" value="varchar(200)" />
                        <el-option label="varchar(500)" value="varchar(500)" />
                        <el-option label="text" value="text" />
                        <el-option label="datetime" value="datetime" />
                        <el-option label="decimal(10,2)" value="decimal(10,2)" />
                      </el-select>
                    </template>
                  </el-table-column>

                  <el-table-column label="控件类型" min-width="100">
                    <template #default="{ row }">
                      <el-select v-model="row.controlType" placeholder="选择控件" size="small" style="width: 100%">
                        <el-option label="文本框" value="文本框" />
                        <el-option label="多行文本" value="多行文本" />
                        <el-option label="下拉框" value="下拉框" />
                        <el-option label="单选按钮" value="单选按钮" />
                        <el-option label="复选框" value="复选框" />
                        <el-option label="日期选择" value="日期选择" />
                        <el-option label="时间选择" value="时间选择" />
                        <el-option label="文件上传" value="文件上传" />
                        <el-option label="图片上传" value="图片上传" />
                        <el-option label="编辑器" value="编辑器" />
                        <el-option label="自动当前时间" value="自动当前时间" />
                      </el-select>
                    </template>
                  </el-table-column>

                  <el-table-column label="必填" width="60" align="center">
                    <template #default="{ row }">
                      <el-checkbox v-model="row.required" />
                    </template>
                  </el-table-column>

                  <el-table-column label="搜索" width="60" align="center">
                    <template #default="{ row }">
                      <el-checkbox v-model="row.searchable" />
                    </template>
                  </el-table-column>

                  <el-table-column label="显示" width="60" align="center">
                    <template #default="{ row }">
                      <el-checkbox v-model="row.visible" />
                    </template>
                  </el-table-column>

                  <el-table-column label="存在" width="60" align="center">
                    <template #default="{ row }">
                      <el-checkbox v-model="row.existsCheck" />
                    </template>
                  </el-table-column>

                  <el-table-column label="操作" width="160" align="center" fixed="right">
                    <template #default="{ row, $index }">
                      <div class="field-actions">
                        <el-button size="small" type="primary" @click="moveFieldUp($index)" :disabled="$index === 0"
                          circle>
                          <el-icon>
                            <ArrowUp />
                          </el-icon>
                        </el-button>
                        <el-button size="small" type="primary" @click="moveFieldDown($index)"
                          :disabled="$index === currentTableDesign.fields.length - 1" circle>
                          <el-icon>
                            <ArrowDown />
                          </el-icon>
                        </el-button>
                        <el-button size="small" type="warning" @click="editFieldSettings(row, $index)" circle>
                          <el-icon>
                            <Edit />
                          </el-icon>
                        </el-button>
                        <el-button size="small" type="danger" @click="deleteFieldFromDesign($index)" circle>
                          <el-icon>
                            <Delete />
                          </el-icon>
                        </el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-space size="large">
            <el-button @click="closeTableDesignModal" size="large">
              <el-icon>
                <Close />
              </el-icon>
              <span>取消</span>
            </el-button>
            <el-button type="primary" @click="saveTableDesign" size="large">
              <el-icon>
                <Check />
              </el-icon>
              <span>保存表设计</span>
            </el-button>
          </el-space>
        </div>
      </template>
    </el-dialog>

    <!-- AI生成器弹窗 -->
    <el-dialog v-model="showAiGeneratorDialog" title="AI生成表结构" width="600px" :close-on-click-modal="false"
      :before-close="hideAiGeneratorModal" class="ai-generator-dialog" destroy-on-close>
      <div class="ai-generator-container">
        <div class="ai-generator-header">
          <h4>🤖 AI智能生成表结构</h4>
          <p>描述您要生成的表结构，AI将自动为您创建完整的数据表设计</p>
        </div>

        <div class="ai-generator-body">
          <el-form label-width="100px">
            <el-form-item label="表描述">
              <el-input v-model="aiGeneratorInput" type="textarea" :rows="4"
                placeholder="请输入表的描述，例如：学生信息管理表、商品管理系统、订单管理表等..." class="ai-generator-input"
                :disabled="aiGenerationInProgress" />
            </el-form-item>
          </el-form>

          <div class="ai-generator-example">
            <h5>💡 示例：</h5>
            <div class="example-list">
              <div class="example-item">
                <strong>表结构描述：</strong>"学生信息管理表" 或 "商品管理系统" 或 "订单管理表"
              </div>
              <div class="example-item">
                <strong>功能说明：</strong>AI会根据您的描述自动生成包含合适字段的完整表结构
              </div>
            </div>
          </div>
        </div>

      </div>

      <template #footer>
        <div class="ai-generator-footer">
          <el-button @click="hideAiGeneratorModal" :disabled="aiGenerationInProgress">取消</el-button>
          <el-button type="primary" @click="confirmAiGeneration" :loading="aiGenerationInProgress">
            <el-icon v-if="!aiGenerationInProgress">
              <Cpu />
            </el-icon>
            {{ aiGenerationInProgress ? '生成中...' : '🚀 生成' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 后台模板选择弹窗 -->
    <el-dialog v-model="showBackendTemplateDialog" title="选择后台模板" width="1000px" :close-on-click-modal="false"
      class="template-dialog" destroy-on-close>
      <div class="template-container">
        <div class="template-grid-4col" v-loading="templatesLoading">
          <div v-for="template in backendTemplates" :key="template.sid" class="template-item-4col">
            <div class="template-image" @click="selectBackendTemplate(template)">
              <img v-if="template.memo4" :src="getTemplateImageUrl(template.memo4)" :alt="template.sname"
                @error="handleImageError" />
              <div v-else class="no-image">
                <el-icon>
                  <Picture />
                </el-icon>
                <span>暂无预览</span>
              </div>
            </div>
            <div class="template-info">
              <h4 @click="selectBackendTemplate(template)">{{ template.sname }}</h4>
              <p class="template-id">模板ID: {{ template.sid }}</p>
              <div class="template-actions">
                <el-button type="primary" size="small" @click="selectBackendTemplate(template)">
                  选择模板
                </el-button>
                <el-button type="info" size="small" @click="viewTemplateDetail(template)">
                  查看详情
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 前台模板选择弹窗 -->
    <el-dialog v-model="showFrontendTemplateDialog" title="选择前台模板" width="1000px" :close-on-click-modal="false"
      class="template-dialog" destroy-on-close>
      <div class="template-container">
        <div class="template-grid-4col" v-loading="templatesLoading">
          <div v-for="template in frontendTemplates" :key="template.sid" class="template-item-4col">
            <div class="template-image" @click="selectFrontendTemplate(template)">
              <img v-if="template.memo4" :src="getTemplateImageUrl(template.memo4)" :alt="template.sname"
                @error="handleImageError" />
              <div v-else class="no-image">
                <el-icon>
                  <Picture />
                </el-icon>
                <span>暂无预览</span>
              </div>
            </div>
            <div class="template-info">
              <h4 @click="selectFrontendTemplate(template)">{{ template.sname }}</h4>
              <p class="template-id">模板ID: {{ template.sid }}</p>
              <div class="template-actions">
                <el-button type="primary" size="small" @click="selectFrontendTemplate(template)">
                  选择模板
                </el-button>
                <el-button type="info" size="small" @click="viewTemplateDetail(template)">
                  查看详情
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 模板详情查看弹窗 -->
    <el-dialog v-model="showTemplateDetailDialog" title="模板详情" width="90%" :close-on-click-modal="false"
      class="template-detail-dialog" destroy-on-close top="5vh">
      <div class="template-detail-container" v-if="currentTemplateDetail">
        <div class="template-detail-header">
          <h3>{{ currentTemplateDetail.sname }}</h3>
          <p class="template-detail-id">模板ID: {{ currentTemplateDetail.sid }}</p>
        </div>
        <div class="template-detail-image-container">
          <div v-if="currentTemplateDetail.memo4" class="template-detail-image">
            <el-image
              :src="getTemplateImageUrl(currentTemplateDetail.memo4)"
              :alt="currentTemplateDetail.sname"
              fit="contain"
              :preview-src-list="[getTemplateImageUrl(currentTemplateDetail.memo4)]"
              :initial-index="0"
              preview-teleported
              class="template-preview-image"
            />
          </div>
          <div v-else class="no-image-large">
            <el-icon>
              <Picture />
            </el-icon>
            <span>暂无预览图片</span>
          </div>
        </div>
        <div class="template-detail-tips">
          <el-alert
            title="提示"
            type="info"
            :closable="false"
            show-icon>
            <template #default>
              点击图片可以放大查看，支持缩放和拖拽操作
            </template>
          </el-alert>
        </div>
      </div>
      <template #footer>
        <div class="template-detail-footer">
          <el-button @click="showTemplateDetailDialog = false" size="large">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 字段设置弹窗 -->
    <el-dialog v-model="showFieldSettingsDialog" title="字段设置" width="600px" :close-on-click-modal="false"
      class="field-settings-dialog" destroy-on-close>
      <div class="field-settings-container" v-if="currentFieldSettings">
        <el-form :model="currentFieldSettings" label-width="100px" size="default">
          <el-form-item label="字段名称">
            <el-input v-model="currentFieldSettings.chineseName" readonly />
          </el-form-item>

          <el-form-item label="关联表">
            <el-input v-model="currentFieldSettings.relatedTable"
              placeholder="例如：doro,dbid,dormitory,1"
              @click="showRelatedTableSelector"
              readonly
              style="cursor: pointer;">
              <template #suffix>
                <el-icon style="cursor: pointer;">
                  <View />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="是否必填">
            <el-checkbox v-model="showInSearchList">搜索列表显示</el-checkbox>
          </el-form-item>

          <el-form-item label="自定义选项">
            <el-input v-model="currentFieldSettings.customOptions"
              type="textarea"
              :rows="6"
              placeholder="一行一个选项，例如：&#10;选项1&#10;选项2&#10;选项3" />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="field-settings-footer">
          <el-button @click="closeFieldSettingsDialog">取消</el-button>
          <el-button type="primary" @click="saveFieldSettings">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 关联表选择弹窗 -->
    <el-dialog v-model="showRelatedTableDialog" title="选择关联表" width="800px" :close-on-click-modal="false"
      class="related-table-dialog" destroy-on-close>
      <div class="related-table-container">
        <el-table :data="availableRelatedTables" border stripe size="small" max-height="400">
          <el-table-column label="主键ID" prop="primaryKey" width="80" align="center" />
          <el-table-column label="名称" prop="displayName" width="120" />
          <el-table-column label="表名称" prop="tableName" width="150" />
          <el-table-column label="联动" width="100" align="center">
            <template #default="{ row }">
              <el-input v-model="row.linkValue" size="small" style="width: 60px;" />
            </template>
          </el-table-column>
          <el-table-column label="选择" width="80" align="center">
            <template #default="{ row }">
              <el-checkbox v-model="row.selected" />
            </template>
          </el-table-column>
        </el-table>
      </div>

      <template #footer>
        <div class="related-table-footer">
          <el-button @click="closeRelatedTableDialog">取消</el-button>
          <el-button type="primary" @click="confirmRelatedTableSelection">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
  @import '../../styles/CodeGenerator.css';

  /* 生成进度样式 */
  .generation-progress {
    margin-top: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    text-align: center;
  }

  .progress-text {
    margin-top: 10px;
    color: #606266;
    font-size: 14px;
  }

  /* 生成结果样式 */
  .generation-result {
    margin-top: 30px;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background: #fff;
  }

  .generation-status {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    border-radius: 6px;
  }

  .generation-status.success {
    background: #f0f9ff;
    border: 1px solid #67c23a;
    color: #67c23a;
  }

  .generation-status.error {
    background: #fef0f0;
    border: 1px solid #f56c6c;
    color: #f56c6c;
  }

  .status-icon {
    margin-right: 10px;
    font-size: 18px;
  }

  .status-text {
    font-weight: 500;
    font-size: 16px;
  }

  /* 文件列表样式 */
  .file-list-section {
    margin-top: 20px;
  }

  .file-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .file-list li {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 4px;
    border-radius: 4px;
    background: #f8f9fa;
  }

  .file-list.success li {
    background: #f0f9ff;
    border-left: 3px solid #67c23a;
  }

  .file-list.error li {
    background: #fef0f0;
    border-left: 3px solid #f56c6c;
  }

  .file-icon {
    margin-right: 8px;
    color: #909399;
  }

  .file-name {
    flex: 1;
    font-family: 'Courier New', monospace;
    font-size: 13px;
  }

  .file-error {
    color: #f56c6c;
    font-size: 12px;
    margin-left: 10px;
  }

  /* 字段设置弹窗样式 */
  .field-settings-dialog .el-dialog__body {
    padding: 20px;
  }

  .field-settings-container {
    max-height: 500px;
    overflow-y: auto;
  }

  .field-settings-footer {
    text-align: right;
  }

  /* 关联表选择弹窗样式 */
  .related-table-dialog .el-dialog__body {
    padding: 20px;
  }

  .related-table-container {
    max-height: 400px;
    overflow-y: auto;
  }

  .related-table-footer {
    text-align: right;
  }

  /* 压缩结果样式 */
  .compression-result {
    margin-top: 20px;
    padding: 15px;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    background: #fafafa;
  }

  .compression-info {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }

  .compression-text {
    margin-left: 10px;
    font-weight: 500;
  }

  .compression-details {
    width: 100%;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e4e7ed;
  }

  .compression-details p {
    margin: 5px 0;
    color: #606266;
  }
</style>

<script>
  import { ref, reactive, onMounted, nextTick, computed } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import request, { base } from "../../../utils/http"
  import {
    Setting, Folder, Edit, Cpu, DataBoard, DocumentCopy, Warning,
    Plus, View, Download, Delete, Document, Monitor, Box, Refresh,
    User, ArrowDown, ArrowUp, Loading, Close, Check, Picture
  } from '@element-plus/icons-vue'

  export default {
    name: 'CodeGenerator',
    components: {
      Setting, Folder, Edit, Cpu, DataBoard, DocumentCopy, Warning,
      Plus, View, Download, Delete, Document, Monitor, Box, Refresh,
      User, ArrowDown, ArrowUp, Loading, Close, Check, Picture
    },
    setup() {
      const activeTab = ref('project')
      const logLevel = ref('all')

      // 登录相关状态
      const isLoggedIn = ref(false)
      const loginDialogVisible = ref(false)
      const loginLoading = ref(false)
      const loginFormRef = ref(null)

      // 用户信息
      const userInfo = reactive({
        username: '',
        loginTime: ''
      })

      // 登录表单
      const loginForm = reactive({
        username: '',
        password: ''
      })

      // 登录表单验证规则
      const loginRules = {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ]
      }

      // 项目选择和配置
      const selectedProject = ref('')
      const projectsLoading = ref(false)
      const availableDatabases = ref([
        { value: '', text: '请选择数据库' }
      ])

      const projectForm = reactive({
        databaseType: 'mysql',
        databaseMode: 'new',
        projectCode: '',
        databaseName: '',
        selectedDatabase: '',
        name: '',
        packageName: 'com',
        backendTemplate: '',
        frontendTemplate: '',
        layer: '否',
        charts: '否',
        schoolName: '',
        adminId: '',
        adminName: '',
        adminRole: '',
        adminLoginName: '',
        copyProject: ''
      })

      // 项目表单相关
      const currentProjectInfo = ref(null)
      const projectTables = ref([])
      const projectTablesLoading = ref(false)

      const formFields = ref([
        { name: 'id', type: 'Long' },
        { name: 'name', type: 'String' },
        { name: 'email', type: 'String' },
        { name: 'createTime', type: 'Date' }
      ])

      const tableData = ref([
        { name: 'user', fields: 8, status: '已配置', updateTime: '2024-01-15 10:30:00' },
        { name: 'role', fields: 5, status: '未配置', updateTime: '2024-01-15 09:15:00' },
        { name: 'permission', fields: 6, status: '已配置', updateTime: '2024-01-14 16:45:00' }
      ])

      const sqlContent = ref(``)

      const logs = ref([
        { time: '2024-01-15 10:30:15', level: 'info', message: '开始生成代码...' },
        { time: '2024-01-15 10:30:16', level: 'success', message: '实体类生成成功' },
        { time: '2024-01-15 10:30:17', level: 'warning', message: '字段名称建议使用驼峰命名' },
        { time: '2024-01-15 10:30:18', level: 'error', message: '数据库连接失败，请检查配置' }
      ])

      // Cookie操作工具函数
      const setCookie = (name, value, days) => {
        const expires = new Date()
        expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000))
        document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`
      }

      const getCookie = (name) => {
        const nameEQ = name + "="
        const ca = document.cookie.split(';')
        for (let i = 0; i < ca.length; i++) {
          let c = ca[i]
          while (c.charAt(0) === ' ') c = c.substring(1, c.length)
          if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length)
        }
        return null
      }

      const deleteCookie = (name) => {
        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`
      }

      // 检查登录状态
      const checkLoginStatus = () => {
        // 首先检查sessionStorage中的用户信息
        const sessionUser = sessionStorage.getItem("user")
        const sessionUserLname = sessionStorage.getItem("userLname")

        if (sessionUser && sessionUserLname) {
          try {
            JSON.parse(sessionUser) // 验证JSON格式
            userInfo.username = sessionUserLname
            userInfo.loginTime = new Date().toLocaleString()
            isLoggedIn.value = true
            return
          } catch (error) {
            console.error('解析sessionStorage用户信息失败:', error)
          }
        }

        // 如果sessionStorage中没有，再检查Cookie
        const savedUser = getCookie('codeGeneratorUser')
        if (savedUser) {
          try {
            const userData = JSON.parse(decodeURIComponent(savedUser))
            userInfo.username = userData.username
            userInfo.loginTime = userData.loginTime
            isLoggedIn.value = true
          } catch (error) {
            console.error('解析Cookie用户信息失败:', error)
            deleteCookie('codeGeneratorUser')
          }
        } else {
          loginDialogVisible.value = true
        }
      }

      // 处理登录
      const handleLogin = async () => {
        if (!loginFormRef.value) return

        // 基本验证
        if (!loginForm.username) {
          ElMessage.warning('请输入用户名')
          return
        }
        if (!loginForm.password) {
          ElMessage.warning('请输入密码')
          return
        }

        try {
          loginLoading.value = true

          // 调用登录API
          const url = base + "/admin/login"
          const loginData = {
            lname: loginForm.username,
            pwd: loginForm.password
          }

          const res = await request.post(url, loginData)
          loginLoading.value = false

          if (res.code == 200) {
            console.log('登录成功:', JSON.stringify(res.resdata))

            // 保存用户信息到sessionStorage（参考管理员登录）
            sessionStorage.setItem("user", JSON.stringify(res.resdata))
            sessionStorage.setItem("userLname", res.resdata.lname)
            sessionStorage.setItem("role", "管理员")

            // 更新本地状态
            userInfo.username = res.resdata.lname
            userInfo.loginTime = new Date().toLocaleString()

            // 保存到Cookie，有效期15天
            const userData = {
              username: res.resdata.lname,
              loginTime: userInfo.loginTime,
              userId: res.resdata.id
            }
            setCookie('codeGeneratorUser', encodeURIComponent(JSON.stringify(userData)), 15)

            isLoggedIn.value = true
            loginDialogVisible.value = false

            // 重置表单
            loginForm.username = ''
            loginForm.password = ''

            ElMessage.success('登录成功！')
          } else {
            ElMessage.error(res.msg || '登录失败')
          }
        } catch (error) {
          loginLoading.value = false
          console.error('登录失败:', error)
          ElMessage.error('登录失败，请检查网络连接')
        }
      }

      // 处理用户下拉菜单命令
      const handleUserCommand = (command) => {
        if (command === 'logout') {
          handleLogout()
        }
      }

      // 退出登录
      const handleLogout = () => {
        // 清除Cookie
        deleteCookie('codeGeneratorUser')

        // 清除sessionStorage
        sessionStorage.removeItem("user")
        sessionStorage.removeItem("userLname")
        sessionStorage.removeItem("role")

        // 重置状态
        isLoggedIn.value = false
        userInfo.username = ''
        userInfo.loginTime = ''
        loginDialogVisible.value = true

        ElMessage.success('已退出登录')
      }

      // 项目类型选择
      const selectProject = (projectType) => {
        selectedProject.value = projectType
        console.log('选择项目类型:', projectType)
      }

      // 获取项目名称
      const getProjectName = (projectType) => {
        const projectNames = {
          'springboot-thymeleaf': 'SpringBoot + Thymeleaf',
          'springboot-miniprogram': 'SpringBoot + 小程序',
          'springboot-vue': 'SpringBoot + Vue',
          'ssm-vue': 'SSM + Vue'
        }
        return projectNames[projectType] || projectType
      }

      // 打开模板选择弹窗
      const openTemplateModal = () => {
        console.log('打开后台模板选择弹窗')
        showBackendTemplateSelector()
      }

      const openFrontTemplateModal = () => {
        console.log('打开前台模板选择弹窗')
        showFrontendTemplateSelector()
      }

      // 处理数据库模式变化
      const handleDatabaseModeChange = (mode) => {
        if (mode === 'existing') {
          loadProjects()
        } else {
          // 清空已有数据库选项
          availableDatabases.value = [{ value: '', text: '请选择数据库' }]
          projectForm.selectedDatabase = ''
        }
      }

      // 加载项目列表
      const loadProjects = async () => {
        try {
          projectsLoading.value = true
          const url = base + "/projects/list?currentPage=1&pageSize=1000"
          const params = {

          }

          const res = await request.post(url, { params })

          if (res.code === 200) {
            const projects = res.resdata || []
            availableDatabases.value = [
              { value: '', text: '请选择数据库' },
              ...projects.map(project => ({
                value: project.pid,
                text: `${project.pno}--${project.daname} (${project.pname})`
              }))
            ]
          } else {
            ElMessage.error('加载项目列表失败')
          }
        } catch (error) {
          console.error('加载项目列表失败:', error)
          ElMessage.error('加载项目列表失败，请检查网络连接')
        } finally {
          projectsLoading.value = false
        }
      }

      // 处理项目选择
      const handleProjectSelect = async (projectId) => {
        if (!projectId) return

        try {
          const url = base + "/projects/get?id=" + projectId;


          const res = await request.post(url, {})

          if (res.code === 200) {
            const project = res.resdata
            // 初始化表单数据
            projectForm.projectCode = project.pno || ''
            projectForm.databaseName = project.daname || ''
            projectForm.name = project.pname || ''
            projectForm.databaseType = project.dtype || 'mysql'
            projectForm.backendTemplate = project.by1 || ''
            projectForm.frontendTemplate = project.by2 || ''
            projectForm.layer = project.by4 || '否'
            projectForm.charts = project.by5 || '否'
            projectForm.schoolName = project.by6 || ''

            // 解析Session信息
            if (project.by3) {
              const sessionInfo = project.by3.split(',')
              projectForm.adminId = sessionInfo[0] || ''
              projectForm.adminName = sessionInfo[1] || ''
              projectForm.adminRole = sessionInfo[2] || ''
              projectForm.adminLoginName = sessionInfo[3] || ''
            }

            ElMessage.success('项目信息加载成功')
          } else {
            ElMessage.error('加载项目详情失败')
          }
        } catch (error) {
          console.error('加载项目详情失败:', error)
          ElMessage.error('加载项目详情失败，请检查网络连接')
        }
      }

      // 保存或更新项目到数据库
      const saveOrUpdateProject = async () => {
        try {
          // 构建Session信息
          const sessionInfo = [
            projectForm.adminId,
            projectForm.adminName,
            projectForm.adminRole,
            projectForm.adminLoginName
          ].join(',')

          const projectData = {
            ptype: selectedProject.value,
            dtype: projectForm.databaseType,
            pflag: projectForm.databaseMode === 'new' ? '1' : '2',
            pno: projectForm.projectCode,
            daname: projectForm.databaseName,
            pname: projectForm.name,
            by1: projectForm.backendTemplate,
            by2: projectForm.frontendTemplate,
            by3: sessionInfo,
            by4: projectForm.layer,
            by5: projectForm.charts,
            by6: projectForm.schoolName,
            by7: projectForm.copyProject,
            lname: userInfo.username
          }

          // 判断是新建还是更新
          const isUpdate = currentProjectInfo.value && currentProjectInfo.value.pid
          let url, res

          if (isUpdate) {
            // 更新项目
            projectData.pid = currentProjectInfo.value.pid
            url = base + "/projects/update"
            res = await request.post(url, projectData)
          } else {
            // 新建项目
            url = base + "/projects/add"
            res = await request.post(url, projectData)
          }

          if (res.code === 200) {
            const message = isUpdate ? '项目更新成功' : '项目保存成功'
            ElMessage.success(message)

            // 如果是新建项目，保存返回的项目ID
            if (!isUpdate && res.resdata && res.resdata.pid) {
              currentProjectInfo.value = {
                ...currentProjectInfo.value,
                pid: res.resdata.pid
              }
            }

            return { success: true, projectId: res.resdata?.pid || currentProjectInfo.value?.pid }
          } else {
            ElMessage.error(res.msg || '项目保存失败')
            return { success: false }
          }
        } catch (error) {
          console.error('保存项目失败:', error)
          ElMessage.error('保存项目失败，请检查网络连接')
          return { success: false }
        }
      }

      // 加载项目表单列表
      const loadProjectTables = async (projectId) => {
        try {
          projectTablesLoading.value = true
          const url = base + "/tables/list?currentPage=1&pageSize=1000"
          const params = {
            pid: projectId
          }

          const res = await request.post(url, params)

          if (res.code === 200) {
            projectTables.value = res.resdata || []

            // 为每个表加载字段数据
            for (let table of projectTables.value) {
              try {
                const fieldsUrl = base + "/mores/list?currentPage=1&pageSize=100"
                const fieldsRes = await request.post(fieldsUrl, { tid: table.tid })

                if (fieldsRes.code === 200 && fieldsRes.resdata) {
                  // 将字段数据转换为前端格式
                  table.fields = fieldsRes.resdata.map(field => ({
                    id: field.mid,
                    tid: field.tid,
                    chineseName: field.mozname,
                    englishName: field.moname,
                    type: field.motype,
                    controlType: field.moflag,
                    required: field.moyz === '1',
                    searchable: field.mobt === '1',
                    visible: field.by1 === '1',
                    existsCheck: field.by2 === '1',
                    relatedTable: field.by3 || '', // 关联表
                    customOptions: field.by4 || '' // 自定义选项
                  }))

                  // 设置表的生成数据条数
                  table.generateDataCount = parseInt(table.by1 || '0')
                } else {
                  table.fields = []
                }
              } catch (error) {
                console.warn('加载表字段失败:', table.tname, error)
                table.fields = []
              }
            }

            console.log('加载项目表单成功:', projectTables.value)
          } else {
            ElMessage.error('加载项目表单失败')
          }
        } catch (error) {
          console.error('加载项目表单失败:', error)
          ElMessage.error('加载项目表单失败，请检查网络连接')
        } finally {
          projectTablesLoading.value = false
        }
      }

      // 表设计相关
      const showTableDesignModal = ref(false)
      const currentTableTab = ref('table-settings')
      const currentTableDesign = ref({
        id: null,
        chineseName: '',
        englishName: '',
        menuOrder: 1,
        generateData: '0',
        generateDataCount: 0,
        functions: {
          backendAdd: true, backendEdit: true, backendDelete: true,
          backendDetail: true, backendList: false, batchImport: false,
          batchExport: false, backendLogin: false, backendRegister: false,
          backendProfile: false, backendPassword: false,
          frontendAdd: false, frontendEdit: false, frontendDelete: false,
          frontendDetail: false, frontendList: false, frontendLogin: false,
          miniAdd: false, miniEdit: false, miniDelete: false,
          miniDetail: false, miniList: false
        },
        fields: [
          {
            id: 1,
            chineseName: '主键ID',
            englishName: 'id',
            type: 'int',
            controlType: '文本框',
            required: true,
            searchable: false,
            visible: true,
            existsCheck: false,
            relatedTable: '',
            customOptions: ''
          }
        ]
      })

      // 功能选择的响应式数组
      const backendFunctions = ref([])
      const frontendFunctions = ref([])
      const miniFunctions = ref([])

      // 重置字段
      const resetFields = () => {
        currentTableDesign.value.fields = [
          {
            id: 1,
            chineseName: '主键ID',
            englishName: 'id',
            type: 'int',
            controlType: '文本框',
            required: true,
            searchable: false,
            visible: true,
            existsCheck: false,
            relatedTable: '',
            customOptions: ''
          }
        ]
        ElMessage.success('字段已重置')
      }

      // AI生成器相关状态
      const showAiGeneratorDialog = ref(false)
      const aiGeneratorInput = ref('')
      const aiGenerationInProgress = ref(false)

      // 模板选择相关状态
      const showBackendTemplateDialog = ref(false)
      const showFrontendTemplateDialog = ref(false)
      const showTemplateDetailDialog = ref(false)
      const backendTemplates = ref([])
      const frontendTemplates = ref([])
      const templatesLoading = ref(false)
      const currentTemplateDetail = ref(null)

      // 项目生成相关状态
      const generationInProgress = ref(false)
      const generationResult = ref(null)

      // SQL脚本生成相关状态
      const sqlGenerationInProgress = ref(false)

      // 数据脚本生成相关状态
      const dataGenerationInProgress = ref(false)
      const dataContent = ref('')

      // 字段设置相关状态
      const showFieldSettingsDialog = ref(false)
      const currentFieldSettings = ref(null)
      const currentFieldIndex = ref(-1)
      const showInSearchList = ref(false)

      // 关联表选择相关状态
      const showRelatedTableDialog = ref(false)
      const availableRelatedTables = ref([])

      // 显示AI生成器弹窗
      const showAiGeneratorModal = () => {
        aiGeneratorInput.value = ''
        showAiGeneratorDialog.value = true
        nextTick(() => {
          // 聚焦到输入框
          const inputElement = document.querySelector('.ai-generator-input')
          if (inputElement) {
            inputElement.focus()
          }
        })
      }

      // 隐藏AI生成器弹窗
      const hideAiGeneratorModal = () => {
        showAiGeneratorDialog.value = false
        aiGeneratorInput.value = ''
      }

      // 确认AI生成
      const confirmAiGeneration = async () => {
        const description = aiGeneratorInput.value.trim()
        if (!description) {
          ElMessage.warning('请输入描述内容')
          return
        }

        // 隐藏弹窗
        hideAiGeneratorModal()

        // 调用AI生成
        await doubaoGenerate(description)
      }

      // 调用豆包AI生成表单
      const doubaoGenerate = async (str) => {
        if (aiGenerationInProgress.value) {
          ElMessage.warning('AI正在生成中，请稍候...')
          return
        }

        // 构建prompt
        const input_text = "用户:users\n" +
          "aid|lname|password|role\n" +
          "用户id|用户名|密码|身份\n" +
          "int|varchar(50)|varchar(50)|int\n" +
          "\n" +
          "学习上面的格式。格式说明如下。\n" +
          "第1行表中文名称:表英文名称。\n" +
          "第2行字段列表，字段简写\n" +
          "第3行字段对应中文\n" +
          "第4行字段类型。如果是字符型加上长度。\n" +
          "\n" +
          "按上面的格式生成下面的内容。不要注释，只返回格式的内容\n" + str

        console.log('发送给豆包AI的内容:', input_text)

        const settings = {
          url: "https://ark.cn-beijing.volces.com/api/v3/chat/completions",
          method: "POST",
          timeout: 30000,
          headers: {
            "Authorization": "Bearer 8d71b27a-b4c9-484e-896b-247f7dda5412",
            "Content-Type": "application/json"
          },
          data: JSON.stringify({
            "model": "doubao-1.5-pro-32k-250115",
            "messages": [
              {
                "role": "system",
                "content": "你是一个数据库设计专家，专门帮助用户设计数据表结构。请严格按照指定的格式返回结果，不要添加任何额外的说明或注释，表名和字段中不要用下划线，不要大写字母。不要用关键字和保留字"
              },
              {
                "role": "user",
                "content": input_text
              }
            ]
          })
        }

        // 显示生成中状态
        aiGenerationInProgress.value = true
        ElMessage.info('正在调用豆包AI生成表结构，请稍候...')

        try {
          const response = await fetch(settings.url, {
            method: settings.method,
            headers: settings.headers,
            body: settings.data
          })

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
          }

          const result = await response.json()
          aiGenerationInProgress.value = false

          // 从豆包AI响应中提取内容
          const generatedContent = result.choices[0].message.content
          console.log('豆包AI生成的原始内容:', generatedContent)

          // 清理返回内容
          const cleanedContent = generatedContent
            .replace(/```[\s\S]*?\n/, '') // 移除开头的markdown代码块标记
            .replace(/\n```[\s\S]*?$/, '') // 移除结尾的markdown代码块标记
            .replace(/^\s+|\s+$/g, '') // 移除首尾空白
            .replace(/\r\n/g, '\n') // 统一换行符
            .replace(/\r/g, '\n') // 处理Mac格式换行符

          console.log('清理后的内容:', cleanedContent)

          // 检查内容是否为空
          if (!cleanedContent || cleanedContent.trim() === '') {
            throw new Error('豆包AI返回的内容为空')
          }

          createFormFromAI(cleanedContent)
          ElMessage.success('AI生成成功！')

        } catch (error) {
          aiGenerationInProgress.value = false
          console.error('处理豆包AI响应时出错:', error)
          ElMessage.error('AI生成失败：' + error.message)
        }
      }

      // 解析AI生成的内容并创建表单
      const createFormFromAI = (content) => {
        try {
          console.log('开始解析AI生成的内容:', content)

          // 按行分割内容，移除空行
          const allLines = content.split('\n')
          const lines = allLines.map(line => line.trim()).filter(line => line !== '')
          console.log('过滤后的有效行:', lines)

          if (lines.length < 4) {
            throw new Error(`豆包AI返回的格式不完整，需要4行内容，实际只有${lines.length}行`)
          }

          // 解析第1行：表名
          const tableNameLine = lines[0]
          const tableNameMatch = tableNameLine.match(/^(.+):(.+)$/)
          if (!tableNameMatch) {
            throw new Error('表名格式不正确，应为：中文名:英文名，实际为：' + tableNameLine)
          }

          const chineseName = tableNameMatch[1].trim()
          const englishName = tableNameMatch[2].trim()

          // 解析第2行：英文字段名
          const englishFields = lines[1].split('|').map(field => field.trim()).filter(field => field !== '')

          // 解析第3行：中文字段名
          const chineseFields = lines[2].split('|').map(field => field.trim()).filter(field => field !== '')

          // 解析第4行：字段类型
          const fieldTypes = lines[3].split('|').map(type => type.trim()).filter(type => type !== '')

          // 验证字段数量一致性
          if (englishFields.length !== chineseFields.length || englishFields.length !== fieldTypes.length) {
            throw new Error(`字段数量不匹配：英文名${englishFields.length}个，中文名${chineseFields.length}个，类型${fieldTypes.length}个`)
          }

          // 构建字段数据
          const fields = []
          for (let i = 0; i < englishFields.length; i++) {
            const field = {
              id: Date.now() + i,
              chineseName: chineseFields[i],
              englishName: englishFields[i],
              type: fieldTypes[i],
              controlType: inferControlType(fieldTypes[i]),
              required: true, // AI生成的字段默认必填项选中
              searchable: i > 0 && i < 3, // 前几个字段设为可搜索
              visible: true,
              existsCheck: false,
              relatedTable: '',
              customOptions: ''
            }
            fields.push(field)
          }

          // 完整替换表结构
          currentTableDesign.value.chineseName = chineseName
          currentTableDesign.value.englishName = englishName
          currentTableDesign.value.fields = fields

          // 确保functions对象存在
          if (!currentTableDesign.value.functions) {
            currentTableDesign.value.functions = {}
          }

          // 默认选中后台功能
          currentTableDesign.value.functions.backendAdd = true
          currentTableDesign.value.functions.backendEdit = true
          currentTableDesign.value.functions.backendDelete = true
          currentTableDesign.value.functions.backendDetail = true
          currentTableDesign.value.functions.backendList = false

          console.log('表单创建成功:', {
            chineseName: chineseName,
            englishName: englishName,
            fieldsCount: fields.length
          })

        } catch (error) {
          console.error('解析AI内容失败:', error)
          ElMessage.error('解析AI生成的内容失败：' + error.message)
        }
      }

      // 根据字段类型推断控件类型
      const inferControlType = (fieldType) => {
        const type = fieldType.toLowerCase()

        if (type.includes('int') || type.includes('bigint')) {
          return '文本框'
        } else if (type.includes('decimal') || type.includes('float') || type.includes('double')) {
          return '文本框'
        } else if (type.includes('text') || type.includes('longtext')) {
          return '多行文本'
        } else if (type.includes('datetime') || type.includes('timestamp')) {
          return '日期时间'
        } else if (type.includes('date')) {
          return '日期选择'
        } else if (type.includes('varchar') && type.includes('200')) {
          return '多行文本'
        } else if (type.includes('varchar')) {
          return '文本框'
        } else {
          return '文本框'
        }
      }

      // 模板选择相关函数

      // 显示后台模板选择弹窗
      const showBackendTemplateSelector = async () => {
        showBackendTemplateDialog.value = true
        await loadBackendTemplates()
      }

      // 显示前台模板选择弹窗
      const showFrontendTemplateSelector = async () => {
        showFrontendTemplateDialog.value = true
        await loadFrontendTemplates()
      }

      // 加载后台模板
      const loadBackendTemplates = async () => {
        try {
          templatesLoading.value = true
          const response = await fetch(base+'/small/backend-templates')
          const result = await response.json()
          if (result.code === 200) {
            backendTemplates.value = result.resdata || []
          } else {
            ElMessage.error('加载后台模板失败')
          }
        } catch (error) {
          console.error('加载后台模板失败:', error)
          ElMessage.error('加载后台模板失败')
        } finally {
          templatesLoading.value = false
        }
      }

      // 加载前台模板
      const loadFrontendTemplates = async () => {
        try {
          templatesLoading.value = true
          const response = await fetch(base+'/small/frontend-templates')
          const result = await response.json()
          if (result.code === 200) {
            frontendTemplates.value = result.resdata || []
          } else {
            ElMessage.error('加载前台模板失败')
          }
        } catch (error) {
          console.error('加载前台模板失败:', error)
          ElMessage.error('加载前台模板失败')
        } finally {
          templatesLoading.value = false
        }
      }

      // 选择后台模板
      const selectBackendTemplate = (template) => {
        // 设置到项目表单的后台模板字段 - 保存模板ID
        projectForm.backendTemplate = template.sid
        showBackendTemplateDialog.value = false
        ElMessage.success(`已选择后台模板: ${template.sname} (ID: ${template.sid})`)
      }

      // 选择前台模板
      const selectFrontendTemplate = (template) => {
        // 设置到项目表单的前台模板字段 - 保存模板ID
        projectForm.frontendTemplate = template.sid
        showFrontendTemplateDialog.value = false
        ElMessage.success(`已选择前台模板: ${template.sname} (ID: ${template.sid})`)
      }

      // 查看模板详情
      const viewTemplateDetail = (template) => {
        currentTemplateDetail.value = template
        showTemplateDetailDialog.value = true
      }

      // 计算属性：是否可以生成项目
      const canGenerate = computed(() => {
        return projectForm.name &&
               projectForm.databaseName &&
               projectForm.projectCode &&
               projectTables.value.length > 0 &&
               !generationInProgress.value
      })

      // 生成项目
      const generateProject = async () => {
        if (!canGenerate.value) {
          ElMessage.warning('请完善项目配置信息')
          return
        }

        try {
          generationInProgress.value = true
          generationResult.value = null

          // 构建项目数据
          const projectData = {
            projectNumber: projectForm.projectCode,
            databaseName: projectForm.databaseName,
            projectName: projectForm.name,
            packageName: projectForm.packageName || 'com',
            databaseType: projectForm.databaseType || 'mysql',
            backendTemplate: projectForm.backendTemplate,
            frontendTemplate: projectForm.frontendTemplate,
            tables: projectTables.value.map(table => ({
              id: table.tid,
              chineseName: table.tword,
              englishName: table.tname,
              functions: table.tgn ? JSON.parse(table.tgn) : {},
              fields: table.fields || []
            }))
          }

          console.log('开始生成项目:', projectData)

          // 调用Java后端API生成项目
          const response = await fetch(base + '/projects/generate', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(projectData)
          })

          const result = await response.json()

          if (result.code === 200) {
            // 适配后端返回的数据结构
            const filesData = result.resdata.files && result.resdata.files.data ? result.resdata.files.data : {}
            const compressionData = result.resdata.compression || null

            generationResult.value = {
              status: 'success',
              message: '项目生成成功！',
              files: filesData,
              compression: compressionData
            }

            // 生成项目成功后，自动生成SQL脚本和数据脚本
            await generateSqlScript()
            await generateDataScript()

            ElMessage.success('项目生成成功！')
          } else {
            throw new Error(result.msg || '项目生成失败')
          }

        } catch (error) {
          console.error('项目生成失败:', error)
          generationResult.value = {
            status: 'error',
            message: '项目生成失败：' + error.message,
            files: null,
            compression: null
          }
          ElMessage.error('项目生成失败：' + error.message)
        } finally {
          generationInProgress.value = false
        }
      }

      // 生成SQL脚本
      const generateSqlScript = async () => {
        if (!currentProjectInfo.value || !projectTables.value || projectTables.value.length === 0) {
          ElMessage.warning('请先配置项目和数据表')
          return
        }

        try {
          sqlGenerationInProgress.value = true

          // 根据数据库类型生成对应的SQL脚本
          const databaseType = projectForm.databaseType || 'mysql'
          let script = ''

          if (databaseType === 'mysql') {
            script = generateMySqlScript()
          } else if (databaseType === 'sqlserver') {
            script = generateSqlServerScript()
          }

          sqlContent.value = script
          ElMessage.success('SQL脚本生成成功！')

        } catch (error) {
          console.error('生成SQL脚本失败:', error)
          ElMessage.error('生成SQL脚本失败：' + error.message)
        } finally {
          sqlGenerationInProgress.value = false
        }
      }

      // 生成MySQL脚本
      const generateMySqlScript = () => {
        let script = `-- ${projectForm.name || '项目'} 数据库脚本 (MySQL)\n`
        script += `-- 数据库名称: ${projectForm.databaseName}\n`
        script += `-- 生成时间: ${new Date().toLocaleString()}\n\n`

        // 创建数据库
        script += `CREATE DATABASE IF NOT EXISTS \`${projectForm.databaseName}\` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n`
        script += `USE \`${projectForm.databaseName}\`;\n\n`

        // 为每个表生成建表语句
        projectTables.value.forEach(table => {
          script += generateMySqlTableScript(table)
          script += '\n'
        })

        return script
      }

      // 生成SQL Server脚本
      const generateSqlServerScript = () => {
        let script = `-- ${projectForm.name || '项目'} 数据库脚本 (SQL Server)\n`
        script += `-- 数据库名称: ${projectForm.databaseName}\n`
        script += `-- 生成时间: ${new Date().toLocaleString()}\n\n`

        // 创建数据库
        script += `IF NOT EXISTS (SELECT * FROM sys.databases WHERE name = '${projectForm.databaseName}')\n`
        script += `CREATE DATABASE [${projectForm.databaseName}];\n`
        script += `GO\n\n`
        script += `USE [${projectForm.databaseName}];\n`
        script += `GO\n\n`

        // 为每个表生成建表语句
        projectTables.value.forEach(table => {
          script += generateSqlServerTableScript(table)
          script += '\n'
        })

        return script
      }

      // 生成MySQL表脚本
      const generateMySqlTableScript = (table) => {
        let script = `-- 表: ${table.tword || table.tname}\n`
        script += `DROP TABLE IF EXISTS \`${table.tname}\`;\n`
        script += `CREATE TABLE \`${table.tname}\` (\n`

        const fields = table.fields || []
        const fieldScripts = fields.map((field, index) => {
          let fieldScript = `  \`${field.englishName}\` ${convertToMySqlType(field.type)}`

          // 主键处理 - 修复第一个字段的AUTO_INCREMENT问题
          if (field.englishName.toLowerCase() === 'id' || index === 0) {
            // 只有int类型的字段才能使用AUTO_INCREMENT
            if (field.type && field.type.toLowerCase() === 'int') {
              fieldScript += ' AUTO_INCREMENT NOT NULL'
            } else {
              fieldScript += ' NOT NULL'
            }
          } else if (field.required) {
            fieldScript += ' NOT NULL'
          }

          // 注释
          if (field.chineseName) {
            fieldScript += ` COMMENT '${field.chineseName}'`
          }

          return fieldScript
        })

        script += fieldScripts.join(',\n')

        // 主键约束
        if (fields.length > 0) {
          const primaryKey = fields[0].englishName
          script += `,\n  PRIMARY KEY (\`${primaryKey}\`)`
        }

        script += `\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='${table.tword || table.tname}';\n\n`

        return script
      }

      // 生成SQL Server表脚本
      const generateSqlServerTableScript = (table) => {
        let script = `-- 表: ${table.tword || table.tname}\n`
        script += `IF OBJECT_ID('[${table.tname}]', 'U') IS NOT NULL DROP TABLE [${table.tname}];\n`
        script += `CREATE TABLE [${table.tname}] (\n`

        const fields = table.fields || []
        const fieldScripts = fields.map((field, index) => {
          let fieldScript = `  [${field.englishName}] ${convertToSqlServerType(field.type)}`

          // 主键处理
          if (field.englishName.toLowerCase() === 'id' || index === 0) {
            fieldScript += ' IDENTITY(1,1)'
          }

          // 非空约束
          if (field.required) {
            fieldScript += ' NOT NULL'
          }

          return fieldScript
        })

        script += fieldScripts.join(',\n')

        // 主键约束
        if (fields.length > 0) {
          const primaryKey = fields[0].englishName
          script += `,\n  CONSTRAINT [PK_${table.tname}] PRIMARY KEY ([${primaryKey}])`
        }

        script += `\n);\n`

        // 添加表注释
        if (table.tword) {
          script += `EXEC sp_addextendedproperty 'MS_Description', '${table.tword}', 'SCHEMA', 'dbo', 'TABLE', '${table.tname}';\n`
        }

        // 添加字段注释
        fields.forEach(field => {
          if (field.chineseName) {
            script += `EXEC sp_addextendedproperty 'MS_Description', '${field.chineseName}', 'SCHEMA', 'dbo', 'TABLE', '${table.tname}', 'COLUMN', '${field.englishName}';\n`
          }
        })

        script += `GO\n\n`

        return script
      }

      // 转换为MySQL数据类型
      const convertToMySqlType = (type) => {
        if (!type) return 'VARCHAR(100)'

        const lowerType = type.toLowerCase()
        if (lowerType === 'int') return 'INT'
        if (lowerType.includes('varchar')) return type.toUpperCase()
        if (lowerType === 'text') return 'TEXT'
        if (lowerType === 'datetime') return 'DATETIME'
        if (lowerType.includes('decimal')) return type.toUpperCase()

        return type.toUpperCase()
      }

      // 转换为SQL Server数据类型
      const convertToSqlServerType = (type) => {
        if (!type) return 'NVARCHAR(100)'

        const lowerType = type.toLowerCase()
        if (lowerType === 'int') return 'INT'
        if (lowerType.includes('varchar')) {
          // 将varchar转换为nvarchar
          return type.replace(/varchar/i, 'NVARCHAR')
        }
        if (lowerType === 'text') return 'NTEXT'
        if (lowerType === 'datetime') return 'DATETIME'
        if (lowerType.includes('decimal')) return type.toUpperCase()

        return type.toUpperCase()
      }

      // 导出SQL脚本
      const exportSqlScript = () => {
        if (!sqlContent.value || sqlContent.value.trim() === '') {
          ElMessage.warning('请先生成SQL脚本')
          return
        }

        try {
          const blob = new Blob([sqlContent.value], { type: 'text/plain;charset=utf-8' })
          const url = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = url

          const databaseType = projectForm.databaseType || 'mysql'
          const fileName = `${projectForm.databaseName || 'database'}_${databaseType}.sql`
          link.download = fileName

          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)

          ElMessage.success('SQL脚本导出成功！')
        } catch (error) {
          console.error('导出SQL脚本失败:', error)
          ElMessage.error('导出SQL脚本失败：' + error.message)
        }
      }

      // 复制SQL脚本到剪切板
      const copySqlScript = async () => {
        if (!sqlContent.value || sqlContent.value.trim() === '') {
          ElMessage.warning('请先生成SQL脚本')
          return
        }

        try {
          await navigator.clipboard.writeText(sqlContent.value)
          ElMessage.success('SQL脚本已复制到剪切板！')
        } catch (error) {
          console.error('复制到剪切板失败:', error)
          // 降级方案：使用传统的复制方法
          try {
            const textArea = document.createElement('textarea')
            textArea.value = sqlContent.value
            document.body.appendChild(textArea)
            textArea.select()
            document.execCommand('copy')
            document.body.removeChild(textArea)
            ElMessage.success('SQL脚本已复制到剪切板！')
          } catch (fallbackError) {
            console.error('降级复制方法也失败:', fallbackError)
            ElMessage.error('复制到剪切板失败，请手动复制')
          }
        }
      }

      // 生成数据脚本
      const generateDataScript = async () => {
        if (!currentProjectInfo.value || !projectTables.value || projectTables.value.length === 0) {
          ElMessage.warning('请先配置项目和数据表')
          return
        }

        try {
          dataGenerationInProgress.value = true

          // 筛选出需要生成数据的表（生成数据条数大于0）
          const tablesWithData = projectTables.value.filter(table => {
            const dataCount = parseInt(table.by1 || '0')
            return dataCount > 0
          })

          if (tablesWithData.length === 0) {
            ElMessage.warning('没有设置生成数据条数的表，请先在表设计中设置生成数据条数')
            return
          }

          let script = ''

          // 为每个需要生成数据的表生成建表语句和插入数据
          for (const table of tablesWithData) {
            script += generateTableWithDataScript(table)
            script += '\n'
          }

          // 添加生成要求说明
          script += `项目名称是：${projectForm.name || '智慧社区网格化管理系统'}\n`
          script += `按要求生成的条数，生成上面所有的数据，数据内容要多一些，数据模拟真实的数据\n`
          script += `如果有密码，密码为123456。\n`
          script += `时间字段为当前时间\n`
          script += `生成的数据为中文，只生成insert into数据，不要注释说明\n`

          dataContent.value = script
          ElMessage.success('数据脚本生成成功！')

        } catch (error) {
          console.error('生成数据脚本失败:', error)
          ElMessage.error('生成数据脚本失败：' + error.message)
        } finally {
          dataGenerationInProgress.value = false
        }
      }

      // 生成单个表的建表语句和数据
      const generateTableWithDataScript = (table) => {
        const fields = table.fields || []
        const dataCount = parseInt(table.by1 || '0')

        let script = `create table if NOT EXISTS ${table.tname} \n(\n`

        // 生成字段定义
        const fieldScripts = fields.map((field, index) => {
          let fieldScript = `${field.englishName}   ${convertToMySqlType(field.type)}`

          // 主键处理 - 修复第一个字段的AUTO_INCREMENT问题
          if (field.englishName.toLowerCase() === 'id' || index === 0) {
            // 只有int类型的字段才能使用AUTO_INCREMENT
            if (field.type && field.type.toLowerCase() === 'int') {
              fieldScript += ' auto_increment  primary key'
            } else {
              fieldScript += ' not null    primary key'
            }
          } else if (field.englishName.toLowerCase().includes('account') ||
                     field.englishName.toLowerCase().includes('username')) {
            fieldScript += ' not null    primary key'
          } else if (field.required) {
            fieldScript += ' not null   '
          } else {
            fieldScript += '  null   '
          }

          // 注释
          if (field.chineseName) {
            fieldScript += ` comment '${field.chineseName}'`
          }

          return fieldScript
        })

        script += fieldScripts.join(' ,\n') + ' \n'
        script += `) ENGINE=MyISAM AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;\n\n`

        // 添加生成数据条数说明
        if (dataCount > 0) {
          script += `生成${dataCount}条insert into数据\n\n`
        }

        return script
      }

      // 复制数据脚本到剪切板
      const copyDataScript = async () => {
        if (!dataContent.value || dataContent.value.trim() === '') {
          ElMessage.warning('请先生成数据脚本')
          return
        }

        try {
          await navigator.clipboard.writeText(dataContent.value)
          ElMessage.success('数据脚本已复制到剪切板！')
        } catch (error) {
          console.error('复制到剪切板失败:', error)
          // 降级方案：使用传统的复制方法
          try {
            const textArea = document.createElement('textarea')
            textArea.value = dataContent.value
            document.body.appendChild(textArea)
            textArea.select()
            document.execCommand('copy')
            document.body.removeChild(textArea)
            ElMessage.success('数据脚本已复制到剪切板！')
          } catch (fallbackError) {
            console.error('降级复制方法也失败:', fallbackError)
            ElMessage.error('复制到剪切板失败，请手动复制')
          }
        }
      }

      // 下载项目文件
      const downloadProject = (filePath) => {
        if (filePath) {
          // 从filePath中提取文件名
          const fileName = filePath.split('/').pop()
          // 使用新的下载接口
          const downloadUrl = `${base}/projects/download?fileName=${encodeURIComponent(fileName)}`
          window.open(downloadUrl, '_blank')
        } else {
          ElMessage.error('下载链接无效')
        }
      }

      // 获取模板图片URL
      const getTemplateImageUrl = (memo4) => {
        if (!memo4) return ''

        // 从HTML中提取图片URL
        const imgMatch = memo4.match(/<img[^>]+src="([^"]+)"/)
        if (imgMatch && imgMatch[1]) {
          return imgMatch[1]
        }

        return ''
      }

      // 处理图片加载错误
      const handleImageError = (event) => {
        event.target.style.display = 'none'
        const parent = event.target.parentElement
        if (parent) {
          parent.innerHTML = '<div class="no-image"><span>图片加载失败</span></div>'
        }
      }

      // 获取表格字段数量
      const getTableFieldCount = (table) => {
        // 如果有字段数据，返回字段数量
        if (table.fields && Array.isArray(table.fields)) {
          return table.fields.length
        }
        // 否则返回0
        return 0
      }

      // 获取表格功能列表
      const getTableFunctions = (table) => {
        if (!table.tgn) return []

        try {
          const functions = JSON.parse(table.tgn)
          const activeFunctions = []

          // 检查各种功能是否启用
          if (functions.backendAdd) activeFunctions.push('后台添加')
          if (functions.backendEdit) activeFunctions.push('后台修改')
          if (functions.backendDelete) activeFunctions.push('后台删除')
          if (functions.backendDetail) activeFunctions.push('后台详情')
          if (functions.backendList) activeFunctions.push('后台列表')
          if (functions.frontendAdd) activeFunctions.push('前台添加')
          if (functions.frontendEdit) activeFunctions.push('前台修改')
          if (functions.frontendDelete) activeFunctions.push('前台删除')
          if (functions.miniAdd) activeFunctions.push('小程序添加')

          return activeFunctions.slice(0, 3) // 只显示前3个功能
        } catch (error) {
          console.error('解析表功能配置失败:', error)
          return []
        }
      }

      // 打开表设计弹窗
      const openTableDesignModal = (table = null) => {
        if (table) {
          // 编辑现有表
          currentTableDesign.value = {
            id: table.tid,
            chineseName: table.tword || '',
            englishName: table.tname || '',
            menuOrder: parseInt(table.by2 || '1'),
            generateData: '0',
            generateDataCount: parseInt(table.by1 || '0'),
            functions: table.tgn ? JSON.parse(table.tgn) : {
              backendAdd: true, backendEdit: true, backendDelete: true,
              backendDetail: true, backendList: false, batchImport: false,
              batchExport: false, backendLogin: false, backendRegister: false,
              backendProfile: false, backendPassword: false,
              frontendAdd: false, frontendEdit: false, frontendDelete: false,
              frontendDetail: false, frontendList: false, frontendLogin: false,
              miniAdd: false, miniEdit: false, miniDelete: false,
              miniDetail: false, miniList: false
            },
            fields: table.fields || [
              {
                id: 1,
                chineseName: '主键ID',
                englishName: 'id',
                type: 'int',
                controlType: '文本框',
                required: true,
                searchable: false,
                visible: true,
                existsCheck: false
              }
            ]
          }
        } else {
          // 创建新表
          resetTableDesign()
        }
        showTableDesignModal.value = true
      }

      // 重置表设计数据
      const resetTableDesign = () => {
        currentTableDesign.value = {
          id: null,
          chineseName: '',
          englishName: '',
          menuOrder: 1,
          generateData: '0',
          generateDataCount: 0,
          functions: {
            backendAdd: true, backendEdit: true, backendDelete: true,
            backendDetail: true, backendList: false, batchImport: false,
            batchExport: false, backendLogin: false, backendRegister: false,
            backendProfile: false, backendPassword: false,
            frontendAdd: false, frontendEdit: false, frontendDelete: false,
            frontendDetail: false, frontendList: false, frontendLogin: false,
            miniAdd: false, miniEdit: false, miniDelete: false,
            miniDetail: false, miniList: false
          },
          fields: [
            {
              id: 1,
              chineseName: '主键ID',
              englishName: 'id',
              type: 'int',
              controlType: '文本框',
              required: true,
              searchable: false,
              visible: true,
              existsCheck: false
            }
          ]
        }
      }

      // 关闭表设计弹窗
      const closeTableDesignModal = () => {
        showTableDesignModal.value = false
        currentTableTab.value = 'table-settings'
      }

      // 表设计弹窗Tab切换功能
      const switchTableTab = (tabId) => {
        currentTableTab.value = tabId
      }

      // 添加字段到设计中
      const addNewFieldToDesign = () => {
        const newField = {
          id: Date.now(),
          chineseName: '',
          englishName: '',
          type: 'varchar(100)',
          controlType: '文本框',
          required: true, // 默认必填项选中
          searchable: false,
          visible: true,
          existsCheck: false,
          relatedTable: '', // 关联表
          customOptions: '' // 自定义选项
        }
        currentTableDesign.value.fields.push(newField)
      }

      // 清空所有字段
      const clearAllFields = async () => {
        try {
          await ElMessageBox.confirm(
            '确定要清空所有字段吗？此操作不可撤销。',
            '清空字段',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            }
          )

          currentTableDesign.value.fields = []
          ElMessage.success('字段清空成功')
        } catch (error) {
          if (error !== 'cancel') {
            console.error('清空字段失败:', error)
          }
        }
      }

      // 删除设计中的字段
      const deleteFieldFromDesign = async (index) => {
        try {
          await ElMessageBox.confirm(
            '确定要删除这个字段吗？此操作不可撤销。',
            '删除字段',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            }
          )

          currentTableDesign.value.fields.splice(index, 1)
          ElMessage.success('字段删除成功')
        } catch (error) {
          if (error !== 'cancel') {
            console.error('删除字段失败:', error)
          }
        }
      }

      // 上移字段
      const moveFieldUp = (index) => {
        if (index > 0) {
          const field = currentTableDesign.value.fields.splice(index, 1)[0]
          currentTableDesign.value.fields.splice(index - 1, 0, field)
        }
      }

      // 下移字段
      const moveFieldDown = (index) => {
        if (index < currentTableDesign.value.fields.length - 1) {
          const field = currentTableDesign.value.fields.splice(index, 1)[0]
          currentTableDesign.value.fields.splice(index + 1, 0, field)
        }
      }

      // 编辑字段设置
      const editFieldSettings = (field, index) => {
        currentFieldSettings.value = { ...field }
        currentFieldIndex.value = index
        showInSearchList.value = field.searchable || false
        showFieldSettingsDialog.value = true
      }

      // 关闭字段设置弹窗
      const closeFieldSettingsDialog = () => {
        showFieldSettingsDialog.value = false
        currentFieldSettings.value = null
        currentFieldIndex.value = -1
      }

      // 保存字段设置
      const saveFieldSettings = () => {
        if (currentFieldIndex.value >= 0 && currentFieldSettings.value) {
          // 更新字段数据
          const field = currentTableDesign.value.fields[currentFieldIndex.value]
          field.relatedTable = currentFieldSettings.value.relatedTable
          field.customOptions = currentFieldSettings.value.customOptions
          field.searchable = showInSearchList.value

          ElMessage.success('字段设置保存成功')
          closeFieldSettingsDialog()
        }
      }

      // 显示关联表选择器
      const showRelatedTableSelector = async () => {
        if (!currentProjectInfo.value || !currentProjectInfo.value.pid) {
          ElMessage.warning('请先保存项目信息')
          return
        }

        try {
          // 加载项目的所有表
          await loadAvailableRelatedTables()
          showRelatedTableDialog.value = true
        } catch (error) {
          console.error('加载关联表失败:', error)
          ElMessage.error('加载关联表失败')
        }
      }

      // 加载可用的关联表
      const loadAvailableRelatedTables = async () => {
        try {
          const url = base + "/tables/list?currentPage=1&pageSize=100"
          const res = await request.post(url, { pid: currentProjectInfo.value.pid })

          if (res.code === 200 && res.resdata) {
            // 为每个表加载字段信息
            const tablesWithFields = []
            for (let table of res.resdata) {
              try {
                const fieldsUrl = base + "/mores/list?currentPage=1&pageSize=100"
                const fieldsRes = await request.post(fieldsUrl, { tid: table.tid })

                if (fieldsRes.code === 200 && fieldsRes.resdata) {
                  table.fields = fieldsRes.resdata.map(field => ({
                    englishName: field.moname,
                    chineseName: field.mozname
                  }))
                } else {
                  table.fields = []
                }
              } catch (error) {
                console.warn('加载表字段失败:', table.tname, error)
                table.fields = []
              }

              // 获取表的第二个字段作为显示名称，如果第二个字段是密码则使用第三个字段
              let displayName = table.tword || table.tname
              if (table.fields && table.fields.length > 1) {
                const secondField = table.fields[1]
                if (secondField && secondField.chineseName && !secondField.chineseName.includes('密码')) {
                  displayName = secondField.chineseName
                } else if (table.fields.length > 2) {
                  const thirdField = table.fields[2]
                  if (thirdField && thirdField.chineseName) {
                    displayName = thirdField.chineseName
                  }
                }
              }

              tablesWithFields.push({
                primaryKey: table.fields && table.fields.length > 0 ? table.fields[0].englishName : 'id',
                displayName: displayName,
                tableName: table.tname,
                linkValue: '1',
                selected: false
              })
            }

            availableRelatedTables.value = tablesWithFields
          }
        } catch (error) {
          console.error('加载关联表失败:', error)
          throw error
        }
      }

      // 关闭关联表选择弹窗
      const closeRelatedTableDialog = () => {
        showRelatedTableDialog.value = false
        availableRelatedTables.value = []
      }

      // 确认关联表选择
      const confirmRelatedTableSelection = () => {
        const selectedTables = availableRelatedTables.value.filter(table => table.selected)
        if (selectedTables.length === 0) {
          ElMessage.warning('请至少选择一个关联表')
          return
        }

        // 构建关联表字符串，格式：doro,dbid,dormitory,1
        const relatedTableStr = selectedTables.map(table =>
          `${table.primaryKey},${table.displayName},${table.tableName},${table.linkValue}`
        ).join(';')

        if (currentFieldSettings.value) {
          currentFieldSettings.value.relatedTable = relatedTableStr
        }

        closeRelatedTableDialog()
        ElMessage.success('关联表设置成功')
      }

      // 保存表设计
      const saveTableDesign = async () => {
        // 验证必填字段
        if (!currentTableDesign.value.chineseName.trim()) {
          ElMessage.warning('请输入表的中文名称')
          return
        }

        if (!currentTableDesign.value.englishName.trim()) {
          ElMessage.warning('请输入表的英文名称')
          return
        }

        // 验证字段
        for (let field of currentTableDesign.value.fields) {
          if (!field.chineseName.trim() || !field.englishName.trim()) {
            ElMessage.warning('请完善所有字段的中文名称和英文名称')
            return
          }
        }

        // 确保有项目ID
        if (!currentProjectInfo.value || !currentProjectInfo.value.pid) {
          ElMessage.error('项目信息缺失，请重新配置项目')
          return
        }

        try {
          // 准备表数据
          const tableData = {
            pid: currentProjectInfo.value.pid,
            tword: currentTableDesign.value.chineseName,
            tname: currentTableDesign.value.englishName,
            tgn: JSON.stringify(currentTableDesign.value.functions),
            tlist: '',
            vlist: '',
            by1: (currentTableDesign.value.generateDataCount || 0).toString(),
            by2: (currentTableDesign.value.menuOrder || 1).toString()
          }

          // 如果是编辑模式，添加tid
          if (currentTableDesign.value.id) {
            tableData.tid = currentTableDesign.value.id
          }

          // 保存表信息
          const isUpdate = currentTableDesign.value.id
          const url = base + (isUpdate ? "/tables/update" : "/tables/add")
          const res = await request.post(url, tableData)

          if (res.code === 200) {
            let tableId = currentTableDesign.value.id
            if (!isUpdate && res.resdata) {
              // 后端返回的是新创建的表ID
              tableId = res.resdata
              currentTableDesign.value.id = tableId
            }

            // 如果有字段，保存字段信息
            if (currentTableDesign.value.fields.length > 0) {
              // 先删除原有字段（如果是更新模式）
              if (isUpdate) {
                try {
                  await request.post(base + "/mores/deleteByTid", { tid: tableId })
                } catch (error) {
                  console.warn('删除原有字段失败:', error)
                }
              }

              // 保存新字段
              for (let field of currentTableDesign.value.fields) {
                const fieldData = {
                  tid: tableId,
                  moname: field.englishName,
                  mozname: field.chineseName,
                  motype: field.type,
                  moflag: field.controlType,
                  molong: '',
                  moyz: field.required ? '1' : '0',
                  mobt: field.searchable ? '1' : '0',
                  by1: field.visible ? '1' : '0',
                  by2: field.existsCheck ? '1' : '0',
                  by3: field.relatedTable || '',
                  by4: field.customOptions || '',
                  by5: '',
                  by6: ''
                }

                try {
                  await request.post(base + "/mores/add", fieldData)
                } catch (error) {
                  console.error('保存字段失败:', field, error)
                }
              }
            }

            ElMessage.success('表设计保存成功')
            closeTableDesignModal()
            // 重新加载项目表单列表
            if (currentProjectInfo.value.pid) {
              loadProjectTables(currentProjectInfo.value.pid)
            }
          } else {
            ElMessage.error(res.msg || '表保存失败')
          }
        } catch (error) {
          console.error('保存表设计失败:', error)
          ElMessage.error('保存表设计失败，请检查网络连接')
        }
      }

      // 删除表
      const deleteTable = async (table) => {
        try {
          await ElMessageBox.confirm(
            `确定要删除表 "${table.tword || table.tname}" 吗？`,
            '确认删除',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            }
          )

          const url = base + "/tables/del?id=" + table.tid
          const res = await request.post(url, {})

          if (res.code === 200) {
            ElMessage.success('删除成功')
            // 重新加载表单列表
            if (currentProjectInfo.value && currentProjectInfo.value.pid) {
              loadProjectTables(currentProjectInfo.value.pid)
            }
          } else {
            ElMessage.error(res.msg || '删除失败')
          }
        } catch (error) {
          if (error !== 'cancel') {
            console.error('删除表失败:', error)
            ElMessage.error('删除失败，请检查网络连接')
          }
        }
      }

      // 下一步操作
      const nextStep = async () => {
        if (activeTab.value === 'project') {
          // 从项目配置到表单设计
          if (!selectedProject.value) {
            ElMessage.warning('请先选择项目类型')
            return
          }
          if (!projectForm.name) {
            ElMessage.warning('请输入项目中文名称')
            return
          }

          // 验证必填字段
          if (projectForm.databaseMode === 'new') {
            if (!projectForm.projectCode) {
              ElMessage.warning('请输入项目编号')
              return
            }
            if (!projectForm.databaseName) {
              ElMessage.warning('请输入数据库名称')
              return
            }
          } else if (projectForm.databaseMode === 'existing') {
            if (!projectForm.selectedDatabase) {
              ElMessage.warning('请选择已有数据库')
              return
            }
          }

          // 设置当前项目信息
          currentProjectInfo.value = {
            name: projectForm.name,
            projectCode: projectForm.projectCode,
            databaseName: projectForm.databaseName,
            pid: projectForm.selectedDatabase || null
          }

          // 保存或更新项目到数据库
          const result = await saveOrUpdateProject()
          if (!result.success) return

          // 更新项目ID
          if (result.projectId) {
            currentProjectInfo.value.pid = result.projectId
          }

          // 如果是已有数据库模式，加载项目表单
          if (projectForm.databaseMode === 'existing' && projectForm.selectedDatabase) {
            loadProjectTables(projectForm.selectedDatabase)
          } else if (currentProjectInfo.value.pid) {
            // 新建项目也加载表单（可能为空）
            loadProjectTables(currentProjectInfo.value.pid)
          }

          activeTab.value = 'form'
          ElMessage.success('项目配置保存成功，请继续设计表单')
        } else if (activeTab.value === 'form') {
          // 从表单设计到项目生成
          if (!projectTables.value || projectTables.value.length === 0) {
            ElMessage.warning('请先设计数据表，不能为空')
            return
          }
          activeTab.value = 'generate'
          ElMessage.success('表单设计完成，请生成项目')
        }
      }

      const handleTabClick = (tab) => {
        console.log('切换到标签页:', tab.props.name)
      }

      // 组件挂载时检查登录状态
      onMounted(() => {
        checkLoginStatus()
      })

      return {
        activeTab,
        logLevel,
        isLoggedIn,
        loginDialogVisible,
        loginLoading,
        loginFormRef,
        userInfo,
        loginForm,
        loginRules,
        selectedProject,
        projectsLoading,
        availableDatabases,
        projectForm,
        currentProjectInfo,
        projectTables,
        projectTablesLoading,
        showTableDesignModal,
        currentTableTab,
        currentTableDesign,
        formFields,
        tableData,
        sqlContent,
        logs,
        handleTabClick,
        handleLogin,
        handleUserCommand,
        handleLogout,
        selectProject,
        getProjectName,
        openTemplateModal,
        openFrontTemplateModal,
        handleDatabaseModeChange,
        handleProjectSelect,
        loadProjectTables,
        openTableDesignModal,
        closeTableDesignModal,
        switchTableTab,
        addNewFieldToDesign,
        clearAllFields,
        deleteFieldFromDesign,
        moveFieldUp,
        moveFieldDown,
        saveTableDesign,
        deleteTable,
        nextStep,
        getTableFieldCount,
        getTableFunctions,
        backendFunctions,
        frontendFunctions,
        miniFunctions,
        resetFields,
        showAiGeneratorModal,

        // AI生成器相关
        showAiGeneratorDialog,
        aiGeneratorInput,
        aiGenerationInProgress,
        hideAiGeneratorModal,
        confirmAiGeneration,
        doubaoGenerate,
        createFormFromAI,
        inferControlType,

        // 模板选择相关
        showBackendTemplateDialog,
        showFrontendTemplateDialog,
        showTemplateDetailDialog,
        backendTemplates,
        frontendTemplates,
        templatesLoading,
        currentTemplateDetail,
        showBackendTemplateSelector,
        showFrontendTemplateSelector,
        selectBackendTemplate,
        selectFrontendTemplate,
        viewTemplateDetail,
        getTemplateImageUrl,
        handleImageError,

        // 项目生成相关
        generationInProgress,
        generationResult,
        canGenerate,
        generateProject,
        downloadProject,

        // SQL脚本生成相关
        sqlGenerationInProgress,
        generateSqlScript,
        exportSqlScript,
        copySqlScript,

        // 数据脚本生成相关
        dataGenerationInProgress,
        dataContent,
        generateDataScript,
        copyDataScript,

        // 字段设置相关
        showFieldSettingsDialog,
        currentFieldSettings,
        showInSearchList,
        editFieldSettings,
        closeFieldSettingsDialog,
        saveFieldSettings,
        showRelatedTableSelector,

        // 关联表选择相关
        showRelatedTableDialog,
        availableRelatedTables,
        closeRelatedTableDialog,
        confirmRelatedTableSelection
      }
    }
  }
</script>