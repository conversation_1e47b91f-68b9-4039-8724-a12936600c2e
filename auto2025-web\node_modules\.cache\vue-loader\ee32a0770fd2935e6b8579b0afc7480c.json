{"remainingRequest": "J:\\auto2025\\auto2025-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!J:\\auto2025\\auto2025-web\\src\\views\\admin\\small\\SmallEdit.vue?vue&type=script&lang=js", "dependencies": [{"path": "J:\\auto2025\\auto2025-web\\src\\views\\admin\\small\\SmallEdit.vue", "mtime": 1749439593062}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748675476717}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748675484569}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748675476717}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1748675485115}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["J:\\auto2025\\auto2025-web\\src\\views\\admin\\small\\SmallEdit.vue"], "names": [], "mappings": ";EAiHE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,EAAE,CAAC,CAAC;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UAChE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAC/D,CAAC;QACH,CAAC;;MAEH,CAAC;IACH,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC;;;IAGD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;MAEP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;QACX,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEzC,CAAC,CAAC;MACJ,CAAC;;MAED,CAAC,EAAE,CAAC;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACT,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;YAE3F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACpD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;gBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACb,CAAC,CAAC;gBACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtB,CAAC,CAAC;cACJ,EAAE,CAAC,CAAC,CAAC,EAAE;gBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACb,CAAC,CAAC;cACJ;cACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC;UACJ;;QAEF,CAAC,CAAC;MACJ,CAAC;;MAED,CAAC,EAAE,CAAC;MACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC;MACJ,CAAC;;;MAGD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC;MACJ,CAAC;;;MAGD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MAC3B,CAAC;;IAEH,CAAC;EACH", "file": "J:/auto2025/auto2025-web/src/views/admin/small/SmallEdit.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n    <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\" align=\"left\">\r\n      <el-form-item label=\"大类\" prop=\"bid\">\r\n        <el-select v-model=\"formData.bid\" placeholder=\"请选择\" size=\"small\">\r\n          <el-option v-for=\"item in bigList\" :key=\"item.bid\" :label=\"item.bname\" :value=\"item.bid\"></el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"小类名称\" prop=\"sname\">\r\n        <el-input v-model=\"formData.sname\" placeholder=\"小类名称\" style=\"width:50%;\"></el-input>\r\n      </el-form-item>\r\n\r\n      <el-tabs v-model=\"activeTab\">\r\n        <el-tab-pane label=\"内容1\" name=\"memo1\">\r\n          <el-form-item label=\"内容1\" prop=\"memo1\">\r\n            <el-input type=\"textarea\" :rows=\"25\" v-model=\"formData.memo1\" placeholder=\"内容1\" size=\"small\"></el-input>\r\n          </el-form-item>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"内容2\" name=\"memo2\">\r\n          <el-form-item label=\"内容2\" prop=\"memo2\">\r\n            <el-input type=\"textarea\" :rows=\"25\" v-model=\"formData.memo2\" placeholder=\"内容2\" size=\"small\"></el-input>\r\n          </el-form-item>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"内容3\" name=\"memo3\">\r\n          <el-form-item label=\"内容3\" prop=\"memo3\">\r\n            <el-input type=\"textarea\" :rows=\"25\" v-model=\"formData.memo3\" placeholder=\"内容3\" size=\"small\"></el-input>\r\n          </el-form-item>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"内容4\" name=\"memo4\">\r\n          <el-form-item label=\"内容4\" prop=\"memo4\">\r\n            <WangEditor ref=\"wangEditorRef\" v-model=\"formData.memo4\" :config=\"editorConfig\" :isClear=\"isClear\"\r\n              @change=\"editorChange\"></WangEditor>\r\n          </el-form-item>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"备用1\" name=\"by1\">\r\n          <el-form-item label=\"备用1\" prop=\"by1\">\r\n            <el-input type=\"textarea\" :rows=\"25\" v-model=\"formData.by1\" placeholder=\"备用1\" size=\"small\"></el-input>\r\n          </el-form-item>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"备用2\" name=\"by2\">\r\n          <el-form-item label=\"备用2\" prop=\"by2\">\r\n            <el-input type=\"textarea\" :rows=\"25\" v-model=\"formData.by2\" placeholder=\"备用2\" size=\"small\"></el-input>\r\n          </el-form-item>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"备用3\" name=\"by3\">\r\n          <el-form-item label=\"备用3\" prop=\"by3\">\r\n            <el-input type=\"textarea\" :rows=\"25\" v-model=\"formData.by3\" placeholder=\"备用3\" size=\"small\"></el-input>\r\n          </el-form-item>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"备用4\" name=\"by4\">\r\n          <el-form-item label=\"备用4\" prop=\"by4\">\r\n            <el-input type=\"textarea\" :rows=\"25\" v-model=\"formData.by4\" placeholder=\"备用4\" size=\"small\"></el-input>\r\n          </el-form-item>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"备用5\" name=\"by5\">\r\n          <el-form-item label=\"备用5\" prop=\"by5\">\r\n            <el-input type=\"textarea\" :rows=\"25\" v-model=\"formData.by5\" placeholder=\"备用5\" size=\"small\"></el-input>\r\n          </el-form-item>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"备用6\" name=\"by6\">\r\n          <el-form-item label=\"备用6\" prop=\"by6\">\r\n            <el-input type=\"textarea\" :rows=\"25\" v-model=\"formData.by6\" placeholder=\"备用6\" size=\"small\"></el-input>\r\n          </el-form-item>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"备用7\" name=\"by7\">\r\n          <el-form-item label=\"备用7\" prop=\"by7\">\r\n            <el-input type=\"textarea\" :rows=\"25\" v-model=\"formData.by7\" placeholder=\"备用7\" size=\"small\"></el-input>\r\n          </el-form-item>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"备用8\" name=\"by8\">\r\n          <el-form-item label=\"备用8\" prop=\"by8\">\r\n            <el-input type=\"textarea\" :rows=\"25\" v-model=\"formData.by8\" placeholder=\"备用8\" size=\"small\"></el-input>\r\n          </el-form-item>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"备用9\" name=\"by9\">\r\n          <el-form-item label=\"备用9\" prop=\"by9\">\r\n            <el-input type=\"textarea\" :rows=\"25\" v-model=\"formData.by9\" placeholder=\"备用9\" size=\"small\"></el-input>\r\n          </el-form-item>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"备用10\" name=\"by10\">\r\n          <el-form-item label=\"备用10\" prop=\"by10\">\r\n            <el-input type=\"textarea\" :rows=\"25\" v-model=\"formData.by10\" placeholder=\"备用10\" size=\"small\"></el-input>\r\n          </el-form-item>\r\n        </el-tab-pane>\r\n\r\n\r\n\r\n      </el-tabs>\r\n\r\n      <el-form-item>\r\n        <el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\r\n        <el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import request, { base } from \"../../../../utils/http\";\r\n  import WangEditor from \"../../../components/WangEditor\";\r\n  export default {\r\n    name: 'SmallEdit',\r\n    components: {\r\n      WangEditor,\r\n    },\r\n    data() {\r\n      return {\r\n        id: '',\r\n        activeTab: 'memo1', // 默认激活第一个标签页\r\n        isClear: false,\r\n        uploadVisible: false,\r\n        btnLoading: false, //保存按钮加载状态     \r\n        formData: {}, //表单数据           \r\n        addrules: {\r\n          bid: [{ required: true, message: '请选择大类', trigger: 'onchange' }],\r\n          sname: [{ required: true, message: '请输入小类名称', trigger: 'blur' },\r\n          ],\r\n        },\r\n\r\n      };\r\n    },\r\n    created() {\r\n      this.id = this.$route.query.id;\r\n      this.getDatas();\r\n      this.getbigList();\r\n    },\r\n\r\n\r\n    methods: {\r\n\r\n      //获取列表数据\r\n      getDatas() {\r\n        let para = {\r\n        };\r\n        this.listLoading = true;\r\n        let url = base + \"/small/get?id=\" + this.id;\r\n        request.post(url, para).then((res) => {\r\n          this.formData = JSON.parse(JSON.stringify(res.resdata));\r\n          this.listLoading = false;\r\n          this.$refs[\"wangEditorRef\"].editor.txt.html(this.formData.memo4);\r\n          this.bid = this.formData.bid;\r\n          this.formData.bid = this.formData.bname;\r\n\r\n        });\r\n      },\r\n\r\n      // 添加\r\n      save() {\r\n        this.$refs[\"formDataRef\"].validate((valid) => { //验证表单\r\n          if (valid) {\r\n            let url = base + \"/small/update\";\r\n            this.btnLoading = true;\r\n            this.formData.bid = this.formData.bid == this.formData.bname ? this.bid : this.formData.bid;\r\n\r\n            request.post(url, this.formData).then((res) => { //发送请求         \r\n              if (res.code == 200) {\r\n                this.$message({\r\n                  message: \"操作成功\",\r\n                  type: \"success\",\r\n                  offset: 320,\r\n                });\r\n                this.$router.push({\r\n                  path: \"/SmallManage\",\r\n                });\r\n              } else {\r\n                this.$message({\r\n                  message: res.msg,\r\n                  type: \"error\",\r\n                  offset: 320,\r\n                });\r\n              }\r\n              this.btnLoading = false;\r\n            });\r\n          }\r\n\r\n        });\r\n      },\r\n\r\n      // 返回\r\n      goBack() {\r\n        this.$router.push({\r\n          path: \"/SmallManage\",\r\n        });\r\n      },\r\n\r\n\r\n      getbigList() {\r\n        let para = {};\r\n        this.listLoading = true;\r\n        let url = base + \"/big/list?currentPage=1&pageSize=1000\";\r\n        request.post(url, para).then((res) => {\r\n          this.bigList = res.resdata;\r\n        });\r\n      },\r\n\r\n\r\n      // 富文本编辑器\r\n      editorChange(val) {\r\n        this.formData.memo4 = val;\r\n      },\r\n\r\n    },\r\n  }\r\n\r\n</script>\r\n<style scoped>\r\n</style>"]}]}