package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mapper.ProductsMapper;
import com.model.Products;
import com.service.ProductsService;
import com.util.PageBean;

/**
 * 产品业务逻辑服务实现类
 *
 * 功能说明：
 * 1. 实现产品相关的业务逻辑
 * 2. 提供完整的CRUD操作实现
 * 3. 处理数据查询和分页逻辑
 * 4. 调用Mapper层进行数据库操作
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024
 */
@Service
public class ProductsServiceImpl implements ProductsService {

	/**
	 * 产品数据访问层
	 * 通过Spring自动注入
	 */
	@Autowired
	private ProductsMapper productsMapper;

	// 查询多条记录
	public List<Products> queryProductsList(Products products, PageBean page) throws Exception {
		Map<String, Object> map = getQueryMap(products, page);

		List<Products> getProducts = productsMapper.query(map);

		return getProducts;
	}

	// 得到记录总数
	@Override
	public int getCount(Products products) {
		Map<String, Object> map = getQueryMap(products, null);
		int count = productsMapper.getCount(map);
		return count;
	}

	private Map<String, Object> getQueryMap(Products products, PageBean page) {
		Map<String, Object> map = new HashMap<String, Object>();
		if (products != null) {
						map.put("pid", products.getPid());
			map.put("pname", products.getPname());
			map.put("cid", products.getCid());
			map.put("photo", products.getPhoto());
			map.put("price", products.getPrice());
			map.put("quantity", products.getQuantity());
			map.put("pmemo", products.getPmemo());
			map.put("sort", products.getSort());
			map.put("condition", products.getCondition());

		}
		PageBean.setPageMap(map, page);
		return map;
	}

	// 添加
	public int insertProducts(Products products) throws Exception {
		return productsMapper.insertProducts(products);
	}

	/**
	 * 根据ID删除产品
	 * @param id 主键ID
	 * @return 影响的行数
	 * @throws Exception 异常
	 */
	public int deleteProducts(Integer id) throws Exception {
		return productsMapper.deleteProducts(id);
	}

	/**
	 * 更新产品
	 * @param products 产品实体对象
	 * @return 影响的行数
	 * @throws Exception 异常
	 */
	public int updateProducts(Products products) throws Exception {
		return productsMapper.updateProducts(products);
	}

	/**
	 * 根据ID查询产品详情
	 * @param id 主键ID
	 * @return 产品实体对象，如果不存在则返回null
	 * @throws Exception 异常
	 */
	public Products queryProductsById(Integer id) throws Exception {
		Products po = productsMapper.queryProductsById(id);
		return po;
	}
}
