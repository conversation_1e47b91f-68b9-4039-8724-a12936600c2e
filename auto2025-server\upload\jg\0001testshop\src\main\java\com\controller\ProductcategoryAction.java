package com.controller;

import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import com.response.Response;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.model.*;
import com.service.*;
import com.util.*;

/**
 * 产品分类控制器类
 *
 * 功能说明：
 * 1. 处理产品分类相关的HTTP请求
 * 2. 提供完整的CRUD操作接口
 * 3. 支持分页查询和条件查询
 * 4. 返回对应的视图页面或JSON数据
 */
@Controller
public class ProductcategoryAction{

	@Autowired private ProductcategoryService productcategoryService;

	// 查询所有产品分类
	@RequestMapping(value = "/productcategoryList")
	public String productcategoryList(Productcategory ser,HttpServletRequest req)throws Exception
	{

		int offset = 0; // 记录偏移量
		int counts = 0; // 总记录数
		try {
			offset = Integer.parseInt(req.getParameter("pager.offset"));
		} catch (Exception e) {
		}
		int pageSize = 10; // 每页显示条记录数
		PageBean page = new PageBean(offset); // 实例化分页类
		page.setPageSize(pageSize); // 设置每页显示记录数

		counts = productcategoryService.getCount(ser);
		List<Productcategory> productcategoryList = productcategoryService.queryProductcategoryList(ser, page); // 查询所有产品分类

		req.setAttribute("list", productcategoryList);

		/** 分页代码开始 **/
		req.setAttribute("itemSize", counts); // 总记录数
		int page_count = (counts + pageSize - 1) / pageSize; // 计算总页数
		req.setAttribute("pageItem", pageSize); // 每页记录数
		req.setAttribute("pageTotal", page_count); // 总页数
		req.setAttribute("currentPageNumber", (offset / pageSize) + 1); // 当前页码
		req.setAttribute("pageUrl", "productcategoryList"); // 跳转路径
		/** 分页代码 结束 **/
		return "/admin/productcategory/productcategory_Manage";
	}

	// 查询所有产品分类（列表页面）
	@RequestMapping(value = "/productcategoryList2")
	public String productcategoryList2(Productcategory ser,HttpServletRequest req)throws Exception
	{

		int offset = 0; // 记录偏移量
		int counts = 0; // 总记录数
		try {
			offset = Integer.parseInt(req.getParameter("pager.offset"));
		} catch (Exception e) {
		}
		int pageSize = 10; // 每页显示条记录数
		PageBean page = new PageBean(offset); // 实例化分页类
		page.setPageSize(pageSize); // 设置每页显示记录数

		counts = productcategoryService.getCount(ser);
		List<Productcategory> productcategoryList = productcategoryService.queryProductcategoryList(ser, page); // 查询所有产品分类

		req.setAttribute("list", productcategoryList);

		/** 分页代码开始 **/
		req.setAttribute("itemSize", counts); // 总记录数
		int page_count = (counts + pageSize - 1) / pageSize; // 计算总页数
		req.setAttribute("pageItem", pageSize); // 每页记录数
		req.setAttribute("pageTotal", page_count); // 总页数
		req.setAttribute("currentPageNumber", (offset / pageSize) + 1); // 当前页码
		req.setAttribute("pageUrl", "productcategoryList2"); // 跳转路径
		/** 分页代码 结束 **/
		return "/admin/productcategory/productcategory_Manage2";
	}

	// 跳转到产品分类添加页面
	@RequestMapping(value = "/productcategoryToAdd")
	public String productcategoryToAdd(HttpServletRequest req) throws Exception {

		return "/admin/productcategory/productcategory_Add";
	}

	// 添加产品分类
	@RequestMapping(value = "/productcategoryAdd")
	@ResponseBody
	public Response productcategoryAdd(Productcategory productcategory, HttpServletRequest req) throws Exception {
		Productcategory checkProductcategory1 = new Productcategory();
		checkProductcategory1.setCname(productcategory.getCname());

		List<Productcategory> checkList1 = productcategoryService.queryProductcategoryList(checkProductcategory1, null);
		if (checkList1 != null && checkList1.size() > 0) {
			return Response.error(201, "该分类名称已存在，请重新输入!");
		} else {
			productcategoryService.insertProductcategory(productcategory); // 添加产品分类
		}
		return Response.success();
	}

	// 删除产品分类
	@RequestMapping(value = "/productcategoryDel")
	public String productcategoryDel(HttpServletRequest req) throws Exception {
		int id = Integer.parseInt(req.getParameter("id"));
		productcategoryService.deleteProductcategory(id); // 删除产品分类
		req.setAttribute("message", "操作成功");
		req.setAttribute("path", "productcategoryList");
		return "common/succeed";
	}

	// 跳转到产品分类修改页面
	@RequestMapping(value = "/productcategoryToEdit")
	public String productcategoryToEdit(HttpServletRequest req) throws Exception {
		int id = Integer.parseInt(req.getParameter("id"));
		Productcategory productcategory = productcategoryService.queryProductcategoryById(id); // 根据ID查询产品分类详情
		req.setAttribute("item", productcategory);

		return "/admin/productcategory/productcategory_Edit";
	}

	// 跳转到产品分类详情页面
	@RequestMapping(value = "/productcategoryToDetail")
	public String productcategoryToDetail(HttpServletRequest req) throws Exception {
		int id = Integer.parseInt(req.getParameter("id"));
		Productcategory productcategory = productcategoryService.queryProductcategoryById(id); // 根据ID查询产品分类详情
		req.setAttribute("item", productcategory); // 设置产品分类对象到请求属性中
		return "/admin/productcategory/productcategory_Detail";
	}

	// 修改产品分类
	@RequestMapping(value = "/productcategoryEdit")
	@ResponseBody
	public Response productcategoryEdit(Productcategory productcategory, HttpServletRequest req) throws Exception {
		productcategoryService.updateProductcategory(productcategory); // 更新产品分类
		return Response.success();
	}



}
