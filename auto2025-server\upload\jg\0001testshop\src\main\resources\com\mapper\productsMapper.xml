<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!--
    产品数据访问层映射文件

    功能说明：
    1. 提供产品的数据库操作SQL映射
    2. 支持动态条件查询和分页查询
    3. 包含完整的CRUD操作

    对应表：products
    对应实体：com.model.Products
-->
<mapper namespace="com.mapper.ProductsMapper">

	<!-- ==================== 查询操作 ==================== -->

	<!-- 查询所有产品记录 -->
	<select id="findProductsList" resultType="com.model.Products">
        select * from products
        order by pid desc
	</select>

	<!-- 动态条件查询产品记录 -->
	<select id="query" parameterType="java.util.Map" resultType="com.model.Products">
        select *
        from products a
		<where>
			<!-- 主键查询条件 -->
			<if test="id != null and id != 0">
                and a.pid = #{id}
			</if>
			<!-- 动态字段查询条件 -->
            		<if test="pid != null and pid != 0">
		    and a.pid = #{pid}
		</if>
		<if test="pname != null and pname != ''">
		    and a.pname = #{pname}
		</if>
		<if test="cid != null and cid != 0">
		    and a.cid = #{cid}
		</if>
		<if test="photo != null and photo != ''">
		    and a.photo = #{photo}
		</if>
		<if test="price != null and price != 0">
		    and a.price = #{price}
		</if>
		<if test="quantity != null and quantity != 0">
		    and a.quantity = #{quantity}
		</if>
		<if test="pmemo != null and pmemo != ''">
		    and a.pmemo = #{pmemo}
		</if>

			<!-- 自定义查询条件 -->
			<if test="condition != null and condition != ''">
                ${condition}
			</if>
		</where>
		<!-- 排序逻辑 -->
		<if test="sort != null and sort != ''">
            order by ${sort}
		</if>
		<if test="sort == null or sort == ''">
            order by a.pid desc
		</if>
		<!-- 分页逻辑 -->
		<if test="page != null and page != ''">
            limit #{offset}, #{pageSize}
		</if>
	</select>

	<!-- 统计符合条件的产品记录总数 -->
	<select id="getCount" parameterType="java.util.Map" resultType="Int">
        select count(0) from products a
		<where>
			<!-- 主键查询条件 -->
			<if test="id != null and id != 0">
                and a.pid = #{id}
			</if>
			<!-- 动态字段查询条件 -->
            		<if test="pid != null and pid != 0">
		    and a.pid = #{pid}
		</if>
		<if test="pname != null and pname != ''">
		    and a.pname = #{pname}
		</if>
		<if test="cid != null and cid != 0">
		    and a.cid = #{cid}
		</if>
		<if test="photo != null and photo != ''">
		    and a.photo = #{photo}
		</if>
		<if test="price != null and price != 0">
		    and a.price = #{price}
		</if>
		<if test="quantity != null and quantity != 0">
		    and a.quantity = #{quantity}
		</if>
		<if test="pmemo != null and pmemo != ''">
		    and a.pmemo = #{pmemo}
		</if>

			<!-- 自定义查询条件 -->
			<if test="condition != null and condition != ''">
                ${condition}
			</if>
		</where>
	</select>

	<!-- 根据ID查询单个产品记录 -->
	<select id="queryProductsById" parameterType="Integer" resultType="com.model.Products">
        select *
        from products a
        where a.pid = #{value}
	</select>

	<!-- ==================== 插入操作 ==================== -->

	<!-- 新增产品记录 -->
	<insert id="insertProducts" useGeneratedKeys="true" keyProperty="pid" parameterType="com.model.Products">
        insert into products
        (pid,pname,cid,photo,price,quantity,pmemo)
        values
        (#{pid},#{pname},#{cid},#{photo},#{price},#{quantity},#{pmemo})
	</insert>

	<!-- ==================== 更新操作 ==================== -->

	<!-- 更新产品记录 -->
	<update id="updateProducts" parameterType="com.model.Products">
        update products
		<set>
			<!-- 动态更新字段 -->
            		<if test="pname != null and pname != ''">
		    pname = #{pname},
		</if>
		<if test="cid != null">
		    cid = #{cid},
		</if>
		<if test="photo != null and photo != ''">
		    photo = #{photo},
		</if>
		<if test="price != null">
		    price = #{price},
		</if>
		<if test="quantity != null">
		    quantity = #{quantity},
		</if>
		<if test="pmemo != null and pmemo != ''">
		    pmemo = #{pmemo},
		</if>

		</set>
		<where>
			<!-- 自定义更新条件 -->
			<if test="condition != null and condition != ''">
                ${condition}
			</if>
			<!-- 主键更新条件 -->
			<if test="pid != null">
				pid = #{pid}
			</if>
		</where>
	</update>

	<!-- ==================== 删除操作 ==================== -->

	<!-- 根据ID删除产品记录 -->
	<delete id="deleteProducts" parameterType="Integer">
        delete from products
        where pid = #{value}
	</delete>

</mapper>
