<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
<head th:replace="@{/admin/head.html}"></head>
  
</head>

<body>
    <div class="content-area" style="height: 100%; padding: 20px;">
        <h4 class="page-title">修改个人信息</h4>

        <div>
        <div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">修改个人信息</h5>
        </div>
        <div class="card-body">
            <form id="userinfoInfoForm" method="post">
                <div class="form-group row mb-3">
                    <label for="uname" class="col-sm-3 col-form-label">姓名</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="uname" name="uname" th:value="${item.uname}">
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label for="gender" class="col-sm-3 col-form-label">性别</label>
                    <div class="col-sm-9">
                        <div class="form-check-inline">
                            <input type="radio" name="gender" value="男" checked="checked" th:checked="${item.gender == '男'}" /> 男
                        </div>
                        <div class="form-check-inline">
                            <input type="radio" name="gender" value="女" th:checked="${item.gender == '女'}" /> 女
                        </div>
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label for="phone" class="col-sm-3 col-form-label">手机号码</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="phone" name="phone" placeholder="请输入手机号码" th:value="${item.phone}">
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label for="email" class="col-sm-3 col-form-label">电子邮箱</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="email" name="email" placeholder="请输入邮箱地址" th:value="${item.email}">
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label for="photo" class="col-sm-3 col-form-label">照片</label>
                    <div class="col-sm-9">
                        <div class="image-upload-container">
                            <input type="file" class="form-control" id="photo_file" accept="image/*" onchange="uploadImage(this, 'photo')">
                            <input type="hidden" id="photo_hidden" name="photo" th:value="${item.photo}">
                            <div class="current-image mt-2" th:if="${item.photo}">
                                <small class="text-muted">当前图片: </small><br>
                                <img th:src="@{'/upload/' + ${item.photo}}" alt="当前图片" style="max-width: 150px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px; padding: 5px;">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-9 offset-sm-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-floppy-fill"></i> 保存
                        </button>
                        <a href="javascript:history.back()" class="btn btn-secondary ml-2">
                            <i class="bi bi-arrow-left"></i> 返回
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
        <script>
        // 文件上传函数
        function uploadFile(input, fieldName) {
            if (input.files && input.files[0]) {
                var formData = new FormData();
                formData.append('file', input.files[0]);
                formData.append('c', fieldName);
                
                $.ajax({
                    url: '/upload_re',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            // 将服务器返回的文件名设置到隐藏字段中
                            $('#' + fieldName + '_hidden').val(response.fileName);
                            CommonUtils.showToast('文件上传成功', 'success');
                        } else {
                            CommonUtils.showToast(response.message || '文件上传失败', 'error');
                        }
                    },
                    error: function() {
                        CommonUtils.showToast('文件上传失败', 'error');
                    }
                });
            }
        }
        
        // 图片上传函数
        function uploadImage(input, fieldName) {
            if (input.files && input.files[0]) {
                var formData = new FormData();
                formData.append('file', input.files[0]);
                formData.append('c', fieldName);
                
                $.ajax({
                    url: '/upload_re',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            // 将服务器返回的文件名设置到隐藏字段中
                            $('#' + fieldName + '_hidden').val(response.fileName);
                            CommonUtils.showToast('图片上传成功', 'success');
                            
                            // 显示预览图片
                            var previewContainer = $('#' + fieldName + '_file').closest('.image-upload-container').find('.current-image');
                            if (previewContainer.length === 0) {
                                $('#' + fieldName + '_file').closest('.image-upload-container').append('<div class="current-image mt-2"><small class="text-muted">当前图片: </small><br><img src="/upload/' + response.fileName + '" alt="预览图片" style="max-width: 150px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px; padding: 5px;"></div>');
                            } else {
                                previewContainer.find('img').attr('src', '/upload/' + response.fileName);
                            }
                        } else {
                            CommonUtils.showToast(response.message || '图片上传失败', 'error');
                        }
                    },
                    error: function() {
                        CommonUtils.showToast('图片上传失败', 'error');
                    }
                });
            }
        }
        
        // 图片预览函数
        function previewImage(src) {
            var modal = '<div class="modal fade" id="imagePreviewModal" tabindex="-1">' +
                       '<div class="modal-dialog modal-lg modal-dialog-centered">' +
                       '<div class="modal-content">' +
                       '<div class="modal-header">' +
                       '<h5 class="modal-title">图片预览</h5>' +
                       '<button type="button" class="btn-close" data-bs-dismiss="modal"></button>' +
                       '</div>' +
                       '<div class="modal-body text-center">' +
                       '<img src="' + src + '" class="img-fluid" style="max-height: 70vh;">' +
                       '</div>' +
                       '</div></div></div>';
            
            $('body').append(modal);
            var modalInstance = new bootstrap.Modal(document.getElementById('imagePreviewModal'));
            modalInstance.show();
            
            $('#imagePreviewModal').on('hidden.bs.modal', function() {
                $(this).remove();
            });
        }
        
        $(document).ready(function() {
            $('#userinfoForm').on('submit', function(e) {
                e.preventDefault();
                
                // 获取表单字段值
                var uname = $('#uname').val().trim();
                var gender = $('#gender').val().trim();
                var phone = $('#phone').val().trim();
                var email = $('#email').val().trim();
                
                // 非空验证
                if (!uname) {
                    CommonUtils.showToast('请输入姓名', 'error');
                    return;
                }
                if (!gender) {
                    CommonUtils.showToast('请输入性别', 'error');
                    return;
                }
                if (!phone) {
                    CommonUtils.showToast('请输入手机号码', 'error');
                    return;
                }
                
                // 邮箱格式验证
                if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                    CommonUtils.showToast('电子邮箱格式不正确', 'error');
                    return;
                }
                
                // 手机号格式验证
                if (phone && !/^1[3-9]\d{9}$/.test(phone)) {
                    CommonUtils.showToast('手机号码格式不正确，请输入11位手机号', 'error');
                    return;
                }
                
                
                // 使用通用表单提交方法
                CommonUtils.submitForm('#userinfoForm', 
                    '/userinfoEdit', 
                    function(response) {
                        // 成功回调
                        CommonUtils.showToast('操作成功！', 'success');
                        setTimeout(function() {
                            window.location.href = '/userinfoList';
                        }, 1500);
                    },
                    function(error) {
                        // 错误回调
                        console.error('提交失败:', error);
                    }
                );
            });
        });
        </script>
        </div>
    </div>

    <!-- Bootstrap JS -->
</body>

</html>