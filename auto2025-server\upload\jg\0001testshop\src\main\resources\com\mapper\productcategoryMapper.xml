<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!--
    产品分类数据访问层映射文件

    功能说明：
    1. 提供产品分类的数据库操作SQL映射
    2. 支持动态条件查询和分页查询
    3. 包含完整的CRUD操作

    对应表：productcategory
    对应实体：com.model.Productcategory
-->
<mapper namespace="com.mapper.ProductcategoryMapper">

	<!-- ==================== 查询操作 ==================== -->

	<!-- 查询所有产品分类记录 -->
	<select id="findProductcategoryList" resultType="com.model.Productcategory">
        select * from productcategory
        order by cid desc
	</select>

	<!-- 动态条件查询产品分类记录 -->
	<select id="query" parameterType="java.util.Map" resultType="com.model.Productcategory">
        select *
        from productcategory a
		<where>
			<!-- 主键查询条件 -->
			<if test="id != null and id != 0">
                and a.cid = #{id}
			</if>
			<!-- 动态字段查询条件 -->
            		<if test="cid != null and cid != 0">
		    and a.cid = #{cid}
		</if>
		<if test="cname != null and cname != ''">
		    and a.cname = #{cname}
		</if>

			<!-- 自定义查询条件 -->
			<if test="condition != null and condition != ''">
                ${condition}
			</if>
		</where>
		<!-- 排序逻辑 -->
		<if test="sort != null and sort != ''">
            order by ${sort}
		</if>
		<if test="sort == null or sort == ''">
            order by a.cid desc
		</if>
		<!-- 分页逻辑 -->
		<if test="page != null and page != ''">
            limit #{offset}, #{pageSize}
		</if>
	</select>

	<!-- 统计符合条件的产品分类记录总数 -->
	<select id="getCount" parameterType="java.util.Map" resultType="Int">
        select count(0) from productcategory a
		<where>
			<!-- 主键查询条件 -->
			<if test="id != null and id != 0">
                and a.cid = #{id}
			</if>
			<!-- 动态字段查询条件 -->
            		<if test="cid != null and cid != 0">
		    and a.cid = #{cid}
		</if>
		<if test="cname != null and cname != ''">
		    and a.cname = #{cname}
		</if>

			<!-- 自定义查询条件 -->
			<if test="condition != null and condition != ''">
                ${condition}
			</if>
		</where>
	</select>

	<!-- 根据ID查询单个产品分类记录 -->
	<select id="queryProductcategoryById" parameterType="Integer" resultType="com.model.Productcategory">
        select *
        from productcategory a
        where a.cid = #{value}
	</select>

	<!-- ==================== 插入操作 ==================== -->

	<!-- 新增产品分类记录 -->
	<insert id="insertProductcategory" useGeneratedKeys="true" keyProperty="cid" parameterType="com.model.Productcategory">
        insert into productcategory
        (cid,cname)
        values
        (#{cid},#{cname})
	</insert>

	<!-- ==================== 更新操作 ==================== -->

	<!-- 更新产品分类记录 -->
	<update id="updateProductcategory" parameterType="com.model.Productcategory">
        update productcategory
		<set>
			<!-- 动态更新字段 -->
            		<if test="cname != null and cname != ''">
		    cname = #{cname},
		</if>

		</set>
		<where>
			<!-- 自定义更新条件 -->
			<if test="condition != null and condition != ''">
                ${condition}
			</if>
			<!-- 主键更新条件 -->
			<if test="cid != null">
				cid = #{cid}
			</if>
		</where>
	</update>

	<!-- ==================== 删除操作 ==================== -->

	<!-- 根据ID删除产品分类记录 -->
	<delete id="deleteProductcategory" parameterType="Integer">
        delete from productcategory
        where cid = #{value}
	</delete>

</mapper>
