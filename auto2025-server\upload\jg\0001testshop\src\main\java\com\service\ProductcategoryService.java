package com.service;

import java.util.List;

import com.model.Productcategory;
import com.util.PageBean;

/**
 * 产品分类业务逻辑服务接口
 *
 * 功能说明：
 * 1. 定义产品分类相关的业务逻辑接口
 * 2. 提供完整的CRUD操作方法
 * 3. 支持分页查询和条件查询
 * 4. 处理业务逻辑和数据验证
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024
 */
public interface ProductcategoryService {

	/**
	 * 分页查询产品分类列表
	 * @param productcategory 查询条件对象
	 * @param page 分页参数
	 * @return 产品分类列表
	 * @throws Exception 异常
	 */
	public List<Productcategory> queryProductcategoryList(Productcategory productcategory, PageBean page) throws Exception;

	/**
	 * 新增产品分类
	 * @param productcategory 产品分类实体对象
	 * @return 影响的行数
	 * @throws Exception 异常
	 */
	public int insertProductcategory(Productcategory productcategory) throws Exception;

	/**
	 * 根据ID删除产品分类
	 * @param id 主键ID
	 * @return 影响的行数
	 * @throws Exception 异常
	 */
	public int deleteProductcategory(Integer id) throws Exception;

	/**
	 * 更新产品分类
	 * @param productcategory 产品分类实体对象
	 * @return 影响的行数
	 * @throws Exception 异常
	 */
	public int updateProductcategory(Productcategory productcategory) throws Exception;

	/**
	 * 根据ID查询产品分类详情
	 * @param id 主键ID
	 * @return 产品分类实体对象，如果不存在则返回null
	 * @throws Exception 异常
	 */
	public Productcategory queryProductcategoryById(Integer id) throws Exception;

	/**
	 * 统计产品分类记录总数
	 * @param productcategory 查询条件对象
	 * @return 记录总数
	 */
	int getCount(Productcategory productcategory);

}
