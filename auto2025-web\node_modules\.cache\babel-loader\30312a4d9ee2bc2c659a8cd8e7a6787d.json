{"remainingRequest": "J:\\auto2025\\auto2025-web\\node_modules\\babel-loader\\lib\\index.js!J:\\auto2025\\auto2025-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!J:\\auto2025\\auto2025-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!J:\\auto2025\\auto2025-web\\src\\views\\web\\CodeGenerator.vue?vue&type=script&lang=js", "dependencies": [{"path": "J:\\auto2025\\auto2025-web\\src\\views\\web\\CodeGenerator.vue", "mtime": 1749465006927}, {"path": "J:\\auto2025\\auto2025-web\\babel.config.js", "mtime": 1748614864000}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748675476717}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748675484569}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748675476717}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1748675485115}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["ref", "reactive", "onMounted", "nextTick", "computed", "ElMessage", "ElMessageBox", "request", "base", "Setting", "Folder", "Edit", "Cpu", "DataBoard", "DocumentCopy", "Warning", "Plus", "View", "Download", "Delete", "Document", "Monitor", "Box", "Refresh", "User", "ArrowDown", "ArrowUp", "Loading", "Close", "Check", "Picture", "name", "components", "setup", "activeTab", "logLevel", "isLoggedIn", "loginDialogVisible", "loginLoading", "loginFormRef", "userInfo", "username", "loginTime", "loginForm", "password", "loginRules", "required", "message", "trigger", "selectedProject", "projectsLoading", "availableDatabases", "value", "text", "projectForm", "databaseType", "databaseMode", "projectCode", "databaseName", "selectedDatabase", "packageName", "backendTemplate", "frontendTemplate", "layer", "charts", "schoolName", "adminId", "admin<PERSON>ame", "adminRole", "adminLoginName", "copyProject", "currentProjectInfo", "projectTables", "projectTablesLoading", "formFields", "type", "tableData", "fields", "status", "updateTime", "sqlContent", "logs", "time", "level", "<PERSON><PERSON><PERSON><PERSON>", "days", "expires", "Date", "setTime", "getTime", "document", "cookie", "toUTCString", "<PERSON><PERSON><PERSON><PERSON>", "nameEQ", "ca", "split", "i", "length", "c", "char<PERSON>t", "substring", "indexOf", "deleteC<PERSON>ie", "checkLoginStatus", "sessionUser", "sessionStorage", "getItem", "sessionUserLname", "JSON", "parse", "toLocaleString", "error", "console", "savedUser", "userData", "decodeURIComponent", "handleLogin", "warning", "url", "loginData", "lname", "pwd", "res", "post", "code", "log", "stringify", "resdata", "setItem", "userId", "id", "encodeURIComponent", "success", "msg", "handleUserCommand", "command", "handleLogout", "removeItem", "selectProject", "projectType", "getProjectName", "projectNames", "openTemplateModal", "showBackendTemplateSelector", "openFrontTemplateModal", "showFrontendTemplateSelector", "handleDatabaseModeChange", "mode", "loadProjects", "params", "projects", "map", "project", "pid", "pno", "daname", "pname", "handleProjectSelect", "projectId", "dtype", "by1", "by2", "by4", "by5", "by6", "by3", "sessionInfo", "saveOrUpdateProject", "join", "projectData", "ptype", "pflag", "by7", "isUpdate", "_res$resdata", "_currentProjectInfo$v", "loadProjectTables", "table", "fieldsUrl", "fieldsRes", "tid", "field", "mid", "chineseName", "mozname", "englishName", "moname", "motype", "controlType", "moflag", "moyz", "searchable", "mobt", "visible", "<PERSON><PERSON><PERSON><PERSON>", "relatedTable", "customOptions", "generateDataCount", "parseInt", "warn", "tname", "showTableDesignModal", "currentTableTab", "currentTableDesign", "menuOrder", "generateData", "functions", "backendAdd", "backendEdit", "backendDelete", "backendDetail", "backendList", "batchImport", "batchExport", "backend<PERSON><PERSON><PERSON>", "backendRegister", "backendProfile", "backendPassword", "frontendAdd", "frontendEdit", "frontendDelete", "frontendDetail", "frontendList", "frontend<PERSON><PERSON><PERSON>", "miniAdd", "miniEdit", "miniDelete", "miniDetail", "miniList", "backendFunctions", "frontendFunctions", "miniFunctions", "resetFields", "showAiGeneratorDialog", "aiGeneratorInput", "aiGenerationInProgress", "showBackendTemplateDialog", "showFrontendTemplateDialog", "showTemplateDetailDialog", "backendTemplates", "frontendTemplates", "templatesLoading", "currentTemplateDetail", "generationInProgress", "generationResult", "sqlGenerationInProgress", "dataGenerationInProgress", "dataContent", "showFieldSettingsDialog", "currentFieldSettings", "currentFieldIndex", "showInSearchList", "showRelatedTableDialog", "availableRelatedTables", "showAiGeneratorModal", "inputElement", "querySelector", "focus", "hideAiGeneratorModal", "confirmAiGeneration", "description", "trim", "doubaoGenerate", "str", "input_text", "settings", "method", "timeout", "headers", "data", "info", "response", "fetch", "body", "ok", "Error", "result", "json", "generatedContent", "choices", "content", "cleanedContent", "replace", "createFormFromAI", "allLines", "lines", "line", "filter", "tableNameLine", "tableNameMatch", "match", "englishFields", "chineseFields", "fieldTypes", "now", "inferControlType", "push", "fieldsCount", "fieldType", "toLowerCase", "includes", "loadBackendTemplates", "loadFrontendTemplates", "selectBackendTemplate", "template", "sid", "sname", "selectFrontendTemplate", "viewTemplateDetail", "canGenerate", "generateProject", "projectNumber", "projectName", "tables", "tword", "tgn", "filesData", "files", "compressionData", "compression", "generateSqlScript", "generateDataScript", "script", "generateMySqlScript", "generateSqlServerScript", "for<PERSON>ach", "generateMySqlTableScript", "generateSqlServerTableScript", "fieldScripts", "index", "fieldScript", "convertToMySqlType", "<PERSON><PERSON><PERSON>", "convertToSqlServerType", "lowerType", "toUpperCase", "exportSqlScript", "blob", "Blob", "window", "URL", "createObjectURL", "link", "createElement", "href", "fileName", "download", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "copySqlScript", "navigator", "clipboard", "writeText", "textArea", "select", "execCommand", "fallback<PERSON><PERSON>r", "tablesWithData", "dataCount", "generateTableWithDataScript", "copyDataScript", "downloadProject", "filePath", "pop", "downloadUrl", "open", "getTemplateImageUrl", "memo4", "imgMatch", "handleImageError", "event", "target", "style", "display", "parent", "parentElement", "innerHTML", "getTableFieldCount", "Array", "isArray", "getTableFunctions", "activeFunctions", "slice", "openTableDesignModal", "resetTableDesign", "closeTableDesignModal", "switchTableTab", "tabId", "addNewFieldToDesign", "newField", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confirm", "confirmButtonText", "cancelButtonText", "deleteFieldFromDesign", "splice", "moveFieldUp", "moveFieldDown", "editFieldSettings", "closeFieldSettingsDialog", "saveFieldSettings", "showRelatedTableSelector", "loadAvailableRelatedTables", "displayName", "second<PERSON>ield", "thirdField", "tableName", "linkValue", "selected", "closeRelatedTableDialog", "confirmRelatedTableSelection", "selectedTables", "relatedTableStr", "saveTableDesign", "tlist", "vlist", "toString", "tableId", "fieldData", "molong", "deleteTable", "nextStep", "handleTabClick", "tab", "props"], "sources": ["J:\\auto2025\\auto2025-web\\src\\views\\web\\CodeGenerator.vue"], "sourcesContent": ["<template>\n  <div class=\"code-generator-container\">\n    <!-- 用户信息浮动显示 -->\n    <div v-if=\"isLoggedIn\" class=\"user-info-float\">\n      <el-dropdown @command=\"handleUserCommand\">\n        <span class=\"user-info-trigger\">\n          <el-icon>\n            <User />\n          </el-icon>\n          {{ userInfo.username }}\n          <el-icon class=\"el-icon--right\">\n            <ArrowDown />\n          </el-icon>\n        </span>\n        <template #dropdown>\n          <el-dropdown-menu>\n            <el-dropdown-item command=\"logout\">退出登录</el-dropdown-item>\n          </el-dropdown-menu>\n        </template>\n      </el-dropdown>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"main-content\">\n      <el-tabs v-model=\"activeTab\" type=\"card\" class=\"generator-tabs\" @tab-click=\"handleTabClick\">\n        <!-- 设置项目 -->\n        <el-tab-pane label=\"设置项目\" name=\"project\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <Folder />\n              </el-icon>\n              设置项目\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <!-- 页面标题 -->\n            <div class=\"page-header\">\n              <div class=\"header-content\">\n                <h1 class=\"page-title\">\n                  <el-icon class=\"title-icon\">\n                    <Setting />\n                  </el-icon>\n                  代码生成器\n                </h1>\n                <p class=\"page-description\">快速生成高质量的代码，提升开发效率</p>\n              </div>\n            </div>\n\n            <!-- 项目类型选择 -->\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>支持的项目类型</h3>\n                <p>选择您需要的技术栈，快速生成项目模板</p>\n              </div>\n\n              <!-- 功能卡片网格 -->\n              <div class=\"features-grid\">\n                <div class=\"feature-card\" @click=\"selectProject('springboot-thymeleaf')\"\n                  :class=\"{ active: selectedProject === 'springboot-thymeleaf' }\">\n                  <h3>🍃 SpringBoot + Thymeleaf</h3>\n                  <p>传统的服务端渲染架构，适合企业级应用开发，集成Thymeleaf模板引擎</p>\n                </div>\n\n                <div class=\"feature-card\" @click=\"selectProject('springboot-miniprogram')\"\n                  :class=\"{ active: selectedProject === 'springboot-miniprogram' }\">\n                  <h3>📱 SpringBoot + 小程序</h3>\n                  <p>微信小程序后端API开发，提供完整的用户认证和数据管理功能</p>\n                </div>\n\n                <div class=\"feature-card\" @click=\"selectProject('springboot-vue')\"\n                  :class=\"{ active: selectedProject === 'springboot-vue' }\">\n                  <h3>⚡ SpringBoot + Vue</h3>\n                  <p>现代化前后端分离架构，Vue.js前端 + SpringBoot后端API</p>\n                </div>\n\n                <div class=\"feature-card\" @click=\"selectProject('ssm-vue')\"\n                  :class=\"{ active: selectedProject === 'ssm-vue' }\">\n                  <h3>🔧 SSM + Vue</h3>\n                  <p>经典的SSM框架（Spring + SpringMVC + MyBatis）配合Vue.js前端</p>\n                </div>\n              </div>\n            </div>\n\n            <!-- 项目配置区域 -->\n            <div v-if=\"selectedProject\" class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>项目配置 - {{ getProjectName(selectedProject) }}</h3>\n                <p>配置项目基本信息和生成参数</p>\n              </div>\n              <div class=\"form-section\">\n                <el-form :model=\"projectForm\" label-width=\"120px\" class=\"project-form\">\n                  <el-row :gutter=\"20\">\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"数据库类型\">\n                        <el-select v-model=\"projectForm.databaseType\" placeholder=\"请选择数据库类型\" style=\"width: 100%\">\n                          <el-option label=\"MySQL\" value=\"mysql\" />\n                          <el-option label=\"SQL Server\" value=\"sqlserver\" />\n                        </el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"数据库模式\">\n                        <el-select v-model=\"projectForm.databaseMode\" placeholder=\"请选择数据库模式\" style=\"width: 100%\"\n                          @change=\"handleDatabaseModeChange\">\n                          <el-option label=\"新建数据库\" value=\"new\" />\n                          <el-option label=\"已有数据库\" value=\"existing\" />\n                        </el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"项目中文名称\">\n                        <el-input v-model=\"projectForm.name\" placeholder=\"请输入项目中文名称\" />\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n\n                  <el-row :gutter=\"20\" v-if=\"projectForm.databaseMode === 'new'\">\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"项目编号\">\n                        <el-input v-model=\"projectForm.projectCode\" placeholder=\"项目编号\" />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"数据库名称\">\n                        <el-input v-model=\"projectForm.databaseName\" placeholder=\"请输入数据库名称\" />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"学校名称\">\n                        <el-input v-model=\"projectForm.schoolName\" placeholder=\"请输入学校名称\" />\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n\n                  <el-row :gutter=\"20\" v-if=\"projectForm.databaseMode === 'existing'\">\n                    <el-col :span=\"24\">\n                      <el-form-item label=\"选择数据库\">\n                        <el-select v-model=\"projectForm.selectedDatabase\" placeholder=\"请选择数据库\" style=\"width: 100%\"\n                          @change=\"handleProjectSelect\" :loading=\"projectsLoading\">\n                          <el-option v-for=\"db in availableDatabases\" :key=\"db.value\" :label=\"db.text\" :value=\"db.value\"\n                            :disabled=\"!db.value\" />\n                        </el-select>\n                        <div class=\"form-text\" v-if=\"availableDatabases.length > 0\">\n                          格式：项目编号--数据库名称 (项目中文名称)\n                        </div>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n\n                  <el-row :gutter=\"20\">\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"后台模板\">\n                        <el-input v-model=\"projectForm.backendTemplate\" placeholder=\"点击选择后台模板\" readonly\n                          @click=\"openTemplateModal\" />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"前台模板\">\n                        <el-input v-model=\"projectForm.frontendTemplate\" placeholder=\"点击选择前台模板\" readonly\n                          @click=\"openFrontTemplateModal\" />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"复制项目\">\n                        <el-input v-model=\"projectForm.copyProject\" placeholder=\"请输入要复制的项目\" />\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n\n                  <el-row :gutter=\"20\">\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"layer弹出层\">\n                        <el-select v-model=\"projectForm.layer\" style=\"width: 100%\">\n                          <el-option label=\"否\" value=\"否\" />\n                          <el-option label=\"是\" value=\"是\" />\n                        </el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"统计图表\">\n                        <el-select v-model=\"projectForm.charts\" style=\"width: 100%\">\n                          <el-option label=\"否\" value=\"否\" />\n                          <el-option label=\"是\" value=\"是\" />\n                        </el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <!-- 空列，保持布局平衡 -->\n                    </el-col>\n                  </el-row>\n\n                  <el-form-item label=\"Session信息\">\n                    <el-row :gutter=\"10\">\n                      <el-col :span=\"6\">\n                        <el-input v-model=\"projectForm.adminId\" placeholder=\"管理员ID\" />\n                      </el-col>\n                      <el-col :span=\"6\">\n                        <el-input v-model=\"projectForm.adminName\" placeholder=\"管理员姓名\" />\n                      </el-col>\n                      <el-col :span=\"6\">\n                        <el-input v-model=\"projectForm.adminRole\" placeholder=\"管理员角色\" />\n                      </el-col>\n                      <el-col :span=\"6\">\n                        <el-input v-model=\"projectForm.adminLoginName\" placeholder=\"登录名\" />\n                      </el-col>\n                    </el-row>\n                  </el-form-item>\n                </el-form>\n\n                <div class=\"form-actions\">\n                  <el-button type=\"primary\" @click=\"nextStep\" size=\"large\">\n                    🎯 下一步：设计表单\n                  </el-button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 设计表单 -->\n        <el-tab-pane label=\"设计表单\" name=\"form\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <Edit />\n              </el-icon>\n              设计表单\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>数据表管理</h3>\n                <p>设计您的数据库表结构和表单字段</p>\n              </div>\n\n              <!-- 项目信息显示 -->\n              <div v-if=\"currentProjectInfo\" class=\"project-info-section\">\n                <div class=\"project-info-card\">\n                  <h4>当前项目：{{ currentProjectInfo.name }}</h4>\n                  <div class=\"project-details\">\n                    <span class=\"project-detail-item\">\n                      <strong>项目编号：</strong>{{ currentProjectInfo.projectCode }}\n                    </span>\n                    <span class=\"project-detail-item\">\n                      <strong>数据库：</strong>{{ currentProjectInfo.databaseName }}\n                    </span>\n                    <span class=\"project-detail-item\">\n                      <strong>类型：</strong>{{ getProjectName(selectedProject) }}\n                    </span>\n                  </div>\n                </div>\n              </div>\n\n              <!-- 表单列表 -->\n              <div class=\"table-designer\">\n                <div class=\"table-list-header\">\n                  <h4>数据表列表</h4>\n                  <el-button type=\"primary\" :icon=\"Plus\" @click=\"openTableDesignModal()\">\n                    添加新表\n                  </el-button>\n                </div>\n\n                <div v-if=\"projectTablesLoading\" class=\"loading-section\">\n                  <el-icon class=\"is-loading\">\n                    <Loading />\n                  </el-icon>\n                  <span>正在加载表单数据...</span>\n                </div>\n\n                <div v-else-if=\"projectTables.length === 0\" class=\"empty-section\">\n                  <el-empty description=\"暂无数据表\">\n                    <el-button type=\"primary\" @click=\"openTableDesignModal()\">创建第一个表</el-button>\n                  </el-empty>\n                </div>\n\n                <div v-else class=\"table-list\">\n                  <div class=\"table-item\" v-for=\"table in projectTables\" :key=\"table.tid\"\n                    @click=\"openTableDesignModal(table)\">\n                    <div class=\"table-item-header\">\n                      <h5>{{ table.tword || table.tname }}</h5>\n                      <div class=\"table-item-actions\">\n                        <el-button size=\"small\" :icon=\"Edit\" @click.stop=\"openTableDesignModal(table)\">编辑</el-button>\n                        <el-button size=\"small\" type=\"danger\" :icon=\"Delete\"\n                          @click.stop=\"deleteTable(table)\">删除</el-button>\n                      </div>\n                    </div>\n                    <p class=\"table-item-description\">\n                      {{ table.tname }} ({{ getTableFieldCount(table) }}个字段)\n                    </p>\n                    <div class=\"table-item-functions\" v-if=\"table.tgn\">\n                      <span class=\"function-tag\" v-for=\"func in getTableFunctions(table)\" :key=\"func\">{{ func }}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"form-actions\" style=\"margin-top: 30px;\">\n                <el-button @click=\"activeTab = 'project'\" size=\"large\">\n                  ← 上一步：项目配置\n                </el-button>\n                <el-button type=\"primary\" @click=\"nextStep\" size=\"large\">\n                  下一步：生成项目 →\n                </el-button>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 生成项目 -->\n        <el-tab-pane label=\"生成项目\" name=\"generate\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <Cpu />\n              </el-icon>\n              生成项目\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>生成项目</h3>\n                <p>确认配置信息并生成您的项目</p>\n              </div>\n\n              <!-- 项目配置摘要 -->\n              <div class=\"generation-summary\">\n                <h4>项目配置摘要</h4>\n                <el-row :gutter=\"20\">\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">项目名称:</span>\n                      <span class=\"summary-value\">{{ projectForm.name || '未设置' }}</span>\n                    </div>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">数据库名称:</span>\n                      <span class=\"summary-value\">{{ projectForm.databaseName || '未设置' }}</span>\n                    </div>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">项目编号:</span>\n                      <span class=\"summary-value\">{{ projectForm.projectCode || '未设置' }}</span>\n                    </div>\n                  </el-col>\n                </el-row>\n                <el-row :gutter=\"20\" style=\"margin-top: 15px;\">\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">数据表数量:</span>\n                      <span class=\"summary-value\">{{ projectTables.length }} 个</span>\n                    </div>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">后台模板:</span>\n                      <span class=\"summary-value\">{{ projectForm.backendTemplate || '未选择' }}</span>\n                    </div>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">前台模板:</span>\n                      <span class=\"summary-value\">{{ projectForm.frontendTemplate || '未选择' }}</span>\n                    </div>\n                  </el-col>\n                </el-row>\n              </div>\n\n              <!-- 生成操作区域 -->\n              <div class=\"generation-actions\">\n                <el-button\n                  type=\"primary\"\n                  size=\"large\"\n                  @click=\"generateProject\"\n                  :loading=\"generationInProgress\"\n                  :disabled=\"!canGenerate || generationInProgress\">\n                  <el-icon v-if=\"!generationInProgress\">\n                    <Cpu />\n                  </el-icon>\n                  <el-icon v-else>\n                    <Loading />\n                  </el-icon>\n                  {{ generationInProgress ? '正在生成项目，请稍候...' : '🚀 生成项目' }}\n                </el-button>\n\n                <!-- 生成进度提示 -->\n                <div v-if=\"generationInProgress\" class=\"generation-progress\">\n                  <el-progress :percentage=\"100\" :show-text=\"false\" status=\"success\" :indeterminate=\"true\" />\n                  <p class=\"progress-text\">正在生成代码文件和项目结构...</p>\n                </div>\n              </div>\n\n              <!-- 项目生成结果显示区域 -->\n              <div v-if=\"generationResult\" class=\"generation-result\">\n                <h4>项目生成结果</h4>\n\n                <!-- 生成状态 -->\n                <div class=\"generation-status\" :class=\"generationResult.status\">\n                  <el-icon class=\"status-icon\">\n                    <Check v-if=\"generationResult.status === 'success'\" />\n                    <Close v-else />\n                  </el-icon>\n                  <span class=\"status-text\">{{ generationResult.message }}</span>\n                </div>\n\n                <!-- 文件列表 -->\n                <div v-if=\"generationResult.files\" class=\"file-list-section\">\n                  <h5>生成的文件列表 ({{ generationResult.files.savedCount }}/{{ generationResult.files.totalCount }})</h5>\n\n                  <!-- 成功文件列表 -->\n                  <div v-if=\"generationResult.files.savedFiles && generationResult.files.savedFiles.length > 0\" class=\"success-files\">\n                    <h6>✅ 成功生成的文件:</h6>\n                    <el-scrollbar max-height=\"200px\">\n                      <ul class=\"file-list success\">\n                        <li v-for=\"file in generationResult.files.savedFiles\" :key=\"file\">\n                          <el-icon class=\"file-icon\"><Document /></el-icon>\n                          <span class=\"file-name\">{{ file }}</span>\n                        </li>\n                      </ul>\n                    </el-scrollbar>\n                  </div>\n\n                  <!-- 失败文件列表 -->\n                  <div v-if=\"generationResult.files.failedFiles && generationResult.files.failedFiles.length > 0\" class=\"failed-files\">\n                    <h6>❌ 生成失败的文件:</h6>\n                    <el-scrollbar max-height=\"200px\">\n                      <ul class=\"file-list error\">\n                        <li v-for=\"file in generationResult.files.failedFiles\" :key=\"file.fileName\">\n                          <el-icon class=\"file-icon\"><Warning /></el-icon>\n                          <span class=\"file-name\">{{ file.fileName }}</span>\n                          <span class=\"file-error\">{{ file.error }}</span>\n                        </li>\n                      </ul>\n                    </el-scrollbar>\n                  </div>\n                </div>\n\n                <!-- 压缩结果 -->\n                <div v-if=\"generationResult.compression\" class=\"compression-result\">\n                  <h5>项目压缩结果</h5>\n                  <div class=\"compression-info\" :class=\"generationResult.compression.status\">\n                    <el-icon class=\"status-icon\">\n                      <Box v-if=\"generationResult.compression.status === 'success'\" />\n                      <Close v-else />\n                    </el-icon>\n                    <span class=\"compression-text\">{{ generationResult.compression.message }}</span>\n                    <div v-if=\"generationResult.compression.data\" class=\"compression-details\">\n                      <p>文件名: {{ generationResult.compression.data.zipFileName }}</p>\n                      <p>文件大小: {{ generationResult.compression.data.fileSize }}</p>\n                      <el-button type=\"success\" @click=\"downloadProject(generationResult.compression.data.zipFilePath)\">\n                        <el-icon><Download /></el-icon>\n                        下载项目文件\n                      </el-button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 数据 -->\n        <el-tab-pane label=\"数据\" name=\"data\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <DataBoard />\n              </el-icon>\n              数据\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>数据脚本管理</h3>\n                <p>生成和管理数据库建表及插入数据脚本</p>\n              </div>\n              <div class=\"data-section\">\n                <div class=\"data-toolbar\">\n                  <el-button type=\"primary\" :icon=\"Plus\" @click=\"generateDataScript\" :loading=\"dataGenerationInProgress\">生成数据脚本</el-button>\n                  <el-button :icon=\"DocumentCopy\" @click=\"copyDataScript\">复制脚本</el-button>\n                </div>\n                <div class=\"data-editor\">\n                  <el-input v-model=\"dataContent\" type=\"textarea\" :rows=\"15\" placeholder=\"数据脚本将在这里显示...\"\n                    class=\"data-textarea\" />\n                </div>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- SQL脚本 -->\n        <el-tab-pane label=\"SQL脚本\" name=\"sql\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <DocumentCopy />\n              </el-icon>\n              SQL脚本\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>SQL脚本管理</h3>\n                <p>生成和管理数据库脚本</p>\n              </div>\n              <div class=\"sql-section\">\n                <div class=\"sql-toolbar\">\n                  <el-button type=\"primary\" :icon=\"Plus\" @click=\"generateSqlScript\" :loading=\"sqlGenerationInProgress\">生成建表脚本</el-button>\n                  <el-button :icon=\"Download\" @click=\"exportSqlScript\">导出脚本</el-button>\n                  <el-button :icon=\"DocumentCopy\" @click=\"copySqlScript\">复制脚本</el-button>\n                </div>\n                <div class=\"sql-editor\">\n                  <el-input v-model=\"sqlContent\" type=\"textarea\" :rows=\"25\" placeholder=\"SQL脚本将在这里显示...\"\n                    class=\"sql-textarea\" />\n                </div>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 错误日志 -->\n        <el-tab-pane label=\"错误日志\" name=\"logs\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <Warning />\n              </el-icon>\n              错误日志\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>错误日志</h3>\n                <p>查看生成过程中的错误和警告信息</p>\n              </div>\n              <div class=\"logs-section\">\n                <div class=\"logs-toolbar\">\n                  <el-button :icon=\"Refresh\">刷新日志</el-button>\n                  <el-button :icon=\"Delete\">清空日志</el-button>\n                  <el-select v-model=\"logLevel\" placeholder=\"日志级别\" style=\"width: 120px\">\n                    <el-option label=\"全部\" value=\"all\" />\n                    <el-option label=\"错误\" value=\"error\" />\n                    <el-option label=\"警告\" value=\"warning\" />\n                    <el-option label=\"信息\" value=\"info\" />\n                  </el-select>\n                </div>\n                <div class=\"logs-content\">\n                  <div class=\"log-item\" v-for=\"(log, index) in logs\" :key=\"index\" :class=\"log.level\">\n                    <div class=\"log-time\">{{ log.time }}</div>\n                    <div class=\"log-level\">{{ log.level.toUpperCase() }}</div>\n                    <div class=\"log-message\">{{ log.message }}</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n      </el-tabs>\n    </div>\n\n    <!-- 登录对话框 -->\n    <el-dialog v-model=\"loginDialogVisible\" title=\"用户登录\" width=\"400px\" :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\" :show-close=\"false\">\n      <el-form :model=\"loginForm\" :rules=\"loginRules\" ref=\"loginFormRef\" label-width=\"80px\">\n        <el-form-item label=\"用户名\" prop=\"username\">\n          <el-input v-model=\"loginForm.username\" placeholder=\"请输入用户名\" />\n        </el-form-item>\n        <el-form-item label=\"密码\" prop=\"password\">\n          <el-input v-model=\"loginForm.password\" type=\"password\" placeholder=\"请输入密码\" @keyup.enter=\"handleLogin\" />\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button type=\"primary\" @click=\"handleLogin\" :loading=\"loginLoading\">\n            登录\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n\n    <!-- 表设计弹窗 -->\n    <el-dialog v-model=\"showTableDesignModal\" :title=\"currentTableDesign.id ? '编辑数据表' : '创建数据表'\" width=\"85%\"\n      :close-on-click-modal=\"false\" :before-close=\"closeTableDesignModal\" class=\"table-design-dialog\" top=\"5vh\"\n      destroy-on-close>\n      <div class=\"table-design-container\">\n        <!-- 自定义Tab导航 -->\n        <div class=\"custom-tabs\">\n          <div class=\"tab-nav\">\n            <div class=\"tab-item\" :class=\"{ active: currentTableTab === 'table-settings' }\"\n              @click=\"switchTableTab('table-settings')\">\n              <el-icon class=\"tab-icon\">\n                <Setting />\n              </el-icon>\n              <span class=\"tab-text\">表设置</span>\n            </div>\n            <div class=\"tab-item\" :class=\"{ active: currentTableTab === 'field-design' }\"\n              @click=\"switchTableTab('field-design')\">\n              <el-icon class=\"tab-icon\">\n                <DataBoard />\n              </el-icon>\n              <span class=\"tab-text\">字段设计</span>\n            </div>\n          </div>\n\n          <!-- Tab内容 -->\n          <div class=\"tab-content-wrapper\">\n            <!-- 表设置内容 -->\n            <div v-show=\"currentTableTab === 'table-settings'\" class=\"tab-content\">\n              <!-- 基本信息 -->\n              <div class=\"basic-info-section\">\n                <el-form :model=\"currentTableDesign\" label-width=\"100px\" class=\"design-form\" size=\"default\">\n                  <el-row :gutter=\"16\">\n                    <el-col :span=\"6\">\n                      <el-form-item label=\"表中文名称\" required>\n                        <el-input v-model=\"currentTableDesign.chineseName\" placeholder=\"请输入表的中文名称\" clearable />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"6\">\n                      <el-form-item label=\"表英文名称\" required>\n                        <el-input v-model=\"currentTableDesign.englishName\" placeholder=\"请输入表的英文名称\" clearable />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"6\">\n                      <el-form-item label=\"菜单显示顺序\">\n                        <el-input-number v-model=\"currentTableDesign.menuOrder\" :min=\"1\" :max=\"999\" placeholder=\"1\" style=\"width: 100%;\" />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"6\">\n                      <el-form-item label=\"生成数据条数\">\n                        <el-input-number v-model=\"currentTableDesign.generateDataCount\" :min=\"0\" :max=\"1000\"\n                          placeholder=\"生成数据条数\" style=\"width: 100%;\" />\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n\n                  <el-row :gutter=\"16\">\n                    <el-col :span=\"24\">\n                      <el-form-item label=\"AI助手\">\n                        <el-button type=\"primary\" @click=\"showAiGeneratorModal('table')\" style=\"width: 100%;\">\n                          <el-icon>\n                            <Cpu />\n                          </el-icon>\n                          AI生成表结构\n                        </el-button>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                </el-form>\n              </div>\n\n              <!-- 功能选择 -->\n              <div class=\"function-selection-section\">\n                <h4 style=\"margin: 20px 0 15px 0; color: #2c3e50; font-size: 16px;\">功能选择</h4>\n                <el-row :gutter=\"20\">\n                  <!-- 后台功能 -->\n                  <el-col :span=\"8\">\n                    <div class=\"function-group\">\n                      <h5>后台功能</h5>\n                      <div class=\"function-checkboxes\">\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendAdd\">后台添加</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendEdit\">后台修改</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendDelete\">后台删除</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendDetail\">后台详情</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendList\">后台列表</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.batchImport\">批量导入</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.batchExport\">批量导出</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendLogin\">后台登录</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendRegister\">后台注册</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendProfile\">后台个人信息</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendPassword\">后台修改密码</el-checkbox>\n                      </div>\n                    </div>\n                  </el-col>\n\n                  <!-- 前台功能 -->\n                  <el-col :span=\"8\">\n                    <div class=\"function-group\">\n                      <h5>前台功能</h5>\n                      <div class=\"function-checkboxes\">\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendAdd\">前台添加</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendEdit\">前台修改</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendDelete\">前台删除</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendDetail\">前台详情</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendList\">前台列表</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendLogin\">前台个人信息和密码</el-checkbox>\n                      </div>\n                    </div>\n                  </el-col>\n\n                  <!-- 小程序功能 -->\n                  <el-col :span=\"8\">\n                    <div class=\"function-group\">\n                      <h5>小程序功能</h5>\n                      <div class=\"function-checkboxes\">\n                        <el-checkbox v-model=\"currentTableDesign.functions.miniAdd\">小程序添加</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.miniEdit\">小程序编辑</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.miniDelete\">小程序删除</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.miniDetail\">小程序详情</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.miniList\">小程序List</el-checkbox>\n                      </div>\n                    </div>\n                  </el-col>\n                </el-row>\n              </div>\n            </div>\n\n            <!-- 字段设计内容 -->\n            <div v-show=\"currentTableTab === 'field-design'\" class=\"tab-content\">\n              <!-- 工具栏 -->\n              <div class=\"field-toolbar-compact\">\n                <el-space size=\"default\" wrap>\n                  <el-button type=\"success\" @click=\"addNewFieldToDesign\">\n                    <el-icon>\n                      <Plus />\n                    </el-icon>\n                    添加字段\n                  </el-button>\n                  <el-button type=\"warning\" @click=\"resetFields\">\n                    <el-icon>\n                      <Refresh />\n                    </el-icon>\n                    重置字段\n                  </el-button>\n                  <el-button type=\"danger\" @click=\"clearAllFields\">\n                    <el-icon>\n                      <Delete />\n                    </el-icon>\n                    清空字段\n                  </el-button>\n                  <el-divider direction=\"vertical\" />\n                  <el-text type=\"info\">\n                    当前字段数量: {{ currentTableDesign.fields?.length || 0 }}\n                  </el-text>\n                </el-space>\n              </div>\n\n              <!-- 字段表格 -->\n              <div class=\"field-table-section\">\n                <el-table :data=\"currentTableDesign.fields\" class=\"field-table\" border stripe\n                  empty-text=\"暂无字段，请点击添加字段按钮\" size=\"small\">\n                  <el-table-column label=\"序号\" type=\"index\" width=\"60\" align=\"center\" />\n\n                  <el-table-column label=\"中文名称\" min-width=\"100\">\n                    <template #default=\"{ row }\">\n                      <el-input v-model=\"row.chineseName\" placeholder=\"请输入中文名称\" size=\"small\" clearable />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"英文名称\" min-width=\"100\">\n                    <template #default=\"{ row }\">\n                      <el-input v-model=\"row.englishName\" placeholder=\"请输入英文名称\" size=\"small\" clearable />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"字段类型\" min-width=\"80\">\n                    <template #default=\"{ row }\">\n                      <el-select v-model=\"row.type\" placeholder=\"选择类型\" size=\"small\" style=\"width: 100%\">\n                        <el-option label=\"int\" value=\"int\" />\n                        <el-option label=\"varchar(50)\" value=\"varchar(50)\" />\n                        <el-option label=\"varchar(100)\" value=\"varchar(100)\" />\n                        <el-option label=\"varchar(200)\" value=\"varchar(200)\" />\n                        <el-option label=\"varchar(500)\" value=\"varchar(500)\" />\n                        <el-option label=\"text\" value=\"text\" />\n                        <el-option label=\"datetime\" value=\"datetime\" />\n                        <el-option label=\"decimal(10,2)\" value=\"decimal(10,2)\" />\n                      </el-select>\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"控件类型\" min-width=\"100\">\n                    <template #default=\"{ row }\">\n                      <el-select v-model=\"row.controlType\" placeholder=\"选择控件\" size=\"small\" style=\"width: 100%\">\n                        <el-option label=\"文本框\" value=\"文本框\" />\n                        <el-option label=\"多行文本\" value=\"多行文本\" />\n                        <el-option label=\"下拉框\" value=\"下拉框\" />\n                        <el-option label=\"单选按钮\" value=\"单选按钮\" />\n                        <el-option label=\"复选框\" value=\"复选框\" />\n                        <el-option label=\"日期选择\" value=\"日期选择\" />\n                        <el-option label=\"时间选择\" value=\"时间选择\" />\n                        <el-option label=\"文件上传\" value=\"文件上传\" />\n                        <el-option label=\"图片上传\" value=\"图片上传\" />\n                        <el-option label=\"编辑器\" value=\"编辑器\" />\n                        <el-option label=\"自动当前时间\" value=\"自动当前时间\" />\n                      </el-select>\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"必填\" width=\"60\" align=\"center\">\n                    <template #default=\"{ row }\">\n                      <el-checkbox v-model=\"row.required\" />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"搜索\" width=\"60\" align=\"center\">\n                    <template #default=\"{ row }\">\n                      <el-checkbox v-model=\"row.searchable\" />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"显示\" width=\"60\" align=\"center\">\n                    <template #default=\"{ row }\">\n                      <el-checkbox v-model=\"row.visible\" />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"存在\" width=\"60\" align=\"center\">\n                    <template #default=\"{ row }\">\n                      <el-checkbox v-model=\"row.existsCheck\" />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"操作\" width=\"160\" align=\"center\" fixed=\"right\">\n                    <template #default=\"{ row, $index }\">\n                      <div class=\"field-actions\">\n                        <el-button size=\"small\" type=\"primary\" @click=\"moveFieldUp($index)\" :disabled=\"$index === 0\"\n                          circle>\n                          <el-icon>\n                            <ArrowUp />\n                          </el-icon>\n                        </el-button>\n                        <el-button size=\"small\" type=\"primary\" @click=\"moveFieldDown($index)\"\n                          :disabled=\"$index === currentTableDesign.fields.length - 1\" circle>\n                          <el-icon>\n                            <ArrowDown />\n                          </el-icon>\n                        </el-button>\n                        <el-button size=\"small\" type=\"warning\" @click=\"editFieldSettings(row, $index)\" circle>\n                          <el-icon>\n                            <Edit />\n                          </el-icon>\n                        </el-button>\n                        <el-button size=\"small\" type=\"danger\" @click=\"deleteFieldFromDesign($index)\" circle>\n                          <el-icon>\n                            <Delete />\n                          </el-icon>\n                        </el-button>\n                      </div>\n                    </template>\n                  </el-table-column>\n                </el-table>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-space size=\"large\">\n            <el-button @click=\"closeTableDesignModal\" size=\"large\">\n              <el-icon>\n                <Close />\n              </el-icon>\n              <span>取消</span>\n            </el-button>\n            <el-button type=\"primary\" @click=\"saveTableDesign\" size=\"large\">\n              <el-icon>\n                <Check />\n              </el-icon>\n              <span>保存表设计</span>\n            </el-button>\n          </el-space>\n        </div>\n      </template>\n    </el-dialog>\n\n    <!-- AI生成器弹窗 -->\n    <el-dialog v-model=\"showAiGeneratorDialog\" title=\"AI生成表结构\" width=\"600px\" :close-on-click-modal=\"false\"\n      :before-close=\"hideAiGeneratorModal\" class=\"ai-generator-dialog\" destroy-on-close>\n      <div class=\"ai-generator-container\">\n        <div class=\"ai-generator-header\">\n          <h4>🤖 AI智能生成表结构</h4>\n          <p>描述您要生成的表结构，AI将自动为您创建完整的数据表设计</p>\n        </div>\n\n        <div class=\"ai-generator-body\">\n          <el-form label-width=\"100px\">\n            <el-form-item label=\"表描述\">\n              <el-input v-model=\"aiGeneratorInput\" type=\"textarea\" :rows=\"4\"\n                placeholder=\"请输入表的描述，例如：学生信息管理表、商品管理系统、订单管理表等...\" class=\"ai-generator-input\"\n                :disabled=\"aiGenerationInProgress\" />\n            </el-form-item>\n          </el-form>\n\n          <div class=\"ai-generator-example\">\n            <h5>💡 示例：</h5>\n            <div class=\"example-list\">\n              <div class=\"example-item\">\n                <strong>表结构描述：</strong>\"学生信息管理表\" 或 \"商品管理系统\" 或 \"订单管理表\"\n              </div>\n              <div class=\"example-item\">\n                <strong>功能说明：</strong>AI会根据您的描述自动生成包含合适字段的完整表结构\n              </div>\n            </div>\n          </div>\n        </div>\n\n      </div>\n\n      <template #footer>\n        <div class=\"ai-generator-footer\">\n          <el-button @click=\"hideAiGeneratorModal\" :disabled=\"aiGenerationInProgress\">取消</el-button>\n          <el-button type=\"primary\" @click=\"confirmAiGeneration\" :loading=\"aiGenerationInProgress\">\n            <el-icon v-if=\"!aiGenerationInProgress\">\n              <Cpu />\n            </el-icon>\n            {{ aiGenerationInProgress ? '生成中...' : '🚀 生成' }}\n          </el-button>\n        </div>\n      </template>\n    </el-dialog>\n\n    <!-- 后台模板选择弹窗 -->\n    <el-dialog v-model=\"showBackendTemplateDialog\" title=\"选择后台模板\" width=\"1000px\" :close-on-click-modal=\"false\"\n      class=\"template-dialog\" destroy-on-close>\n      <div class=\"template-container\">\n        <div class=\"template-grid-4col\" v-loading=\"templatesLoading\">\n          <div v-for=\"template in backendTemplates\" :key=\"template.sid\" class=\"template-item-4col\">\n            <div class=\"template-image\" @click=\"selectBackendTemplate(template)\">\n              <img v-if=\"template.memo4\" :src=\"getTemplateImageUrl(template.memo4)\" :alt=\"template.sname\"\n                @error=\"handleImageError\" />\n              <div v-else class=\"no-image\">\n                <el-icon>\n                  <Picture />\n                </el-icon>\n                <span>暂无预览</span>\n              </div>\n            </div>\n            <div class=\"template-info\">\n              <h4 @click=\"selectBackendTemplate(template)\">{{ template.sname }}</h4>\n              <p class=\"template-id\">模板ID: {{ template.sid }}</p>\n              <div class=\"template-actions\">\n                <el-button type=\"primary\" size=\"small\" @click=\"selectBackendTemplate(template)\">\n                  选择模板\n                </el-button>\n                <el-button type=\"info\" size=\"small\" @click=\"viewTemplateDetail(template)\">\n                  查看详情\n                </el-button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-dialog>\n\n    <!-- 前台模板选择弹窗 -->\n    <el-dialog v-model=\"showFrontendTemplateDialog\" title=\"选择前台模板\" width=\"1000px\" :close-on-click-modal=\"false\"\n      class=\"template-dialog\" destroy-on-close>\n      <div class=\"template-container\">\n        <div class=\"template-grid-4col\" v-loading=\"templatesLoading\">\n          <div v-for=\"template in frontendTemplates\" :key=\"template.sid\" class=\"template-item-4col\">\n            <div class=\"template-image\" @click=\"selectFrontendTemplate(template)\">\n              <img v-if=\"template.memo4\" :src=\"getTemplateImageUrl(template.memo4)\" :alt=\"template.sname\"\n                @error=\"handleImageError\" />\n              <div v-else class=\"no-image\">\n                <el-icon>\n                  <Picture />\n                </el-icon>\n                <span>暂无预览</span>\n              </div>\n            </div>\n            <div class=\"template-info\">\n              <h4 @click=\"selectFrontendTemplate(template)\">{{ template.sname }}</h4>\n              <p class=\"template-id\">模板ID: {{ template.sid }}</p>\n              <div class=\"template-actions\">\n                <el-button type=\"primary\" size=\"small\" @click=\"selectFrontendTemplate(template)\">\n                  选择模板\n                </el-button>\n                <el-button type=\"info\" size=\"small\" @click=\"viewTemplateDetail(template)\">\n                  查看详情\n                </el-button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-dialog>\n\n    <!-- 模板详情查看弹窗 -->\n    <el-dialog v-model=\"showTemplateDetailDialog\" title=\"模板详情\" width=\"90%\" :close-on-click-modal=\"false\"\n      class=\"template-detail-dialog\" destroy-on-close top=\"5vh\">\n      <div class=\"template-detail-container\" v-if=\"currentTemplateDetail\">\n        <div class=\"template-detail-header\">\n          <h3>{{ currentTemplateDetail.sname }}</h3>\n          <p class=\"template-detail-id\">模板ID: {{ currentTemplateDetail.sid }}</p>\n        </div>\n        <div class=\"template-detail-image-container\">\n          <div v-if=\"currentTemplateDetail.memo4\" class=\"template-detail-image\">\n            <el-image\n              :src=\"getTemplateImageUrl(currentTemplateDetail.memo4)\"\n              :alt=\"currentTemplateDetail.sname\"\n              fit=\"contain\"\n              :preview-src-list=\"[getTemplateImageUrl(currentTemplateDetail.memo4)]\"\n              :initial-index=\"0\"\n              preview-teleported\n              class=\"template-preview-image\"\n            />\n          </div>\n          <div v-else class=\"no-image-large\">\n            <el-icon>\n              <Picture />\n            </el-icon>\n            <span>暂无预览图片</span>\n          </div>\n        </div>\n        <div class=\"template-detail-tips\">\n          <el-alert\n            title=\"提示\"\n            type=\"info\"\n            :closable=\"false\"\n            show-icon>\n            <template #default>\n              点击图片可以放大查看，支持缩放和拖拽操作\n            </template>\n          </el-alert>\n        </div>\n      </div>\n      <template #footer>\n        <div class=\"template-detail-footer\">\n          <el-button @click=\"showTemplateDetailDialog = false\" size=\"large\">关闭</el-button>\n        </div>\n      </template>\n    </el-dialog>\n\n    <!-- 字段设置弹窗 -->\n    <el-dialog v-model=\"showFieldSettingsDialog\" title=\"字段设置\" width=\"600px\" :close-on-click-modal=\"false\"\n      class=\"field-settings-dialog\" destroy-on-close>\n      <div class=\"field-settings-container\" v-if=\"currentFieldSettings\">\n        <el-form :model=\"currentFieldSettings\" label-width=\"100px\" size=\"default\">\n          <el-form-item label=\"字段名称\">\n            <el-input v-model=\"currentFieldSettings.chineseName\" readonly />\n          </el-form-item>\n\n          <el-form-item label=\"关联表\">\n            <el-input v-model=\"currentFieldSettings.relatedTable\"\n              placeholder=\"例如：doro,dbid,dormitory,1\"\n              @click=\"showRelatedTableSelector\"\n              readonly\n              style=\"cursor: pointer;\">\n              <template #suffix>\n                <el-icon style=\"cursor: pointer;\">\n                  <View />\n                </el-icon>\n              </template>\n            </el-input>\n          </el-form-item>\n\n          <el-form-item label=\"是否必填\">\n            <el-checkbox v-model=\"showInSearchList\">搜索列表显示</el-checkbox>\n          </el-form-item>\n\n          <el-form-item label=\"自定义选项\">\n            <el-input v-model=\"currentFieldSettings.customOptions\"\n              type=\"textarea\"\n              :rows=\"6\"\n              placeholder=\"一行一个选项，例如：&#10;选项1&#10;选项2&#10;选项3\" />\n          </el-form-item>\n        </el-form>\n      </div>\n\n      <template #footer>\n        <div class=\"field-settings-footer\">\n          <el-button @click=\"closeFieldSettingsDialog\">取消</el-button>\n          <el-button type=\"primary\" @click=\"saveFieldSettings\">保存</el-button>\n        </div>\n      </template>\n    </el-dialog>\n\n    <!-- 关联表选择弹窗 -->\n    <el-dialog v-model=\"showRelatedTableDialog\" title=\"选择关联表\" width=\"800px\" :close-on-click-modal=\"false\"\n      class=\"related-table-dialog\" destroy-on-close>\n      <div class=\"related-table-container\">\n        <el-table :data=\"availableRelatedTables\" border stripe size=\"small\" max-height=\"400\">\n          <el-table-column label=\"主键ID\" prop=\"primaryKey\" width=\"80\" align=\"center\" />\n          <el-table-column label=\"名称\" prop=\"displayName\" width=\"120\" />\n          <el-table-column label=\"表名称\" prop=\"tableName\" width=\"150\" />\n          <el-table-column label=\"联动\" width=\"100\" align=\"center\">\n            <template #default=\"{ row }\">\n              <el-input v-model=\"row.linkValue\" size=\"small\" style=\"width: 60px;\" />\n            </template>\n          </el-table-column>\n          <el-table-column label=\"选择\" width=\"80\" align=\"center\">\n            <template #default=\"{ row }\">\n              <el-checkbox v-model=\"row.selected\" />\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n\n      <template #footer>\n        <div class=\"related-table-footer\">\n          <el-button @click=\"closeRelatedTableDialog\">取消</el-button>\n          <el-button type=\"primary\" @click=\"confirmRelatedTableSelection\">确定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<style scoped>\n  @import '../../styles/CodeGenerator.css';\n\n  /* 生成进度样式 */\n  .generation-progress {\n    margin-top: 20px;\n    padding: 20px;\n    background: #f8f9fa;\n    border-radius: 8px;\n    text-align: center;\n  }\n\n  .progress-text {\n    margin-top: 10px;\n    color: #606266;\n    font-size: 14px;\n  }\n\n  /* 生成结果样式 */\n  .generation-result {\n    margin-top: 30px;\n    padding: 20px;\n    border: 1px solid #e4e7ed;\n    border-radius: 8px;\n    background: #fff;\n  }\n\n  .generation-status {\n    display: flex;\n    align-items: center;\n    margin-bottom: 20px;\n    padding: 15px;\n    border-radius: 6px;\n  }\n\n  .generation-status.success {\n    background: #f0f9ff;\n    border: 1px solid #67c23a;\n    color: #67c23a;\n  }\n\n  .generation-status.error {\n    background: #fef0f0;\n    border: 1px solid #f56c6c;\n    color: #f56c6c;\n  }\n\n  .status-icon {\n    margin-right: 10px;\n    font-size: 18px;\n  }\n\n  .status-text {\n    font-weight: 500;\n    font-size: 16px;\n  }\n\n  /* 文件列表样式 */\n  .file-list-section {\n    margin-top: 20px;\n  }\n\n  .file-list {\n    list-style: none;\n    padding: 0;\n    margin: 0;\n  }\n\n  .file-list li {\n    display: flex;\n    align-items: center;\n    padding: 8px 12px;\n    margin-bottom: 4px;\n    border-radius: 4px;\n    background: #f8f9fa;\n  }\n\n  .file-list.success li {\n    background: #f0f9ff;\n    border-left: 3px solid #67c23a;\n  }\n\n  .file-list.error li {\n    background: #fef0f0;\n    border-left: 3px solid #f56c6c;\n  }\n\n  .file-icon {\n    margin-right: 8px;\n    color: #909399;\n  }\n\n  .file-name {\n    flex: 1;\n    font-family: 'Courier New', monospace;\n    font-size: 13px;\n  }\n\n  .file-error {\n    color: #f56c6c;\n    font-size: 12px;\n    margin-left: 10px;\n  }\n\n  /* 压缩结果样式 */\n  .compression-result {\n    margin-top: 20px;\n    padding: 15px;\n    border: 1px solid #e4e7ed;\n    border-radius: 6px;\n    background: #fafafa;\n  }\n\n  .compression-info {\n    display: flex;\n    align-items: center;\n    flex-wrap: wrap;\n  }\n\n  .compression-text {\n    margin-left: 10px;\n    font-weight: 500;\n  }\n\n  .compression-details {\n    width: 100%;\n    margin-top: 15px;\n    padding-top: 15px;\n    border-top: 1px solid #e4e7ed;\n  }\n\n  .compression-details p {\n    margin: 5px 0;\n    color: #606266;\n  }\n</style>\n\n<script>\n  import { ref, reactive, onMounted, nextTick, computed } from 'vue'\n  import { ElMessage, ElMessageBox } from 'element-plus'\n  import request, { base } from \"../../../utils/http\"\n  import {\n    Setting, Folder, Edit, Cpu, DataBoard, DocumentCopy, Warning,\n    Plus, View, Download, Delete, Document, Monitor, Box, Refresh,\n    User, ArrowDown, ArrowUp, Loading, Close, Check, Picture\n  } from '@element-plus/icons-vue'\n\n  export default {\n    name: 'CodeGenerator',\n    components: {\n      Setting, Folder, Edit, Cpu, DataBoard, DocumentCopy, Warning,\n      Plus, View, Download, Delete, Document, Monitor, Box, Refresh,\n      User, ArrowDown, ArrowUp, Loading, Close, Check, Picture\n    },\n    setup() {\n      const activeTab = ref('project')\n      const logLevel = ref('all')\n\n      // 登录相关状态\n      const isLoggedIn = ref(false)\n      const loginDialogVisible = ref(false)\n      const loginLoading = ref(false)\n      const loginFormRef = ref(null)\n\n      // 用户信息\n      const userInfo = reactive({\n        username: '',\n        loginTime: ''\n      })\n\n      // 登录表单\n      const loginForm = reactive({\n        username: '',\n        password: ''\n      })\n\n      // 登录表单验证规则\n      const loginRules = {\n        username: [\n          { required: true, message: '请输入用户名', trigger: 'blur' }\n        ],\n        password: [\n          { required: true, message: '请输入密码', trigger: 'blur' }\n        ]\n      }\n\n      // 项目选择和配置\n      const selectedProject = ref('')\n      const projectsLoading = ref(false)\n      const availableDatabases = ref([\n        { value: '', text: '请选择数据库' }\n      ])\n\n      const projectForm = reactive({\n        databaseType: 'mysql',\n        databaseMode: 'new',\n        projectCode: '',\n        databaseName: '',\n        selectedDatabase: '',\n        name: '',\n        packageName: 'com',\n        backendTemplate: '',\n        frontendTemplate: '',\n        layer: '否',\n        charts: '否',\n        schoolName: '',\n        adminId: '',\n        adminName: '',\n        adminRole: '',\n        adminLoginName: '',\n        copyProject: ''\n      })\n\n      // 项目表单相关\n      const currentProjectInfo = ref(null)\n      const projectTables = ref([])\n      const projectTablesLoading = ref(false)\n\n      const formFields = ref([\n        { name: 'id', type: 'Long' },\n        { name: 'name', type: 'String' },\n        { name: 'email', type: 'String' },\n        { name: 'createTime', type: 'Date' }\n      ])\n\n      const tableData = ref([\n        { name: 'user', fields: 8, status: '已配置', updateTime: '2024-01-15 10:30:00' },\n        { name: 'role', fields: 5, status: '未配置', updateTime: '2024-01-15 09:15:00' },\n        { name: 'permission', fields: 6, status: '已配置', updateTime: '2024-01-14 16:45:00' }\n      ])\n\n      const sqlContent = ref(``)\n\n      const logs = ref([\n        { time: '2024-01-15 10:30:15', level: 'info', message: '开始生成代码...' },\n        { time: '2024-01-15 10:30:16', level: 'success', message: '实体类生成成功' },\n        { time: '2024-01-15 10:30:17', level: 'warning', message: '字段名称建议使用驼峰命名' },\n        { time: '2024-01-15 10:30:18', level: 'error', message: '数据库连接失败，请检查配置' }\n      ])\n\n      // Cookie操作工具函数\n      const setCookie = (name, value, days) => {\n        const expires = new Date()\n        expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000))\n        document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`\n      }\n\n      const getCookie = (name) => {\n        const nameEQ = name + \"=\"\n        const ca = document.cookie.split(';')\n        for (let i = 0; i < ca.length; i++) {\n          let c = ca[i]\n          while (c.charAt(0) === ' ') c = c.substring(1, c.length)\n          if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length)\n        }\n        return null\n      }\n\n      const deleteCookie = (name) => {\n        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`\n      }\n\n      // 检查登录状态\n      const checkLoginStatus = () => {\n        // 首先检查sessionStorage中的用户信息\n        const sessionUser = sessionStorage.getItem(\"user\")\n        const sessionUserLname = sessionStorage.getItem(\"userLname\")\n\n        if (sessionUser && sessionUserLname) {\n          try {\n            JSON.parse(sessionUser) // 验证JSON格式\n            userInfo.username = sessionUserLname\n            userInfo.loginTime = new Date().toLocaleString()\n            isLoggedIn.value = true\n            return\n          } catch (error) {\n            console.error('解析sessionStorage用户信息失败:', error)\n          }\n        }\n\n        // 如果sessionStorage中没有，再检查Cookie\n        const savedUser = getCookie('codeGeneratorUser')\n        if (savedUser) {\n          try {\n            const userData = JSON.parse(decodeURIComponent(savedUser))\n            userInfo.username = userData.username\n            userInfo.loginTime = userData.loginTime\n            isLoggedIn.value = true\n          } catch (error) {\n            console.error('解析Cookie用户信息失败:', error)\n            deleteCookie('codeGeneratorUser')\n          }\n        } else {\n          loginDialogVisible.value = true\n        }\n      }\n\n      // 处理登录\n      const handleLogin = async () => {\n        if (!loginFormRef.value) return\n\n        // 基本验证\n        if (!loginForm.username) {\n          ElMessage.warning('请输入用户名')\n          return\n        }\n        if (!loginForm.password) {\n          ElMessage.warning('请输入密码')\n          return\n        }\n\n        try {\n          loginLoading.value = true\n\n          // 调用登录API\n          const url = base + \"/admin/login\"\n          const loginData = {\n            lname: loginForm.username,\n            pwd: loginForm.password\n          }\n\n          const res = await request.post(url, loginData)\n          loginLoading.value = false\n\n          if (res.code == 200) {\n            console.log('登录成功:', JSON.stringify(res.resdata))\n\n            // 保存用户信息到sessionStorage（参考管理员登录）\n            sessionStorage.setItem(\"user\", JSON.stringify(res.resdata))\n            sessionStorage.setItem(\"userLname\", res.resdata.lname)\n            sessionStorage.setItem(\"role\", \"管理员\")\n\n            // 更新本地状态\n            userInfo.username = res.resdata.lname\n            userInfo.loginTime = new Date().toLocaleString()\n\n            // 保存到Cookie，有效期15天\n            const userData = {\n              username: res.resdata.lname,\n              loginTime: userInfo.loginTime,\n              userId: res.resdata.id\n            }\n            setCookie('codeGeneratorUser', encodeURIComponent(JSON.stringify(userData)), 15)\n\n            isLoggedIn.value = true\n            loginDialogVisible.value = false\n\n            // 重置表单\n            loginForm.username = ''\n            loginForm.password = ''\n\n            ElMessage.success('登录成功！')\n          } else {\n            ElMessage.error(res.msg || '登录失败')\n          }\n        } catch (error) {\n          loginLoading.value = false\n          console.error('登录失败:', error)\n          ElMessage.error('登录失败，请检查网络连接')\n        }\n      }\n\n      // 处理用户下拉菜单命令\n      const handleUserCommand = (command) => {\n        if (command === 'logout') {\n          handleLogout()\n        }\n      }\n\n      // 退出登录\n      const handleLogout = () => {\n        // 清除Cookie\n        deleteCookie('codeGeneratorUser')\n\n        // 清除sessionStorage\n        sessionStorage.removeItem(\"user\")\n        sessionStorage.removeItem(\"userLname\")\n        sessionStorage.removeItem(\"role\")\n\n        // 重置状态\n        isLoggedIn.value = false\n        userInfo.username = ''\n        userInfo.loginTime = ''\n        loginDialogVisible.value = true\n\n        ElMessage.success('已退出登录')\n      }\n\n      // 项目类型选择\n      const selectProject = (projectType) => {\n        selectedProject.value = projectType\n        console.log('选择项目类型:', projectType)\n      }\n\n      // 获取项目名称\n      const getProjectName = (projectType) => {\n        const projectNames = {\n          'springboot-thymeleaf': 'SpringBoot + Thymeleaf',\n          'springboot-miniprogram': 'SpringBoot + 小程序',\n          'springboot-vue': 'SpringBoot + Vue',\n          'ssm-vue': 'SSM + Vue'\n        }\n        return projectNames[projectType] || projectType\n      }\n\n      // 打开模板选择弹窗\n      const openTemplateModal = () => {\n        console.log('打开后台模板选择弹窗')\n        showBackendTemplateSelector()\n      }\n\n      const openFrontTemplateModal = () => {\n        console.log('打开前台模板选择弹窗')\n        showFrontendTemplateSelector()\n      }\n\n      // 处理数据库模式变化\n      const handleDatabaseModeChange = (mode) => {\n        if (mode === 'existing') {\n          loadProjects()\n        } else {\n          // 清空已有数据库选项\n          availableDatabases.value = [{ value: '', text: '请选择数据库' }]\n          projectForm.selectedDatabase = ''\n        }\n      }\n\n      // 加载项目列表\n      const loadProjects = async () => {\n        try {\n          projectsLoading.value = true\n          const url = base + \"/projects/list?currentPage=1&pageSize=1000\"\n          const params = {\n\n          }\n\n          const res = await request.post(url, { params })\n\n          if (res.code === 200) {\n            const projects = res.resdata || []\n            availableDatabases.value = [\n              { value: '', text: '请选择数据库' },\n              ...projects.map(project => ({\n                value: project.pid,\n                text: `${project.pno}--${project.daname} (${project.pname})`\n              }))\n            ]\n          } else {\n            ElMessage.error('加载项目列表失败')\n          }\n        } catch (error) {\n          console.error('加载项目列表失败:', error)\n          ElMessage.error('加载项目列表失败，请检查网络连接')\n        } finally {\n          projectsLoading.value = false\n        }\n      }\n\n      // 处理项目选择\n      const handleProjectSelect = async (projectId) => {\n        if (!projectId) return\n\n        try {\n          const url = base + \"/projects/get?id=\" + projectId;\n\n\n          const res = await request.post(url, {})\n\n          if (res.code === 200) {\n            const project = res.resdata\n            // 初始化表单数据\n            projectForm.projectCode = project.pno || ''\n            projectForm.databaseName = project.daname || ''\n            projectForm.name = project.pname || ''\n            projectForm.databaseType = project.dtype || 'mysql'\n            projectForm.backendTemplate = project.by1 || ''\n            projectForm.frontendTemplate = project.by2 || ''\n            projectForm.layer = project.by4 || '否'\n            projectForm.charts = project.by5 || '否'\n            projectForm.schoolName = project.by6 || ''\n\n            // 解析Session信息\n            if (project.by3) {\n              const sessionInfo = project.by3.split(',')\n              projectForm.adminId = sessionInfo[0] || ''\n              projectForm.adminName = sessionInfo[1] || ''\n              projectForm.adminRole = sessionInfo[2] || ''\n              projectForm.adminLoginName = sessionInfo[3] || ''\n            }\n\n            ElMessage.success('项目信息加载成功')\n          } else {\n            ElMessage.error('加载项目详情失败')\n          }\n        } catch (error) {\n          console.error('加载项目详情失败:', error)\n          ElMessage.error('加载项目详情失败，请检查网络连接')\n        }\n      }\n\n      // 保存或更新项目到数据库\n      const saveOrUpdateProject = async () => {\n        try {\n          // 构建Session信息\n          const sessionInfo = [\n            projectForm.adminId,\n            projectForm.adminName,\n            projectForm.adminRole,\n            projectForm.adminLoginName\n          ].join(',')\n\n          const projectData = {\n            ptype: selectedProject.value,\n            dtype: projectForm.databaseType,\n            pflag: projectForm.databaseMode === 'new' ? '1' : '2',\n            pno: projectForm.projectCode,\n            daname: projectForm.databaseName,\n            pname: projectForm.name,\n            by1: projectForm.backendTemplate,\n            by2: projectForm.frontendTemplate,\n            by3: sessionInfo,\n            by4: projectForm.layer,\n            by5: projectForm.charts,\n            by6: projectForm.schoolName,\n            by7: projectForm.copyProject,\n            lname: userInfo.username\n          }\n\n          // 判断是新建还是更新\n          const isUpdate = currentProjectInfo.value && currentProjectInfo.value.pid\n          let url, res\n\n          if (isUpdate) {\n            // 更新项目\n            projectData.pid = currentProjectInfo.value.pid\n            url = base + \"/projects/update\"\n            res = await request.post(url, projectData)\n          } else {\n            // 新建项目\n            url = base + \"/projects/add\"\n            res = await request.post(url, projectData)\n          }\n\n          if (res.code === 200) {\n            const message = isUpdate ? '项目更新成功' : '项目保存成功'\n            ElMessage.success(message)\n\n            // 如果是新建项目，保存返回的项目ID\n            if (!isUpdate && res.resdata && res.resdata.pid) {\n              currentProjectInfo.value = {\n                ...currentProjectInfo.value,\n                pid: res.resdata.pid\n              }\n            }\n\n            return { success: true, projectId: res.resdata?.pid || currentProjectInfo.value?.pid }\n          } else {\n            ElMessage.error(res.msg || '项目保存失败')\n            return { success: false }\n          }\n        } catch (error) {\n          console.error('保存项目失败:', error)\n          ElMessage.error('保存项目失败，请检查网络连接')\n          return { success: false }\n        }\n      }\n\n      // 加载项目表单列表\n      const loadProjectTables = async (projectId) => {\n        try {\n          projectTablesLoading.value = true\n          const url = base + \"/tables/list?currentPage=1&pageSize=1000\"\n          const params = {\n            pid: projectId\n          }\n\n          const res = await request.post(url, params)\n\n          if (res.code === 200) {\n            projectTables.value = res.resdata || []\n\n            // 为每个表加载字段数据\n            for (let table of projectTables.value) {\n              try {\n                const fieldsUrl = base + \"/mores/list?currentPage=1&pageSize=100\"\n                const fieldsRes = await request.post(fieldsUrl, { tid: table.tid })\n\n                if (fieldsRes.code === 200 && fieldsRes.resdata) {\n                  // 将字段数据转换为前端格式\n                  table.fields = fieldsRes.resdata.map(field => ({\n                    id: field.mid,\n                    tid: field.tid,\n                    chineseName: field.mozname,\n                    englishName: field.moname,\n                    type: field.motype,\n                    controlType: field.moflag,\n                    required: field.moyz === '1',\n                    searchable: field.mobt === '1',\n                    visible: field.by1 === '1',\n                    existsCheck: field.by2 === '1',\n                    relatedTable: field.by3 || '', // 关联表\n                    customOptions: field.by4 || '' // 自定义选项\n                  }))\n\n                  // 设置表的生成数据条数\n                  table.generateDataCount = parseInt(table.by1 || '0')\n                } else {\n                  table.fields = []\n                }\n              } catch (error) {\n                console.warn('加载表字段失败:', table.tname, error)\n                table.fields = []\n              }\n            }\n\n            console.log('加载项目表单成功:', projectTables.value)\n          } else {\n            ElMessage.error('加载项目表单失败')\n          }\n        } catch (error) {\n          console.error('加载项目表单失败:', error)\n          ElMessage.error('加载项目表单失败，请检查网络连接')\n        } finally {\n          projectTablesLoading.value = false\n        }\n      }\n\n      // 表设计相关\n      const showTableDesignModal = ref(false)\n      const currentTableTab = ref('table-settings')\n      const currentTableDesign = ref({\n        id: null,\n        chineseName: '',\n        englishName: '',\n        menuOrder: 1,\n        generateData: '0',\n        generateDataCount: 0,\n        functions: {\n          backendAdd: true, backendEdit: true, backendDelete: true,\n          backendDetail: true, backendList: false, batchImport: false,\n          batchExport: false, backendLogin: false, backendRegister: false,\n          backendProfile: false, backendPassword: false,\n          frontendAdd: false, frontendEdit: false, frontendDelete: false,\n          frontendDetail: false, frontendList: false, frontendLogin: false,\n          miniAdd: false, miniEdit: false, miniDelete: false,\n          miniDetail: false, miniList: false\n        },\n        fields: [\n          {\n            id: 1,\n            chineseName: '主键ID',\n            englishName: 'id',\n            type: 'int',\n            controlType: '文本框',\n            required: true,\n            searchable: false,\n            visible: true,\n            existsCheck: false,\n            relatedTable: '',\n            customOptions: ''\n          }\n        ]\n      })\n\n      // 功能选择的响应式数组\n      const backendFunctions = ref([])\n      const frontendFunctions = ref([])\n      const miniFunctions = ref([])\n\n      // 重置字段\n      const resetFields = () => {\n        currentTableDesign.value.fields = [\n          {\n            id: 1,\n            chineseName: '主键ID',\n            englishName: 'id',\n            type: 'int',\n            controlType: '文本框',\n            required: true,\n            searchable: false,\n            visible: true,\n            existsCheck: false\n          }\n        ]\n        ElMessage.success('字段已重置')\n      }\n\n      // AI生成器相关状态\n      const showAiGeneratorDialog = ref(false)\n      const aiGeneratorInput = ref('')\n      const aiGenerationInProgress = ref(false)\n\n      // 模板选择相关状态\n      const showBackendTemplateDialog = ref(false)\n      const showFrontendTemplateDialog = ref(false)\n      const showTemplateDetailDialog = ref(false)\n      const backendTemplates = ref([])\n      const frontendTemplates = ref([])\n      const templatesLoading = ref(false)\n      const currentTemplateDetail = ref(null)\n\n      // 项目生成相关状态\n      const generationInProgress = ref(false)\n      const generationResult = ref(null)\n\n      // SQL脚本生成相关状态\n      const sqlGenerationInProgress = ref(false)\n\n      // 数据脚本生成相关状态\n      const dataGenerationInProgress = ref(false)\n      const dataContent = ref('')\n\n      // 字段设置相关状态\n      const showFieldSettingsDialog = ref(false)\n      const currentFieldSettings = ref(null)\n      const currentFieldIndex = ref(-1)\n      const showInSearchList = ref(false)\n\n      // 关联表选择相关状态\n      const showRelatedTableDialog = ref(false)\n      const availableRelatedTables = ref([])\n\n      // 显示AI生成器弹窗\n      const showAiGeneratorModal = () => {\n        aiGeneratorInput.value = ''\n        showAiGeneratorDialog.value = true\n        nextTick(() => {\n          // 聚焦到输入框\n          const inputElement = document.querySelector('.ai-generator-input')\n          if (inputElement) {\n            inputElement.focus()\n          }\n        })\n      }\n\n      // 隐藏AI生成器弹窗\n      const hideAiGeneratorModal = () => {\n        showAiGeneratorDialog.value = false\n        aiGeneratorInput.value = ''\n      }\n\n      // 确认AI生成\n      const confirmAiGeneration = async () => {\n        const description = aiGeneratorInput.value.trim()\n        if (!description) {\n          ElMessage.warning('请输入描述内容')\n          return\n        }\n\n        // 隐藏弹窗\n        hideAiGeneratorModal()\n\n        // 调用AI生成\n        await doubaoGenerate(description)\n      }\n\n      // 调用豆包AI生成表单\n      const doubaoGenerate = async (str) => {\n        if (aiGenerationInProgress.value) {\n          ElMessage.warning('AI正在生成中，请稍候...')\n          return\n        }\n\n        // 构建prompt\n        const input_text = \"用户:users\\n\" +\n          \"aid|lname|password|role\\n\" +\n          \"用户id|用户名|密码|身份\\n\" +\n          \"int|varchar(50)|varchar(50)|int\\n\" +\n          \"\\n\" +\n          \"学习上面的格式。格式说明如下。\\n\" +\n          \"第1行表中文名称:表英文名称。\\n\" +\n          \"第2行字段列表，字段简写\\n\" +\n          \"第3行字段对应中文\\n\" +\n          \"第4行字段类型。如果是字符型加上长度。\\n\" +\n          \"\\n\" +\n          \"按上面的格式生成下面的内容。不要注释，只返回格式的内容\\n\" + str\n\n        console.log('发送给豆包AI的内容:', input_text)\n\n        const settings = {\n          url: \"https://ark.cn-beijing.volces.com/api/v3/chat/completions\",\n          method: \"POST\",\n          timeout: 30000,\n          headers: {\n            \"Authorization\": \"Bearer 8d71b27a-b4c9-484e-896b-247f7dda5412\",\n            \"Content-Type\": \"application/json\"\n          },\n          data: JSON.stringify({\n            \"model\": \"doubao-1.5-pro-32k-250115\",\n            \"messages\": [\n              {\n                \"role\": \"system\",\n                \"content\": \"你是一个数据库设计专家，专门帮助用户设计数据表结构。请严格按照指定的格式返回结果，不要添加任何额外的说明或注释，表名和字段中不要用下划线，不要大写字母。不要用关键字和保留字\"\n              },\n              {\n                \"role\": \"user\",\n                \"content\": input_text\n              }\n            ]\n          })\n        }\n\n        // 显示生成中状态\n        aiGenerationInProgress.value = true\n        ElMessage.info('正在调用豆包AI生成表结构，请稍候...')\n\n        try {\n          const response = await fetch(settings.url, {\n            method: settings.method,\n            headers: settings.headers,\n            body: settings.data\n          })\n\n          if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`)\n          }\n\n          const result = await response.json()\n          aiGenerationInProgress.value = false\n\n          // 从豆包AI响应中提取内容\n          const generatedContent = result.choices[0].message.content\n          console.log('豆包AI生成的原始内容:', generatedContent)\n\n          // 清理返回内容\n          const cleanedContent = generatedContent\n            .replace(/```[\\s\\S]*?\\n/, '') // 移除开头的markdown代码块标记\n            .replace(/\\n```[\\s\\S]*?$/, '') // 移除结尾的markdown代码块标记\n            .replace(/^\\s+|\\s+$/g, '') // 移除首尾空白\n            .replace(/\\r\\n/g, '\\n') // 统一换行符\n            .replace(/\\r/g, '\\n') // 处理Mac格式换行符\n\n          console.log('清理后的内容:', cleanedContent)\n\n          // 检查内容是否为空\n          if (!cleanedContent || cleanedContent.trim() === '') {\n            throw new Error('豆包AI返回的内容为空')\n          }\n\n          createFormFromAI(cleanedContent)\n          ElMessage.success('AI生成成功！')\n\n        } catch (error) {\n          aiGenerationInProgress.value = false\n          console.error('处理豆包AI响应时出错:', error)\n          ElMessage.error('AI生成失败：' + error.message)\n        }\n      }\n\n      // 解析AI生成的内容并创建表单\n      const createFormFromAI = (content) => {\n        try {\n          console.log('开始解析AI生成的内容:', content)\n\n          // 按行分割内容，移除空行\n          const allLines = content.split('\\n')\n          const lines = allLines.map(line => line.trim()).filter(line => line !== '')\n          console.log('过滤后的有效行:', lines)\n\n          if (lines.length < 4) {\n            throw new Error(`豆包AI返回的格式不完整，需要4行内容，实际只有${lines.length}行`)\n          }\n\n          // 解析第1行：表名\n          const tableNameLine = lines[0]\n          const tableNameMatch = tableNameLine.match(/^(.+):(.+)$/)\n          if (!tableNameMatch) {\n            throw new Error('表名格式不正确，应为：中文名:英文名，实际为：' + tableNameLine)\n          }\n\n          const chineseName = tableNameMatch[1].trim()\n          const englishName = tableNameMatch[2].trim()\n\n          // 解析第2行：英文字段名\n          const englishFields = lines[1].split('|').map(field => field.trim()).filter(field => field !== '')\n\n          // 解析第3行：中文字段名\n          const chineseFields = lines[2].split('|').map(field => field.trim()).filter(field => field !== '')\n\n          // 解析第4行：字段类型\n          const fieldTypes = lines[3].split('|').map(type => type.trim()).filter(type => type !== '')\n\n          // 验证字段数量一致性\n          if (englishFields.length !== chineseFields.length || englishFields.length !== fieldTypes.length) {\n            throw new Error(`字段数量不匹配：英文名${englishFields.length}个，中文名${chineseFields.length}个，类型${fieldTypes.length}个`)\n          }\n\n          // 构建字段数据\n          const fields = []\n          for (let i = 0; i < englishFields.length; i++) {\n            const field = {\n              id: Date.now() + i,\n              chineseName: chineseFields[i],\n              englishName: englishFields[i],\n              type: fieldTypes[i],\n              controlType: inferControlType(fieldTypes[i]),\n              required: true, // AI生成的字段默认必填项选中\n              searchable: i > 0 && i < 3, // 前几个字段设为可搜索\n              visible: true,\n              existsCheck: false\n            }\n            fields.push(field)\n          }\n\n          // 完整替换表结构\n          currentTableDesign.value.chineseName = chineseName\n          currentTableDesign.value.englishName = englishName\n          currentTableDesign.value.fields = fields\n\n          // 确保functions对象存在\n          if (!currentTableDesign.value.functions) {\n            currentTableDesign.value.functions = {}\n          }\n\n          // 默认选中后台功能\n          currentTableDesign.value.functions.backendAdd = true\n          currentTableDesign.value.functions.backendEdit = true\n          currentTableDesign.value.functions.backendDelete = true\n          currentTableDesign.value.functions.backendDetail = true\n          currentTableDesign.value.functions.backendList = false\n\n          console.log('表单创建成功:', {\n            chineseName: chineseName,\n            englishName: englishName,\n            fieldsCount: fields.length\n          })\n\n        } catch (error) {\n          console.error('解析AI内容失败:', error)\n          ElMessage.error('解析AI生成的内容失败：' + error.message)\n        }\n      }\n\n      // 根据字段类型推断控件类型\n      const inferControlType = (fieldType) => {\n        const type = fieldType.toLowerCase()\n\n        if (type.includes('int') || type.includes('bigint')) {\n          return '文本框'\n        } else if (type.includes('decimal') || type.includes('float') || type.includes('double')) {\n          return '文本框'\n        } else if (type.includes('text') || type.includes('longtext')) {\n          return '多行文本'\n        } else if (type.includes('datetime') || type.includes('timestamp')) {\n          return '日期时间'\n        } else if (type.includes('date')) {\n          return '日期选择'\n        } else if (type.includes('varchar') && type.includes('200')) {\n          return '多行文本'\n        } else if (type.includes('varchar')) {\n          return '文本框'\n        } else {\n          return '文本框'\n        }\n      }\n\n      // 模板选择相关函数\n\n      // 显示后台模板选择弹窗\n      const showBackendTemplateSelector = async () => {\n        showBackendTemplateDialog.value = true\n        await loadBackendTemplates()\n      }\n\n      // 显示前台模板选择弹窗\n      const showFrontendTemplateSelector = async () => {\n        showFrontendTemplateDialog.value = true\n        await loadFrontendTemplates()\n      }\n\n      // 加载后台模板\n      const loadBackendTemplates = async () => {\n        try {\n          templatesLoading.value = true\n          const response = await fetch(base+'/small/backend-templates')\n          const result = await response.json()\n          if (result.code === 200) {\n            backendTemplates.value = result.resdata || []\n          } else {\n            ElMessage.error('加载后台模板失败')\n          }\n        } catch (error) {\n          console.error('加载后台模板失败:', error)\n          ElMessage.error('加载后台模板失败')\n        } finally {\n          templatesLoading.value = false\n        }\n      }\n\n      // 加载前台模板\n      const loadFrontendTemplates = async () => {\n        try {\n          templatesLoading.value = true\n          const response = await fetch(base+'/small/frontend-templates')\n          const result = await response.json()\n          if (result.code === 200) {\n            frontendTemplates.value = result.resdata || []\n          } else {\n            ElMessage.error('加载前台模板失败')\n          }\n        } catch (error) {\n          console.error('加载前台模板失败:', error)\n          ElMessage.error('加载前台模板失败')\n        } finally {\n          templatesLoading.value = false\n        }\n      }\n\n      // 选择后台模板\n      const selectBackendTemplate = (template) => {\n        // 设置到项目表单的后台模板字段 - 保存模板ID\n        projectForm.backendTemplate = template.sid\n        showBackendTemplateDialog.value = false\n        ElMessage.success(`已选择后台模板: ${template.sname} (ID: ${template.sid})`)\n      }\n\n      // 选择前台模板\n      const selectFrontendTemplate = (template) => {\n        // 设置到项目表单的前台模板字段 - 保存模板ID\n        projectForm.frontendTemplate = template.sid\n        showFrontendTemplateDialog.value = false\n        ElMessage.success(`已选择前台模板: ${template.sname} (ID: ${template.sid})`)\n      }\n\n      // 查看模板详情\n      const viewTemplateDetail = (template) => {\n        currentTemplateDetail.value = template\n        showTemplateDetailDialog.value = true\n      }\n\n      // 计算属性：是否可以生成项目\n      const canGenerate = computed(() => {\n        return projectForm.name &&\n               projectForm.databaseName &&\n               projectForm.projectCode &&\n               projectTables.value.length > 0 &&\n               !generationInProgress.value\n      })\n\n      // 生成项目\n      const generateProject = async () => {\n        if (!canGenerate.value) {\n          ElMessage.warning('请完善项目配置信息')\n          return\n        }\n\n        try {\n          generationInProgress.value = true\n          generationResult.value = null\n\n          // 构建项目数据\n          const projectData = {\n            projectNumber: projectForm.projectCode,\n            databaseName: projectForm.databaseName,\n            projectName: projectForm.name,\n            packageName: projectForm.packageName || 'com',\n            databaseType: projectForm.databaseType || 'mysql',\n            backendTemplate: projectForm.backendTemplate,\n            frontendTemplate: projectForm.frontendTemplate,\n            tables: projectTables.value.map(table => ({\n              id: table.tid,\n              chineseName: table.tword,\n              englishName: table.tname,\n              functions: table.tgn ? JSON.parse(table.tgn) : {},\n              fields: table.fields || []\n            }))\n          }\n\n          console.log('开始生成项目:', projectData)\n\n          // 调用Java后端API生成项目\n          const response = await fetch(base + '/projects/generate', {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(projectData)\n          })\n\n          const result = await response.json()\n\n          if (result.code === 200) {\n            // 适配后端返回的数据结构\n            const filesData = result.resdata.files && result.resdata.files.data ? result.resdata.files.data : {}\n            const compressionData = result.resdata.compression || null\n\n            generationResult.value = {\n              status: 'success',\n              message: '项目生成成功！',\n              files: filesData,\n              compression: compressionData\n            }\n\n            // 生成项目成功后，自动生成SQL脚本和数据脚本\n            await generateSqlScript()\n            await generateDataScript()\n\n            ElMessage.success('项目生成成功！')\n          } else {\n            throw new Error(result.msg || '项目生成失败')\n          }\n\n        } catch (error) {\n          console.error('项目生成失败:', error)\n          generationResult.value = {\n            status: 'error',\n            message: '项目生成失败：' + error.message,\n            files: null,\n            compression: null\n          }\n          ElMessage.error('项目生成失败：' + error.message)\n        } finally {\n          generationInProgress.value = false\n        }\n      }\n\n      // 生成SQL脚本\n      const generateSqlScript = async () => {\n        if (!currentProjectInfo.value || !projectTables.value || projectTables.value.length === 0) {\n          ElMessage.warning('请先配置项目和数据表')\n          return\n        }\n\n        try {\n          sqlGenerationInProgress.value = true\n\n          // 根据数据库类型生成对应的SQL脚本\n          const databaseType = projectForm.databaseType || 'mysql'\n          let script = ''\n\n          if (databaseType === 'mysql') {\n            script = generateMySqlScript()\n          } else if (databaseType === 'sqlserver') {\n            script = generateSqlServerScript()\n          }\n\n          sqlContent.value = script\n          ElMessage.success('SQL脚本生成成功！')\n\n        } catch (error) {\n          console.error('生成SQL脚本失败:', error)\n          ElMessage.error('生成SQL脚本失败：' + error.message)\n        } finally {\n          sqlGenerationInProgress.value = false\n        }\n      }\n\n      // 生成MySQL脚本\n      const generateMySqlScript = () => {\n        let script = `-- ${projectForm.name || '项目'} 数据库脚本 (MySQL)\\n`\n        script += `-- 数据库名称: ${projectForm.databaseName}\\n`\n        script += `-- 生成时间: ${new Date().toLocaleString()}\\n\\n`\n\n        // 创建数据库\n        script += `CREATE DATABASE IF NOT EXISTS \\`${projectForm.databaseName}\\` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\\n`\n        script += `USE \\`${projectForm.databaseName}\\`;\\n\\n`\n\n        // 为每个表生成建表语句\n        projectTables.value.forEach(table => {\n          script += generateMySqlTableScript(table)\n          script += '\\n'\n        })\n\n        return script\n      }\n\n      // 生成SQL Server脚本\n      const generateSqlServerScript = () => {\n        let script = `-- ${projectForm.name || '项目'} 数据库脚本 (SQL Server)\\n`\n        script += `-- 数据库名称: ${projectForm.databaseName}\\n`\n        script += `-- 生成时间: ${new Date().toLocaleString()}\\n\\n`\n\n        // 创建数据库\n        script += `IF NOT EXISTS (SELECT * FROM sys.databases WHERE name = '${projectForm.databaseName}')\\n`\n        script += `CREATE DATABASE [${projectForm.databaseName}];\\n`\n        script += `GO\\n\\n`\n        script += `USE [${projectForm.databaseName}];\\n`\n        script += `GO\\n\\n`\n\n        // 为每个表生成建表语句\n        projectTables.value.forEach(table => {\n          script += generateSqlServerTableScript(table)\n          script += '\\n'\n        })\n\n        return script\n      }\n\n      // 生成MySQL表脚本\n      const generateMySqlTableScript = (table) => {\n        let script = `-- 表: ${table.tword || table.tname}\\n`\n        script += `DROP TABLE IF EXISTS \\`${table.tname}\\`;\\n`\n        script += `CREATE TABLE \\`${table.tname}\\` (\\n`\n\n        const fields = table.fields || []\n        const fieldScripts = fields.map((field, index) => {\n          let fieldScript = `  \\`${field.englishName}\\` ${convertToMySqlType(field.type)}`\n\n          // 主键处理 - 修复第一个字段的AUTO_INCREMENT问题\n          if (field.englishName.toLowerCase() === 'id' || index === 0) {\n            // 只有int类型的字段才能使用AUTO_INCREMENT\n            if (field.type && field.type.toLowerCase() === 'int') {\n              fieldScript += ' AUTO_INCREMENT NOT NULL'\n            } else {\n              fieldScript += ' NOT NULL'\n            }\n          } else if (field.required) {\n            fieldScript += ' NOT NULL'\n          }\n\n          // 注释\n          if (field.chineseName) {\n            fieldScript += ` COMMENT '${field.chineseName}'`\n          }\n\n          return fieldScript\n        })\n\n        script += fieldScripts.join(',\\n')\n\n        // 主键约束\n        if (fields.length > 0) {\n          const primaryKey = fields[0].englishName\n          script += `,\\n  PRIMARY KEY (\\`${primaryKey}\\`)`\n        }\n\n        script += `\\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='${table.tword || table.tname}';\\n\\n`\n\n        return script\n      }\n\n      // 生成SQL Server表脚本\n      const generateSqlServerTableScript = (table) => {\n        let script = `-- 表: ${table.tword || table.tname}\\n`\n        script += `IF OBJECT_ID('[${table.tname}]', 'U') IS NOT NULL DROP TABLE [${table.tname}];\\n`\n        script += `CREATE TABLE [${table.tname}] (\\n`\n\n        const fields = table.fields || []\n        const fieldScripts = fields.map((field, index) => {\n          let fieldScript = `  [${field.englishName}] ${convertToSqlServerType(field.type)}`\n\n          // 主键处理\n          if (field.englishName.toLowerCase() === 'id' || index === 0) {\n            fieldScript += ' IDENTITY(1,1)'\n          }\n\n          // 非空约束\n          if (field.required) {\n            fieldScript += ' NOT NULL'\n          }\n\n          return fieldScript\n        })\n\n        script += fieldScripts.join(',\\n')\n\n        // 主键约束\n        if (fields.length > 0) {\n          const primaryKey = fields[0].englishName\n          script += `,\\n  CONSTRAINT [PK_${table.tname}] PRIMARY KEY ([${primaryKey}])`\n        }\n\n        script += `\\n);\\n`\n\n        // 添加表注释\n        if (table.tword) {\n          script += `EXEC sp_addextendedproperty 'MS_Description', '${table.tword}', 'SCHEMA', 'dbo', 'TABLE', '${table.tname}';\\n`\n        }\n\n        // 添加字段注释\n        fields.forEach(field => {\n          if (field.chineseName) {\n            script += `EXEC sp_addextendedproperty 'MS_Description', '${field.chineseName}', 'SCHEMA', 'dbo', 'TABLE', '${table.tname}', 'COLUMN', '${field.englishName}';\\n`\n          }\n        })\n\n        script += `GO\\n\\n`\n\n        return script\n      }\n\n      // 转换为MySQL数据类型\n      const convertToMySqlType = (type) => {\n        if (!type) return 'VARCHAR(100)'\n\n        const lowerType = type.toLowerCase()\n        if (lowerType === 'int') return 'INT'\n        if (lowerType.includes('varchar')) return type.toUpperCase()\n        if (lowerType === 'text') return 'TEXT'\n        if (lowerType === 'datetime') return 'DATETIME'\n        if (lowerType.includes('decimal')) return type.toUpperCase()\n\n        return type.toUpperCase()\n      }\n\n      // 转换为SQL Server数据类型\n      const convertToSqlServerType = (type) => {\n        if (!type) return 'NVARCHAR(100)'\n\n        const lowerType = type.toLowerCase()\n        if (lowerType === 'int') return 'INT'\n        if (lowerType.includes('varchar')) {\n          // 将varchar转换为nvarchar\n          return type.replace(/varchar/i, 'NVARCHAR')\n        }\n        if (lowerType === 'text') return 'NTEXT'\n        if (lowerType === 'datetime') return 'DATETIME'\n        if (lowerType.includes('decimal')) return type.toUpperCase()\n\n        return type.toUpperCase()\n      }\n\n      // 导出SQL脚本\n      const exportSqlScript = () => {\n        if (!sqlContent.value || sqlContent.value.trim() === '') {\n          ElMessage.warning('请先生成SQL脚本')\n          return\n        }\n\n        try {\n          const blob = new Blob([sqlContent.value], { type: 'text/plain;charset=utf-8' })\n          const url = window.URL.createObjectURL(blob)\n          const link = document.createElement('a')\n          link.href = url\n\n          const databaseType = projectForm.databaseType || 'mysql'\n          const fileName = `${projectForm.databaseName || 'database'}_${databaseType}.sql`\n          link.download = fileName\n\n          document.body.appendChild(link)\n          link.click()\n          document.body.removeChild(link)\n          window.URL.revokeObjectURL(url)\n\n          ElMessage.success('SQL脚本导出成功！')\n        } catch (error) {\n          console.error('导出SQL脚本失败:', error)\n          ElMessage.error('导出SQL脚本失败：' + error.message)\n        }\n      }\n\n      // 复制SQL脚本到剪切板\n      const copySqlScript = async () => {\n        if (!sqlContent.value || sqlContent.value.trim() === '') {\n          ElMessage.warning('请先生成SQL脚本')\n          return\n        }\n\n        try {\n          await navigator.clipboard.writeText(sqlContent.value)\n          ElMessage.success('SQL脚本已复制到剪切板！')\n        } catch (error) {\n          console.error('复制到剪切板失败:', error)\n          // 降级方案：使用传统的复制方法\n          try {\n            const textArea = document.createElement('textarea')\n            textArea.value = sqlContent.value\n            document.body.appendChild(textArea)\n            textArea.select()\n            document.execCommand('copy')\n            document.body.removeChild(textArea)\n            ElMessage.success('SQL脚本已复制到剪切板！')\n          } catch (fallbackError) {\n            console.error('降级复制方法也失败:', fallbackError)\n            ElMessage.error('复制到剪切板失败，请手动复制')\n          }\n        }\n      }\n\n      // 生成数据脚本\n      const generateDataScript = async () => {\n        if (!currentProjectInfo.value || !projectTables.value || projectTables.value.length === 0) {\n          ElMessage.warning('请先配置项目和数据表')\n          return\n        }\n\n        try {\n          dataGenerationInProgress.value = true\n\n          // 筛选出需要生成数据的表（生成数据条数大于0）\n          const tablesWithData = projectTables.value.filter(table => {\n            const dataCount = parseInt(table.by1 || '0')\n            return dataCount > 0\n          })\n\n          if (tablesWithData.length === 0) {\n            ElMessage.warning('没有设置生成数据条数的表，请先在表设计中设置生成数据条数')\n            return\n          }\n\n          let script = ''\n\n          // 为每个需要生成数据的表生成建表语句和插入数据\n          for (const table of tablesWithData) {\n            script += generateTableWithDataScript(table)\n            script += '\\n'\n          }\n\n          // 添加生成要求说明\n          script += `项目名称是：${projectForm.name || '智慧社区网格化管理系统'}\\n`\n          script += `按要求生成的条数，生成上面所有的数据，数据内容要多一些，数据模拟真实的数据\\n`\n          script += `如果有密码，密码为123456。\\n`\n          script += `时间字段为当前时间\\n`\n          script += `生成的数据为中文，只生成insert into数据，不要注释说明\\n`\n\n          dataContent.value = script\n          ElMessage.success('数据脚本生成成功！')\n\n        } catch (error) {\n          console.error('生成数据脚本失败:', error)\n          ElMessage.error('生成数据脚本失败：' + error.message)\n        } finally {\n          dataGenerationInProgress.value = false\n        }\n      }\n\n      // 生成单个表的建表语句和数据\n      const generateTableWithDataScript = (table) => {\n        const fields = table.fields || []\n        const dataCount = parseInt(table.by1 || '0')\n\n        let script = `create table if NOT EXISTS ${table.tname} \\n(\\n`\n\n        // 生成字段定义\n        const fieldScripts = fields.map((field, index) => {\n          let fieldScript = `${field.englishName}   ${convertToMySqlType(field.type)}`\n\n          // 主键处理 - 修复第一个字段的AUTO_INCREMENT问题\n          if (field.englishName.toLowerCase() === 'id' || index === 0) {\n            // 只有int类型的字段才能使用AUTO_INCREMENT\n            if (field.type && field.type.toLowerCase() === 'int') {\n              fieldScript += ' auto_increment  primary key'\n            } else {\n              fieldScript += ' not null    primary key'\n            }\n          } else if (field.englishName.toLowerCase().includes('account') ||\n                     field.englishName.toLowerCase().includes('username')) {\n            fieldScript += ' not null    primary key'\n          } else if (field.required) {\n            fieldScript += ' not null   '\n          } else {\n            fieldScript += '  null   '\n          }\n\n          // 注释\n          if (field.chineseName) {\n            fieldScript += ` comment '${field.chineseName}'`\n          }\n\n          return fieldScript\n        })\n\n        script += fieldScripts.join(' ,\\n') + ' \\n'\n        script += `) ENGINE=MyISAM AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;\\n\\n`\n\n        // 添加生成数据条数说明\n        if (dataCount > 0) {\n          script += `生成${dataCount}条insert into数据\\n\\n`\n        }\n\n        return script\n      }\n\n      // 复制数据脚本到剪切板\n      const copyDataScript = async () => {\n        if (!dataContent.value || dataContent.value.trim() === '') {\n          ElMessage.warning('请先生成数据脚本')\n          return\n        }\n\n        try {\n          await navigator.clipboard.writeText(dataContent.value)\n          ElMessage.success('数据脚本已复制到剪切板！')\n        } catch (error) {\n          console.error('复制到剪切板失败:', error)\n          // 降级方案：使用传统的复制方法\n          try {\n            const textArea = document.createElement('textarea')\n            textArea.value = dataContent.value\n            document.body.appendChild(textArea)\n            textArea.select()\n            document.execCommand('copy')\n            document.body.removeChild(textArea)\n            ElMessage.success('数据脚本已复制到剪切板！')\n          } catch (fallbackError) {\n            console.error('降级复制方法也失败:', fallbackError)\n            ElMessage.error('复制到剪切板失败，请手动复制')\n          }\n        }\n      }\n\n      // 下载项目文件\n      const downloadProject = (filePath) => {\n        if (filePath) {\n          // 从filePath中提取文件名\n          const fileName = filePath.split('/').pop()\n          // 使用新的下载接口\n          const downloadUrl = `${base}/projects/download?fileName=${encodeURIComponent(fileName)}`\n          window.open(downloadUrl, '_blank')\n        } else {\n          ElMessage.error('下载链接无效')\n        }\n      }\n\n      // 获取模板图片URL\n      const getTemplateImageUrl = (memo4) => {\n        if (!memo4) return ''\n\n        // 从HTML中提取图片URL\n        const imgMatch = memo4.match(/<img[^>]+src=\"([^\"]+)\"/)\n        if (imgMatch && imgMatch[1]) {\n          return imgMatch[1]\n        }\n\n        return ''\n      }\n\n      // 处理图片加载错误\n      const handleImageError = (event) => {\n        event.target.style.display = 'none'\n        const parent = event.target.parentElement\n        if (parent) {\n          parent.innerHTML = '<div class=\"no-image\"><span>图片加载失败</span></div>'\n        }\n      }\n\n      // 获取表格字段数量\n      const getTableFieldCount = (table) => {\n        // 如果有字段数据，返回字段数量\n        if (table.fields && Array.isArray(table.fields)) {\n          return table.fields.length\n        }\n        // 否则返回0\n        return 0\n      }\n\n      // 获取表格功能列表\n      const getTableFunctions = (table) => {\n        if (!table.tgn) return []\n\n        try {\n          const functions = JSON.parse(table.tgn)\n          const activeFunctions = []\n\n          // 检查各种功能是否启用\n          if (functions.backendAdd) activeFunctions.push('后台添加')\n          if (functions.backendEdit) activeFunctions.push('后台修改')\n          if (functions.backendDelete) activeFunctions.push('后台删除')\n          if (functions.backendDetail) activeFunctions.push('后台详情')\n          if (functions.backendList) activeFunctions.push('后台列表')\n          if (functions.frontendAdd) activeFunctions.push('前台添加')\n          if (functions.frontendEdit) activeFunctions.push('前台修改')\n          if (functions.frontendDelete) activeFunctions.push('前台删除')\n          if (functions.miniAdd) activeFunctions.push('小程序添加')\n\n          return activeFunctions.slice(0, 3) // 只显示前3个功能\n        } catch (error) {\n          console.error('解析表功能配置失败:', error)\n          return []\n        }\n      }\n\n      // 打开表设计弹窗\n      const openTableDesignModal = (table = null) => {\n        if (table) {\n          // 编辑现有表\n          currentTableDesign.value = {\n            id: table.tid,\n            chineseName: table.tword || '',\n            englishName: table.tname || '',\n            menuOrder: parseInt(table.by2 || '1'),\n            generateData: '0',\n            generateDataCount: parseInt(table.by1 || '0'),\n            functions: table.tgn ? JSON.parse(table.tgn) : {\n              backendAdd: true, backendEdit: true, backendDelete: true,\n              backendDetail: true, backendList: false, batchImport: false,\n              batchExport: false, backendLogin: false, backendRegister: false,\n              backendProfile: false, backendPassword: false,\n              frontendAdd: false, frontendEdit: false, frontendDelete: false,\n              frontendDetail: false, frontendList: false, frontendLogin: false,\n              miniAdd: false, miniEdit: false, miniDelete: false,\n              miniDetail: false, miniList: false\n            },\n            fields: table.fields || [\n              {\n                id: 1,\n                chineseName: '主键ID',\n                englishName: 'id',\n                type: 'int',\n                controlType: '文本框',\n                required: true,\n                searchable: false,\n                visible: true,\n                existsCheck: false\n              }\n            ]\n          }\n        } else {\n          // 创建新表\n          resetTableDesign()\n        }\n        showTableDesignModal.value = true\n      }\n\n      // 重置表设计数据\n      const resetTableDesign = () => {\n        currentTableDesign.value = {\n          id: null,\n          chineseName: '',\n          englishName: '',\n          menuOrder: 1,\n          generateData: '0',\n          generateDataCount: 0,\n          functions: {\n            backendAdd: true, backendEdit: true, backendDelete: true,\n            backendDetail: true, backendList: false, batchImport: false,\n            batchExport: false, backendLogin: false, backendRegister: false,\n            backendProfile: false, backendPassword: false,\n            frontendAdd: false, frontendEdit: false, frontendDelete: false,\n            frontendDetail: false, frontendList: false, frontendLogin: false,\n            miniAdd: false, miniEdit: false, miniDelete: false,\n            miniDetail: false, miniList: false\n          },\n          fields: [\n            {\n              id: 1,\n              chineseName: '主键ID',\n              englishName: 'id',\n              type: 'int',\n              controlType: '文本框',\n              required: true,\n              searchable: false,\n              visible: true,\n              existsCheck: false\n            }\n          ]\n        }\n      }\n\n      // 关闭表设计弹窗\n      const closeTableDesignModal = () => {\n        showTableDesignModal.value = false\n        currentTableTab.value = 'table-settings'\n      }\n\n      // 表设计弹窗Tab切换功能\n      const switchTableTab = (tabId) => {\n        currentTableTab.value = tabId\n      }\n\n      // 添加字段到设计中\n      const addNewFieldToDesign = () => {\n        const newField = {\n          id: Date.now(),\n          chineseName: '',\n          englishName: '',\n          type: 'varchar(100)',\n          controlType: '文本框',\n          required: true, // 默认必填项选中\n          searchable: false,\n          visible: true,\n          existsCheck: false,\n          relatedTable: '', // 关联表\n          customOptions: '' // 自定义选项\n        }\n        currentTableDesign.value.fields.push(newField)\n      }\n\n      // 清空所有字段\n      const clearAllFields = async () => {\n        try {\n          await ElMessageBox.confirm(\n            '确定要清空所有字段吗？此操作不可撤销。',\n            '清空字段',\n            {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              type: 'warning',\n            }\n          )\n\n          currentTableDesign.value.fields = []\n          ElMessage.success('字段清空成功')\n        } catch (error) {\n          if (error !== 'cancel') {\n            console.error('清空字段失败:', error)\n          }\n        }\n      }\n\n      // 删除设计中的字段\n      const deleteFieldFromDesign = async (index) => {\n        try {\n          await ElMessageBox.confirm(\n            '确定要删除这个字段吗？此操作不可撤销。',\n            '删除字段',\n            {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              type: 'warning',\n            }\n          )\n\n          currentTableDesign.value.fields.splice(index, 1)\n          ElMessage.success('字段删除成功')\n        } catch (error) {\n          if (error !== 'cancel') {\n            console.error('删除字段失败:', error)\n          }\n        }\n      }\n\n      // 上移字段\n      const moveFieldUp = (index) => {\n        if (index > 0) {\n          const field = currentTableDesign.value.fields.splice(index, 1)[0]\n          currentTableDesign.value.fields.splice(index - 1, 0, field)\n        }\n      }\n\n      // 下移字段\n      const moveFieldDown = (index) => {\n        if (index < currentTableDesign.value.fields.length - 1) {\n          const field = currentTableDesign.value.fields.splice(index, 1)[0]\n          currentTableDesign.value.fields.splice(index + 1, 0, field)\n        }\n      }\n\n      // 编辑字段设置\n      const editFieldSettings = (field, index) => {\n        currentFieldSettings.value = { ...field }\n        currentFieldIndex.value = index\n        showInSearchList.value = field.searchable || false\n        showFieldSettingsDialog.value = true\n      }\n\n      // 关闭字段设置弹窗\n      const closeFieldSettingsDialog = () => {\n        showFieldSettingsDialog.value = false\n        currentFieldSettings.value = null\n        currentFieldIndex.value = -1\n      }\n\n      // 保存字段设置\n      const saveFieldSettings = () => {\n        if (currentFieldIndex.value >= 0 && currentFieldSettings.value) {\n          // 更新字段数据\n          const field = currentTableDesign.value.fields[currentFieldIndex.value]\n          field.relatedTable = currentFieldSettings.value.relatedTable\n          field.customOptions = currentFieldSettings.value.customOptions\n          field.searchable = showInSearchList.value\n\n          ElMessage.success('字段设置保存成功')\n          closeFieldSettingsDialog()\n        }\n      }\n\n      // 显示关联表选择器\n      const showRelatedTableSelector = async () => {\n        if (!currentProjectInfo.value || !currentProjectInfo.value.pid) {\n          ElMessage.warning('请先保存项目信息')\n          return\n        }\n\n        try {\n          // 加载项目的所有表\n          await loadAvailableRelatedTables()\n          showRelatedTableDialog.value = true\n        } catch (error) {\n          console.error('加载关联表失败:', error)\n          ElMessage.error('加载关联表失败')\n        }\n      }\n\n      // 加载可用的关联表\n      const loadAvailableRelatedTables = async () => {\n        try {\n          const url = base + \"/tables/list?currentPage=1&pageSize=100\"\n          const res = await request.post(url, { pid: currentProjectInfo.value.pid })\n\n          if (res.code === 200 && res.resdata) {\n            availableRelatedTables.value = res.resdata.map(table => {\n              // 获取表的第二个字段作为显示名称，如果第二个字段是密码则使用第三个字段\n              let displayName = table.tword || table.tname\n              if (table.fields && table.fields.length > 1) {\n                const secondField = table.fields[1]\n                if (secondField && secondField.chineseName && !secondField.chineseName.includes('密码')) {\n                  displayName = secondField.chineseName\n                } else if (table.fields.length > 2) {\n                  const thirdField = table.fields[2]\n                  if (thirdField && thirdField.chineseName) {\n                    displayName = thirdField.chineseName\n                  }\n                }\n              }\n\n              return {\n                primaryKey: table.fields && table.fields.length > 0 ? table.fields[0].englishName : 'id',\n                displayName: displayName,\n                tableName: table.tname,\n                linkValue: '1',\n                selected: false\n              }\n            })\n          }\n        } catch (error) {\n          console.error('加载关联表失败:', error)\n          throw error\n        }\n      }\n\n      // 关闭关联表选择弹窗\n      const closeRelatedTableDialog = () => {\n        showRelatedTableDialog.value = false\n        availableRelatedTables.value = []\n      }\n\n      // 确认关联表选择\n      const confirmRelatedTableSelection = () => {\n        const selectedTables = availableRelatedTables.value.filter(table => table.selected)\n        if (selectedTables.length === 0) {\n          ElMessage.warning('请至少选择一个关联表')\n          return\n        }\n\n        // 构建关联表字符串，格式：doro,dbid,dormitory,1\n        const relatedTableStr = selectedTables.map(table =>\n          `${table.primaryKey},${table.displayName},${table.tableName},${table.linkValue}`\n        ).join(';')\n\n        if (currentFieldSettings.value) {\n          currentFieldSettings.value.relatedTable = relatedTableStr\n        }\n\n        closeRelatedTableDialog()\n        ElMessage.success('关联表设置成功')\n      }\n\n      // 保存表设计\n      const saveTableDesign = async () => {\n        // 验证必填字段\n        if (!currentTableDesign.value.chineseName.trim()) {\n          ElMessage.warning('请输入表的中文名称')\n          return\n        }\n\n        if (!currentTableDesign.value.englishName.trim()) {\n          ElMessage.warning('请输入表的英文名称')\n          return\n        }\n\n        // 验证字段\n        for (let field of currentTableDesign.value.fields) {\n          if (!field.chineseName.trim() || !field.englishName.trim()) {\n            ElMessage.warning('请完善所有字段的中文名称和英文名称')\n            return\n          }\n        }\n\n        // 确保有项目ID\n        if (!currentProjectInfo.value || !currentProjectInfo.value.pid) {\n          ElMessage.error('项目信息缺失，请重新配置项目')\n          return\n        }\n\n        try {\n          // 准备表数据\n          const tableData = {\n            pid: currentProjectInfo.value.pid,\n            tword: currentTableDesign.value.chineseName,\n            tname: currentTableDesign.value.englishName,\n            tgn: JSON.stringify(currentTableDesign.value.functions),\n            tlist: '',\n            vlist: '',\n            by1: (currentTableDesign.value.generateDataCount || 0).toString(),\n            by2: (currentTableDesign.value.menuOrder || 1).toString()\n          }\n\n          // 如果是编辑模式，添加tid\n          if (currentTableDesign.value.id) {\n            tableData.tid = currentTableDesign.value.id\n          }\n\n          // 保存表信息\n          const isUpdate = currentTableDesign.value.id\n          const url = base + (isUpdate ? \"/tables/update\" : \"/tables/add\")\n          const res = await request.post(url, tableData)\n\n          if (res.code === 200) {\n            let tableId = currentTableDesign.value.id\n            if (!isUpdate && res.resdata) {\n              // 后端返回的是新创建的表ID\n              tableId = res.resdata\n              currentTableDesign.value.id = tableId\n            }\n\n            // 如果有字段，保存字段信息\n            if (currentTableDesign.value.fields.length > 0) {\n              // 先删除原有字段（如果是更新模式）\n              if (isUpdate) {\n                try {\n                  await request.post(base + \"/mores/deleteByTid\", { tid: tableId })\n                } catch (error) {\n                  console.warn('删除原有字段失败:', error)\n                }\n              }\n\n              // 保存新字段\n              for (let field of currentTableDesign.value.fields) {\n                const fieldData = {\n                  tid: tableId,\n                  moname: field.englishName,\n                  mozname: field.chineseName,\n                  motype: field.type,\n                  moflag: field.controlType,\n                  molong: '',\n                  moyz: field.required ? '1' : '0',\n                  mobt: field.searchable ? '1' : '0',\n                  by1: field.visible ? '1' : '0',\n                  by2: field.existsCheck ? '1' : '0',\n                  by3: field.relatedTable || '',\n                  by4: field.customOptions || '',\n                  by5: '',\n                  by6: ''\n                }\n\n                try {\n                  await request.post(base + \"/mores/add\", fieldData)\n                } catch (error) {\n                  console.error('保存字段失败:', field, error)\n                }\n              }\n            }\n\n            ElMessage.success('表设计保存成功')\n            closeTableDesignModal()\n            // 重新加载项目表单列表\n            if (currentProjectInfo.value.pid) {\n              loadProjectTables(currentProjectInfo.value.pid)\n            }\n          } else {\n            ElMessage.error(res.msg || '表保存失败')\n          }\n        } catch (error) {\n          console.error('保存表设计失败:', error)\n          ElMessage.error('保存表设计失败，请检查网络连接')\n        }\n      }\n\n      // 删除表\n      const deleteTable = async (table) => {\n        try {\n          await ElMessageBox.confirm(\n            `确定要删除表 \"${table.tword || table.tname}\" 吗？`,\n            '确认删除',\n            {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              type: 'warning',\n            }\n          )\n\n          const url = base + \"/tables/del?id=\" + table.tid\n          const res = await request.post(url, {})\n\n          if (res.code === 200) {\n            ElMessage.success('删除成功')\n            // 重新加载表单列表\n            if (currentProjectInfo.value && currentProjectInfo.value.pid) {\n              loadProjectTables(currentProjectInfo.value.pid)\n            }\n          } else {\n            ElMessage.error(res.msg || '删除失败')\n          }\n        } catch (error) {\n          if (error !== 'cancel') {\n            console.error('删除表失败:', error)\n            ElMessage.error('删除失败，请检查网络连接')\n          }\n        }\n      }\n\n      // 下一步操作\n      const nextStep = async () => {\n        if (activeTab.value === 'project') {\n          // 从项目配置到表单设计\n          if (!selectedProject.value) {\n            ElMessage.warning('请先选择项目类型')\n            return\n          }\n          if (!projectForm.name) {\n            ElMessage.warning('请输入项目中文名称')\n            return\n          }\n\n          // 验证必填字段\n          if (projectForm.databaseMode === 'new') {\n            if (!projectForm.projectCode) {\n              ElMessage.warning('请输入项目编号')\n              return\n            }\n            if (!projectForm.databaseName) {\n              ElMessage.warning('请输入数据库名称')\n              return\n            }\n          } else if (projectForm.databaseMode === 'existing') {\n            if (!projectForm.selectedDatabase) {\n              ElMessage.warning('请选择已有数据库')\n              return\n            }\n          }\n\n          // 设置当前项目信息\n          currentProjectInfo.value = {\n            name: projectForm.name,\n            projectCode: projectForm.projectCode,\n            databaseName: projectForm.databaseName,\n            pid: projectForm.selectedDatabase || null\n          }\n\n          // 保存或更新项目到数据库\n          const result = await saveOrUpdateProject()\n          if (!result.success) return\n\n          // 更新项目ID\n          if (result.projectId) {\n            currentProjectInfo.value.pid = result.projectId\n          }\n\n          // 如果是已有数据库模式，加载项目表单\n          if (projectForm.databaseMode === 'existing' && projectForm.selectedDatabase) {\n            loadProjectTables(projectForm.selectedDatabase)\n          } else if (currentProjectInfo.value.pid) {\n            // 新建项目也加载表单（可能为空）\n            loadProjectTables(currentProjectInfo.value.pid)\n          }\n\n          activeTab.value = 'form'\n          ElMessage.success('项目配置保存成功，请继续设计表单')\n        } else if (activeTab.value === 'form') {\n          // 从表单设计到项目生成\n          if (!projectTables.value || projectTables.value.length === 0) {\n            ElMessage.warning('请先设计数据表，不能为空')\n            return\n          }\n          activeTab.value = 'generate'\n          ElMessage.success('表单设计完成，请生成项目')\n        }\n      }\n\n      const handleTabClick = (tab) => {\n        console.log('切换到标签页:', tab.props.name)\n      }\n\n      // 组件挂载时检查登录状态\n      onMounted(() => {\n        checkLoginStatus()\n      })\n\n      return {\n        activeTab,\n        logLevel,\n        isLoggedIn,\n        loginDialogVisible,\n        loginLoading,\n        loginFormRef,\n        userInfo,\n        loginForm,\n        loginRules,\n        selectedProject,\n        projectsLoading,\n        availableDatabases,\n        projectForm,\n        currentProjectInfo,\n        projectTables,\n        projectTablesLoading,\n        showTableDesignModal,\n        currentTableTab,\n        currentTableDesign,\n        formFields,\n        tableData,\n        sqlContent,\n        logs,\n        handleTabClick,\n        handleLogin,\n        handleUserCommand,\n        handleLogout,\n        selectProject,\n        getProjectName,\n        openTemplateModal,\n        openFrontTemplateModal,\n        handleDatabaseModeChange,\n        handleProjectSelect,\n        loadProjectTables,\n        openTableDesignModal,\n        closeTableDesignModal,\n        switchTableTab,\n        addNewFieldToDesign,\n        clearAllFields,\n        deleteFieldFromDesign,\n        moveFieldUp,\n        moveFieldDown,\n        saveTableDesign,\n        deleteTable,\n        nextStep,\n        getTableFieldCount,\n        getTableFunctions,\n        backendFunctions,\n        frontendFunctions,\n        miniFunctions,\n        resetFields,\n        showAiGeneratorModal,\n\n        // AI生成器相关\n        showAiGeneratorDialog,\n        aiGeneratorInput,\n        aiGenerationInProgress,\n        hideAiGeneratorModal,\n        confirmAiGeneration,\n        doubaoGenerate,\n        createFormFromAI,\n        inferControlType,\n\n        // 模板选择相关\n        showBackendTemplateDialog,\n        showFrontendTemplateDialog,\n        showTemplateDetailDialog,\n        backendTemplates,\n        frontendTemplates,\n        templatesLoading,\n        currentTemplateDetail,\n        showBackendTemplateSelector,\n        showFrontendTemplateSelector,\n        selectBackendTemplate,\n        selectFrontendTemplate,\n        viewTemplateDetail,\n        getTemplateImageUrl,\n        handleImageError,\n\n        // 项目生成相关\n        generationInProgress,\n        generationResult,\n        canGenerate,\n        generateProject,\n        downloadProject,\n\n        // SQL脚本生成相关\n        sqlGenerationInProgress,\n        generateSqlScript,\n        exportSqlScript,\n        copySqlScript,\n\n        // 数据脚本生成相关\n        dataGenerationInProgress,\n        dataContent,\n        generateDataScript,\n        copyDataScript,\n\n        // 字段设置相关\n        showFieldSettingsDialog,\n        currentFieldSettings,\n        showInSearchList,\n        editFieldSettings,\n        closeFieldSettingsDialog,\n        saveFieldSettings,\n        showRelatedTableSelector,\n\n        // 关联表选择相关\n        showRelatedTableDialog,\n        availableRelatedTables,\n        closeRelatedTableDialog,\n        confirmRelatedTableSelection\n      }\n    }\n  }\n</script>"], "mappings": ";;;;;;;;;AA4tCE,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAO,QAAS,KAAI;AACjE,SAASC,SAAS,EAAEC,YAAW,QAAS,cAAa;AACrD,OAAOC,OAAO,IAAIC,IAAG,QAAS,qBAAoB;AAClD,SACEC,OAAO,EAAEC,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEC,SAAS,EAAEC,YAAY,EAAEC,OAAO,EAC5DC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,GAAG,EAAEC,OAAO,EAC7DC,IAAI,EAAEC,SAAS,EAAEC,OAAO,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAM,QAClD,yBAAwB;AAE/B,eAAe;EACbC,IAAI,EAAE,eAAe;EACrBC,UAAU,EAAE;IACVvB,OAAO;IAAEC,MAAM;IAAEC,IAAI;IAAEC,GAAG;IAAEC,SAAS;IAAEC,YAAY;IAAEC,OAAO;IAC5DC,IAAI;IAAEC,IAAI;IAAEC,QAAQ;IAAEC,MAAM;IAAEC,QAAQ;IAAEC,OAAO;IAAEC,GAAG;IAAEC,OAAO;IAC7DC,IAAI;IAAEC,SAAS;IAAEC,OAAO;IAAEC,OAAO;IAAEC,KAAK;IAAEC,KAAK;IAAEC;EACnD,CAAC;EACDG,KAAKA,CAAA,EAAG;IACN,MAAMC,SAAQ,GAAIlC,GAAG,CAAC,SAAS;IAC/B,MAAMmC,QAAO,GAAInC,GAAG,CAAC,KAAK;;IAE1B;IACA,MAAMoC,UAAS,GAAIpC,GAAG,CAAC,KAAK;IAC5B,MAAMqC,kBAAiB,GAAIrC,GAAG,CAAC,KAAK;IACpC,MAAMsC,YAAW,GAAItC,GAAG,CAAC,KAAK;IAC9B,MAAMuC,YAAW,GAAIvC,GAAG,CAAC,IAAI;;IAE7B;IACA,MAAMwC,QAAO,GAAIvC,QAAQ,CAAC;MACxBwC,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE;IACb,CAAC;;IAED;IACA,MAAMC,SAAQ,GAAI1C,QAAQ,CAAC;MACzBwC,QAAQ,EAAE,EAAE;MACZG,QAAQ,EAAE;IACZ,CAAC;;IAED;IACA,MAAMC,UAAS,GAAI;MACjBJ,QAAQ,EAAE,CACR;QAAEK,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,EACtD;MACDJ,QAAQ,EAAE,CACR;QAAEE,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO;IAExD;;IAEA;IACA,MAAMC,eAAc,GAAIjD,GAAG,CAAC,EAAE;IAC9B,MAAMkD,eAAc,GAAIlD,GAAG,CAAC,KAAK;IACjC,MAAMmD,kBAAiB,GAAInD,GAAG,CAAC,CAC7B;MAAEoD,KAAK,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAS,EAC7B;IAED,MAAMC,WAAU,GAAIrD,QAAQ,CAAC;MAC3BsD,YAAY,EAAE,OAAO;MACrBC,YAAY,EAAE,KAAK;MACnBC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,gBAAgB,EAAE,EAAE;MACpB5B,IAAI,EAAE,EAAE;MACR6B,WAAW,EAAE,KAAK;MAClBC,eAAe,EAAE,EAAE;MACnBC,gBAAgB,EAAE,EAAE;MACpBC,KAAK,EAAE,GAAG;MACVC,MAAM,EAAE,GAAG;MACXC,UAAU,EAAE,EAAE;MACdC,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE,EAAE;MACbC,cAAc,EAAE,EAAE;MAClBC,WAAW,EAAE;IACf,CAAC;;IAED;IACA,MAAMC,kBAAiB,GAAIvE,GAAG,CAAC,IAAI;IACnC,MAAMwE,aAAY,GAAIxE,GAAG,CAAC,EAAE;IAC5B,MAAMyE,oBAAmB,GAAIzE,GAAG,CAAC,KAAK;IAEtC,MAAM0E,UAAS,GAAI1E,GAAG,CAAC,CACrB;MAAE+B,IAAI,EAAE,IAAI;MAAE4C,IAAI,EAAE;IAAO,CAAC,EAC5B;MAAE5C,IAAI,EAAE,MAAM;MAAE4C,IAAI,EAAE;IAAS,CAAC,EAChC;MAAE5C,IAAI,EAAE,OAAO;MAAE4C,IAAI,EAAE;IAAS,CAAC,EACjC;MAAE5C,IAAI,EAAE,YAAY;MAAE4C,IAAI,EAAE;IAAO,EACpC;IAED,MAAMC,SAAQ,GAAI5E,GAAG,CAAC,CACpB;MAAE+B,IAAI,EAAE,MAAM;MAAE8C,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAE,KAAK;MAAEC,UAAU,EAAE;IAAsB,CAAC,EAC7E;MAAEhD,IAAI,EAAE,MAAM;MAAE8C,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAE,KAAK;MAAEC,UAAU,EAAE;IAAsB,CAAC,EAC7E;MAAEhD,IAAI,EAAE,YAAY;MAAE8C,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAE,KAAK;MAAEC,UAAU,EAAE;IAAsB,EACnF;IAED,MAAMC,UAAS,GAAIhF,GAAG,CAAC,EAAE;IAEzB,MAAMiF,IAAG,GAAIjF,GAAG,CAAC,CACf;MAAEkF,IAAI,EAAE,qBAAqB;MAAEC,KAAK,EAAE,MAAM;MAAEpC,OAAO,EAAE;IAAY,CAAC,EACpE;MAAEmC,IAAI,EAAE,qBAAqB;MAAEC,KAAK,EAAE,SAAS;MAAEpC,OAAO,EAAE;IAAU,CAAC,EACrE;MAAEmC,IAAI,EAAE,qBAAqB;MAAEC,KAAK,EAAE,SAAS;MAAEpC,OAAO,EAAE;IAAe,CAAC,EAC1E;MAAEmC,IAAI,EAAE,qBAAqB;MAAEC,KAAK,EAAE,OAAO;MAAEpC,OAAO,EAAE;IAAgB,EACzE;;IAED;IACA,MAAMqC,SAAQ,GAAIA,CAACrD,IAAI,EAAEqB,KAAK,EAAEiC,IAAI,KAAK;MACvC,MAAMC,OAAM,GAAI,IAAIC,IAAI,CAAC;MACzBD,OAAO,CAACE,OAAO,CAACF,OAAO,CAACG,OAAO,CAAC,IAAKJ,IAAG,GAAI,EAAC,GAAI,EAAC,GAAI,EAAC,GAAI,IAAK;MAChEK,QAAQ,CAACC,MAAK,GAAI,GAAG5D,IAAI,IAAIqB,KAAK,YAAYkC,OAAO,CAACM,WAAW,CAAC,CAAC,SAAQ;IAC7E;IAEA,MAAMC,SAAQ,GAAK9D,IAAI,IAAK;MAC1B,MAAM+D,MAAK,GAAI/D,IAAG,GAAI,GAAE;MACxB,MAAMgE,EAAC,GAAIL,QAAQ,CAACC,MAAM,CAACK,KAAK,CAAC,GAAG;MACpC,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIF,EAAE,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;QAClC,IAAIE,CAAA,GAAIJ,EAAE,CAACE,CAAC;QACZ,OAAOE,CAAC,CAACC,MAAM,CAAC,CAAC,MAAM,GAAG,EAAED,CAAA,GAAIA,CAAC,CAACE,SAAS,CAAC,CAAC,EAAEF,CAAC,CAACD,MAAM;QACvD,IAAIC,CAAC,CAACG,OAAO,CAACR,MAAM,MAAM,CAAC,EAAE,OAAOK,CAAC,CAACE,SAAS,CAACP,MAAM,CAACI,MAAM,EAAEC,CAAC,CAACD,MAAM;MACzE;MACA,OAAO,IAAG;IACZ;IAEA,MAAMK,YAAW,GAAKxE,IAAI,IAAK;MAC7B2D,QAAQ,CAACC,MAAK,GAAI,GAAG5D,IAAI,mDAAkD;IAC7E;;IAEA;IACA,MAAMyE,gBAAe,GAAIA,CAAA,KAAM;MAC7B;MACA,MAAMC,WAAU,GAAIC,cAAc,CAACC,OAAO,CAAC,MAAM;MACjD,MAAMC,gBAAe,GAAIF,cAAc,CAACC,OAAO,CAAC,WAAW;MAE3D,IAAIF,WAAU,IAAKG,gBAAgB,EAAE;QACnC,IAAI;UACFC,IAAI,CAACC,KAAK,CAACL,WAAW,GAAE;UACxBjE,QAAQ,CAACC,QAAO,GAAImE,gBAAe;UACnCpE,QAAQ,CAACE,SAAQ,GAAI,IAAI6C,IAAI,CAAC,CAAC,CAACwB,cAAc,CAAC;UAC/C3E,UAAU,CAACgB,KAAI,GAAI,IAAG;UACtB;QACF,EAAE,OAAO4D,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK;QAChD;MACF;;MAEA;MACA,MAAME,SAAQ,GAAIrB,SAAS,CAAC,mBAAmB;MAC/C,IAAIqB,SAAS,EAAE;QACb,IAAI;UACF,MAAMC,QAAO,GAAIN,IAAI,CAACC,KAAK,CAACM,kBAAkB,CAACF,SAAS,CAAC;UACzD1E,QAAQ,CAACC,QAAO,GAAI0E,QAAQ,CAAC1E,QAAO;UACpCD,QAAQ,CAACE,SAAQ,GAAIyE,QAAQ,CAACzE,SAAQ;UACtCN,UAAU,CAACgB,KAAI,GAAI,IAAG;QACxB,EAAE,OAAO4D,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK;UACtCT,YAAY,CAAC,mBAAmB;QAClC;MACF,OAAO;QACLlE,kBAAkB,CAACe,KAAI,GAAI,IAAG;MAChC;IACF;;IAEA;IACA,MAAMiE,WAAU,GAAI,MAAAA,CAAA,KAAY;MAC9B,IAAI,CAAC9E,YAAY,CAACa,KAAK,EAAE;;MAEzB;MACA,IAAI,CAACT,SAAS,CAACF,QAAQ,EAAE;QACvBpC,SAAS,CAACiH,OAAO,CAAC,QAAQ;QAC1B;MACF;MACA,IAAI,CAAC3E,SAAS,CAACC,QAAQ,EAAE;QACvBvC,SAAS,CAACiH,OAAO,CAAC,OAAO;QACzB;MACF;MAEA,IAAI;QACFhF,YAAY,CAACc,KAAI,GAAI,IAAG;;QAExB;QACA,MAAMmE,GAAE,GAAI/G,IAAG,GAAI,cAAa;QAChC,MAAMgH,SAAQ,GAAI;UAChBC,KAAK,EAAE9E,SAAS,CAACF,QAAQ;UACzBiF,GAAG,EAAE/E,SAAS,CAACC;QACjB;QAEA,MAAM+E,GAAE,GAAI,MAAMpH,OAAO,CAACqH,IAAI,CAACL,GAAG,EAAEC,SAAS;QAC7ClF,YAAY,CAACc,KAAI,GAAI,KAAI;QAEzB,IAAIuE,GAAG,CAACE,IAAG,IAAK,GAAG,EAAE;UACnBZ,OAAO,CAACa,GAAG,CAAC,OAAO,EAAEjB,IAAI,CAACkB,SAAS,CAACJ,GAAG,CAACK,OAAO,CAAC;;UAEhD;UACAtB,cAAc,CAACuB,OAAO,CAAC,MAAM,EAAEpB,IAAI,CAACkB,SAAS,CAACJ,GAAG,CAACK,OAAO,CAAC;UAC1DtB,cAAc,CAACuB,OAAO,CAAC,WAAW,EAAEN,GAAG,CAACK,OAAO,CAACP,KAAK;UACrDf,cAAc,CAACuB,OAAO,CAAC,MAAM,EAAE,KAAK;;UAEpC;UACAzF,QAAQ,CAACC,QAAO,GAAIkF,GAAG,CAACK,OAAO,CAACP,KAAI;UACpCjF,QAAQ,CAACE,SAAQ,GAAI,IAAI6C,IAAI,CAAC,CAAC,CAACwB,cAAc,CAAC;;UAE/C;UACA,MAAMI,QAAO,GAAI;YACf1E,QAAQ,EAAEkF,GAAG,CAACK,OAAO,CAACP,KAAK;YAC3B/E,SAAS,EAAEF,QAAQ,CAACE,SAAS;YAC7BwF,MAAM,EAAEP,GAAG,CAACK,OAAO,CAACG;UACtB;UACA/C,SAAS,CAAC,mBAAmB,EAAEgD,kBAAkB,CAACvB,IAAI,CAACkB,SAAS,CAACZ,QAAQ,CAAC,CAAC,EAAE,EAAE;UAE/E/E,UAAU,CAACgB,KAAI,GAAI,IAAG;UACtBf,kBAAkB,CAACe,KAAI,GAAI,KAAI;;UAE/B;UACAT,SAAS,CAACF,QAAO,GAAI,EAAC;UACtBE,SAAS,CAACC,QAAO,GAAI,EAAC;UAEtBvC,SAAS,CAACgI,OAAO,CAAC,OAAO;QAC3B,OAAO;UACLhI,SAAS,CAAC2G,KAAK,CAACW,GAAG,CAACW,GAAE,IAAK,MAAM;QACnC;MACF,EAAE,OAAOtB,KAAK,EAAE;QACd1E,YAAY,CAACc,KAAI,GAAI,KAAI;QACzB6D,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK;QAC5B3G,SAAS,CAAC2G,KAAK,CAAC,cAAc;MAChC;IACF;;IAEA;IACA,MAAMuB,iBAAgB,GAAKC,OAAO,IAAK;MACrC,IAAIA,OAAM,KAAM,QAAQ,EAAE;QACxBC,YAAY,CAAC;MACf;IACF;;IAEA;IACA,MAAMA,YAAW,GAAIA,CAAA,KAAM;MACzB;MACAlC,YAAY,CAAC,mBAAmB;;MAEhC;MACAG,cAAc,CAACgC,UAAU,CAAC,MAAM;MAChChC,cAAc,CAACgC,UAAU,CAAC,WAAW;MACrChC,cAAc,CAACgC,UAAU,CAAC,MAAM;;MAEhC;MACAtG,UAAU,CAACgB,KAAI,GAAI,KAAI;MACvBZ,QAAQ,CAACC,QAAO,GAAI,EAAC;MACrBD,QAAQ,CAACE,SAAQ,GAAI,EAAC;MACtBL,kBAAkB,CAACe,KAAI,GAAI,IAAG;MAE9B/C,SAAS,CAACgI,OAAO,CAAC,OAAO;IAC3B;;IAEA;IACA,MAAMM,aAAY,GAAKC,WAAW,IAAK;MACrC3F,eAAe,CAACG,KAAI,GAAIwF,WAAU;MAClC3B,OAAO,CAACa,GAAG,CAAC,SAAS,EAAEc,WAAW;IACpC;;IAEA;IACA,MAAMC,cAAa,GAAKD,WAAW,IAAK;MACtC,MAAME,YAAW,GAAI;QACnB,sBAAsB,EAAE,wBAAwB;QAChD,wBAAwB,EAAE,kBAAkB;QAC5C,gBAAgB,EAAE,kBAAkB;QACpC,SAAS,EAAE;MACb;MACA,OAAOA,YAAY,CAACF,WAAW,KAAKA,WAAU;IAChD;;IAEA;IACA,MAAMG,iBAAgB,GAAIA,CAAA,KAAM;MAC9B9B,OAAO,CAACa,GAAG,CAAC,YAAY;MACxBkB,2BAA2B,CAAC;IAC9B;IAEA,MAAMC,sBAAqB,GAAIA,CAAA,KAAM;MACnChC,OAAO,CAACa,GAAG,CAAC,YAAY;MACxBoB,4BAA4B,CAAC;IAC/B;;IAEA;IACA,MAAMC,wBAAuB,GAAKC,IAAI,IAAK;MACzC,IAAIA,IAAG,KAAM,UAAU,EAAE;QACvBC,YAAY,CAAC;MACf,OAAO;QACL;QACAlG,kBAAkB,CAACC,KAAI,GAAI,CAAC;UAAEA,KAAK,EAAE,EAAE;UAAEC,IAAI,EAAE;QAAS,CAAC;QACzDC,WAAW,CAACK,gBAAe,GAAI,EAAC;MAClC;IACF;;IAEA;IACA,MAAM0F,YAAW,GAAI,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACFnG,eAAe,CAACE,KAAI,GAAI,IAAG;QAC3B,MAAMmE,GAAE,GAAI/G,IAAG,GAAI,4CAA2C;QAC9D,MAAM8I,MAAK,GAAI,CAEf;QAEA,MAAM3B,GAAE,GAAI,MAAMpH,OAAO,CAACqH,IAAI,CAACL,GAAG,EAAE;UAAE+B;QAAO,CAAC;QAE9C,IAAI3B,GAAG,CAACE,IAAG,KAAM,GAAG,EAAE;UACpB,MAAM0B,QAAO,GAAI5B,GAAG,CAACK,OAAM,IAAK,EAAC;UACjC7E,kBAAkB,CAACC,KAAI,GAAI,CACzB;YAAEA,KAAK,EAAE,EAAE;YAAEC,IAAI,EAAE;UAAS,CAAC,EAC7B,GAAGkG,QAAQ,CAACC,GAAG,CAACC,OAAM,KAAM;YAC1BrG,KAAK,EAAEqG,OAAO,CAACC,GAAG;YAClBrG,IAAI,EAAE,GAAGoG,OAAO,CAACE,GAAG,KAAKF,OAAO,CAACG,MAAM,KAAKH,OAAO,CAACI,KAAK;UAC3D,CAAC,CAAC,EACJ;QACF,OAAO;UACLxJ,SAAS,CAAC2G,KAAK,CAAC,UAAU;QAC5B;MACF,EAAE,OAAOA,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC3G,SAAS,CAAC2G,KAAK,CAAC,kBAAkB;MACpC,UAAU;QACR9D,eAAe,CAACE,KAAI,GAAI,KAAI;MAC9B;IACF;;IAEA;IACA,MAAM0G,mBAAkB,GAAI,MAAOC,SAAS,IAAK;MAC/C,IAAI,CAACA,SAAS,EAAE;MAEhB,IAAI;QACF,MAAMxC,GAAE,GAAI/G,IAAG,GAAI,mBAAkB,GAAIuJ,SAAS;QAGlD,MAAMpC,GAAE,GAAI,MAAMpH,OAAO,CAACqH,IAAI,CAACL,GAAG,EAAE,CAAC,CAAC;QAEtC,IAAII,GAAG,CAACE,IAAG,KAAM,GAAG,EAAE;UACpB,MAAM4B,OAAM,GAAI9B,GAAG,CAACK,OAAM;UAC1B;UACA1E,WAAW,CAACG,WAAU,GAAIgG,OAAO,CAACE,GAAE,IAAK,EAAC;UAC1CrG,WAAW,CAACI,YAAW,GAAI+F,OAAO,CAACG,MAAK,IAAK,EAAC;UAC9CtG,WAAW,CAACvB,IAAG,GAAI0H,OAAO,CAACI,KAAI,IAAK,EAAC;UACrCvG,WAAW,CAACC,YAAW,GAAIkG,OAAO,CAACO,KAAI,IAAK,OAAM;UAClD1G,WAAW,CAACO,eAAc,GAAI4F,OAAO,CAACQ,GAAE,IAAK,EAAC;UAC9C3G,WAAW,CAACQ,gBAAe,GAAI2F,OAAO,CAACS,GAAE,IAAK,EAAC;UAC/C5G,WAAW,CAACS,KAAI,GAAI0F,OAAO,CAACU,GAAE,IAAK,GAAE;UACrC7G,WAAW,CAACU,MAAK,GAAIyF,OAAO,CAACW,GAAE,IAAK,GAAE;UACtC9G,WAAW,CAACW,UAAS,GAAIwF,OAAO,CAACY,GAAE,IAAK,EAAC;;UAEzC;UACA,IAAIZ,OAAO,CAACa,GAAG,EAAE;YACf,MAAMC,WAAU,GAAId,OAAO,CAACa,GAAG,CAACtE,KAAK,CAAC,GAAG;YACzC1C,WAAW,CAACY,OAAM,GAAIqG,WAAW,CAAC,CAAC,KAAK,EAAC;YACzCjH,WAAW,CAACa,SAAQ,GAAIoG,WAAW,CAAC,CAAC,KAAK,EAAC;YAC3CjH,WAAW,CAACc,SAAQ,GAAImG,WAAW,CAAC,CAAC,KAAK,EAAC;YAC3CjH,WAAW,CAACe,cAAa,GAAIkG,WAAW,CAAC,CAAC,KAAK,EAAC;UAClD;UAEAlK,SAAS,CAACgI,OAAO,CAAC,UAAU;QAC9B,OAAO;UACLhI,SAAS,CAAC2G,KAAK,CAAC,UAAU;QAC5B;MACF,EAAE,OAAOA,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC3G,SAAS,CAAC2G,KAAK,CAAC,kBAAkB;MACpC;IACF;;IAEA;IACA,MAAMwD,mBAAkB,GAAI,MAAAA,CAAA,KAAY;MACtC,IAAI;QACF;QACA,MAAMD,WAAU,GAAI,CAClBjH,WAAW,CAACY,OAAO,EACnBZ,WAAW,CAACa,SAAS,EACrBb,WAAW,CAACc,SAAS,EACrBd,WAAW,CAACe,cAAa,CAC1B,CAACoG,IAAI,CAAC,GAAG;QAEV,MAAMC,WAAU,GAAI;UAClBC,KAAK,EAAE1H,eAAe,CAACG,KAAK;UAC5B4G,KAAK,EAAE1G,WAAW,CAACC,YAAY;UAC/BqH,KAAK,EAAEtH,WAAW,CAACE,YAAW,KAAM,KAAI,GAAI,GAAE,GAAI,GAAG;UACrDmG,GAAG,EAAErG,WAAW,CAACG,WAAW;UAC5BmG,MAAM,EAAEtG,WAAW,CAACI,YAAY;UAChCmG,KAAK,EAAEvG,WAAW,CAACvB,IAAI;UACvBkI,GAAG,EAAE3G,WAAW,CAACO,eAAe;UAChCqG,GAAG,EAAE5G,WAAW,CAACQ,gBAAgB;UACjCwG,GAAG,EAAEC,WAAW;UAChBJ,GAAG,EAAE7G,WAAW,CAACS,KAAK;UACtBqG,GAAG,EAAE9G,WAAW,CAACU,MAAM;UACvBqG,GAAG,EAAE/G,WAAW,CAACW,UAAU;UAC3B4G,GAAG,EAAEvH,WAAW,CAACgB,WAAW;UAC5BmD,KAAK,EAAEjF,QAAQ,CAACC;QAClB;;QAEA;QACA,MAAMqI,QAAO,GAAIvG,kBAAkB,CAACnB,KAAI,IAAKmB,kBAAkB,CAACnB,KAAK,CAACsG,GAAE;QACxE,IAAInC,GAAG,EAAEI,GAAE;QAEX,IAAImD,QAAQ,EAAE;UACZ;UACAJ,WAAW,CAAChB,GAAE,GAAInF,kBAAkB,CAACnB,KAAK,CAACsG,GAAE;UAC7CnC,GAAE,GAAI/G,IAAG,GAAI,kBAAiB;UAC9BmH,GAAE,GAAI,MAAMpH,OAAO,CAACqH,IAAI,CAACL,GAAG,EAAEmD,WAAW;QAC3C,OAAO;UACL;UACAnD,GAAE,GAAI/G,IAAG,GAAI,eAAc;UAC3BmH,GAAE,GAAI,MAAMpH,OAAO,CAACqH,IAAI,CAACL,GAAG,EAAEmD,WAAW;QAC3C;QAEA,IAAI/C,GAAG,CAACE,IAAG,KAAM,GAAG,EAAE;UAAA,IAAAkD,YAAA,EAAAC,qBAAA;UACpB,MAAMjI,OAAM,GAAI+H,QAAO,GAAI,QAAO,GAAI,QAAO;UAC7CzK,SAAS,CAACgI,OAAO,CAACtF,OAAO;;UAEzB;UACA,IAAI,CAAC+H,QAAO,IAAKnD,GAAG,CAACK,OAAM,IAAKL,GAAG,CAACK,OAAO,CAAC0B,GAAG,EAAE;YAC/CnF,kBAAkB,CAACnB,KAAI,GAAI;cACzB,GAAGmB,kBAAkB,CAACnB,KAAK;cAC3BsG,GAAG,EAAE/B,GAAG,CAACK,OAAO,CAAC0B;YACnB;UACF;UAEA,OAAO;YAAErB,OAAO,EAAE,IAAI;YAAE0B,SAAS,EAAE,EAAAgB,YAAA,GAAApD,GAAG,CAACK,OAAO,cAAA+C,YAAA,uBAAXA,YAAA,CAAarB,GAAE,OAAAsB,qBAAA,GAAKzG,kBAAkB,CAACnB,KAAK,cAAA4H,qBAAA,uBAAxBA,qBAAA,CAA0BtB,GAAE;UAAE;QACvF,OAAO;UACLrJ,SAAS,CAAC2G,KAAK,CAACW,GAAG,CAACW,GAAE,IAAK,QAAQ;UACnC,OAAO;YAAED,OAAO,EAAE;UAAM;QAC1B;MACF,EAAE,OAAOrB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9B3G,SAAS,CAAC2G,KAAK,CAAC,gBAAgB;QAChC,OAAO;UAAEqB,OAAO,EAAE;QAAM;MAC1B;IACF;;IAEA;IACA,MAAM4C,iBAAgB,GAAI,MAAOlB,SAAS,IAAK;MAC7C,IAAI;QACFtF,oBAAoB,CAACrB,KAAI,GAAI,IAAG;QAChC,MAAMmE,GAAE,GAAI/G,IAAG,GAAI,0CAAyC;QAC5D,MAAM8I,MAAK,GAAI;UACbI,GAAG,EAAEK;QACP;QAEA,MAAMpC,GAAE,GAAI,MAAMpH,OAAO,CAACqH,IAAI,CAACL,GAAG,EAAE+B,MAAM;QAE1C,IAAI3B,GAAG,CAACE,IAAG,KAAM,GAAG,EAAE;UACpBrD,aAAa,CAACpB,KAAI,GAAIuE,GAAG,CAACK,OAAM,IAAK,EAAC;;UAEtC;UACA,KAAK,IAAIkD,KAAI,IAAK1G,aAAa,CAACpB,KAAK,EAAE;YACrC,IAAI;cACF,MAAM+H,SAAQ,GAAI3K,IAAG,GAAI,wCAAuC;cAChE,MAAM4K,SAAQ,GAAI,MAAM7K,OAAO,CAACqH,IAAI,CAACuD,SAAS,EAAE;gBAAEE,GAAG,EAAEH,KAAK,CAACG;cAAI,CAAC;cAElE,IAAID,SAAS,CAACvD,IAAG,KAAM,GAAE,IAAKuD,SAAS,CAACpD,OAAO,EAAE;gBAC/C;gBACAkD,KAAK,CAACrG,MAAK,GAAIuG,SAAS,CAACpD,OAAO,CAACwB,GAAG,CAAC8B,KAAI,KAAM;kBAC7CnD,EAAE,EAAEmD,KAAK,CAACC,GAAG;kBACbF,GAAG,EAAEC,KAAK,CAACD,GAAG;kBACdG,WAAW,EAAEF,KAAK,CAACG,OAAO;kBAC1BC,WAAW,EAAEJ,KAAK,CAACK,MAAM;kBACzBhH,IAAI,EAAE2G,KAAK,CAACM,MAAM;kBAClBC,WAAW,EAAEP,KAAK,CAACQ,MAAM;kBACzBhJ,QAAQ,EAAEwI,KAAK,CAACS,IAAG,KAAM,GAAG;kBAC5BC,UAAU,EAAEV,KAAK,CAACW,IAAG,KAAM,GAAG;kBAC9BC,OAAO,EAAEZ,KAAK,CAACrB,GAAE,KAAM,GAAG;kBAC1BkC,WAAW,EAAEb,KAAK,CAACpB,GAAE,KAAM,GAAG;kBAC9BkC,YAAY,EAAEd,KAAK,CAAChB,GAAE,IAAK,EAAE;kBAAE;kBAC/B+B,aAAa,EAAEf,KAAK,CAACnB,GAAE,IAAK,EAAC,CAAE;gBACjC,CAAC,CAAC;;gBAEF;gBACAe,KAAK,CAACoB,iBAAgB,GAAIC,QAAQ,CAACrB,KAAK,CAACjB,GAAE,IAAK,GAAG;cACrD,OAAO;gBACLiB,KAAK,CAACrG,MAAK,GAAI,EAAC;cAClB;YACF,EAAE,OAAOmC,KAAK,EAAE;cACdC,OAAO,CAACuF,IAAI,CAAC,UAAU,EAAEtB,KAAK,CAACuB,KAAK,EAAEzF,KAAK;cAC3CkE,KAAK,CAACrG,MAAK,GAAI,EAAC;YAClB;UACF;UAEAoC,OAAO,CAACa,GAAG,CAAC,WAAW,EAAEtD,aAAa,CAACpB,KAAK;QAC9C,OAAO;UACL/C,SAAS,CAAC2G,KAAK,CAAC,UAAU;QAC5B;MACF,EAAE,OAAOA,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC3G,SAAS,CAAC2G,KAAK,CAAC,kBAAkB;MACpC,UAAU;QACRvC,oBAAoB,CAACrB,KAAI,GAAI,KAAI;MACnC;IACF;;IAEA;IACA,MAAMsJ,oBAAmB,GAAI1M,GAAG,CAAC,KAAK;IACtC,MAAM2M,eAAc,GAAI3M,GAAG,CAAC,gBAAgB;IAC5C,MAAM4M,kBAAiB,GAAI5M,GAAG,CAAC;MAC7BmI,EAAE,EAAE,IAAI;MACRqD,WAAW,EAAE,EAAE;MACfE,WAAW,EAAE,EAAE;MACfmB,SAAS,EAAE,CAAC;MACZC,YAAY,EAAE,GAAG;MACjBR,iBAAiB,EAAE,CAAC;MACpBS,SAAS,EAAE;QACTC,UAAU,EAAE,IAAI;QAAEC,WAAW,EAAE,IAAI;QAAEC,aAAa,EAAE,IAAI;QACxDC,aAAa,EAAE,IAAI;QAAEC,WAAW,EAAE,KAAK;QAAEC,WAAW,EAAE,KAAK;QAC3DC,WAAW,EAAE,KAAK;QAAEC,YAAY,EAAE,KAAK;QAAEC,eAAe,EAAE,KAAK;QAC/DC,cAAc,EAAE,KAAK;QAAEC,eAAe,EAAE,KAAK;QAC7CC,WAAW,EAAE,KAAK;QAAEC,YAAY,EAAE,KAAK;QAAEC,cAAc,EAAE,KAAK;QAC9DC,cAAc,EAAE,KAAK;QAAEC,YAAY,EAAE,KAAK;QAAEC,aAAa,EAAE,KAAK;QAChEC,OAAO,EAAE,KAAK;QAAEC,QAAQ,EAAE,KAAK;QAAEC,UAAU,EAAE,KAAK;QAClDC,UAAU,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAC/B,CAAC;MACDxJ,MAAM,EAAE,CACN;QACEsD,EAAE,EAAE,CAAC;QACLqD,WAAW,EAAE,MAAM;QACnBE,WAAW,EAAE,IAAI;QACjB/G,IAAI,EAAE,KAAK;QACXkH,WAAW,EAAE,KAAK;QAClB/I,QAAQ,EAAE,IAAI;QACdkJ,UAAU,EAAE,KAAK;QACjBE,OAAO,EAAE,IAAI;QACbC,WAAW,EAAE,KAAK;QAClBC,YAAY,EAAE,EAAE;QAChBC,aAAa,EAAE;MACjB;IAEJ,CAAC;;IAED;IACA,MAAMiC,gBAAe,GAAItO,GAAG,CAAC,EAAE;IAC/B,MAAMuO,iBAAgB,GAAIvO,GAAG,CAAC,EAAE;IAChC,MAAMwO,aAAY,GAAIxO,GAAG,CAAC,EAAE;;IAE5B;IACA,MAAMyO,WAAU,GAAIA,CAAA,KAAM;MACxB7B,kBAAkB,CAACxJ,KAAK,CAACyB,MAAK,GAAI,CAChC;QACEsD,EAAE,EAAE,CAAC;QACLqD,WAAW,EAAE,MAAM;QACnBE,WAAW,EAAE,IAAI;QACjB/G,IAAI,EAAE,KAAK;QACXkH,WAAW,EAAE,KAAK;QAClB/I,QAAQ,EAAE,IAAI;QACdkJ,UAAU,EAAE,KAAK;QACjBE,OAAO,EAAE,IAAI;QACbC,WAAW,EAAE;MACf,EACF;MACA9L,SAAS,CAACgI,OAAO,CAAC,OAAO;IAC3B;;IAEA;IACA,MAAMqG,qBAAoB,GAAI1O,GAAG,CAAC,KAAK;IACvC,MAAM2O,gBAAe,GAAI3O,GAAG,CAAC,EAAE;IAC/B,MAAM4O,sBAAqB,GAAI5O,GAAG,CAAC,KAAK;;IAExC;IACA,MAAM6O,yBAAwB,GAAI7O,GAAG,CAAC,KAAK;IAC3C,MAAM8O,0BAAyB,GAAI9O,GAAG,CAAC,KAAK;IAC5C,MAAM+O,wBAAuB,GAAI/O,GAAG,CAAC,KAAK;IAC1C,MAAMgP,gBAAe,GAAIhP,GAAG,CAAC,EAAE;IAC/B,MAAMiP,iBAAgB,GAAIjP,GAAG,CAAC,EAAE;IAChC,MAAMkP,gBAAe,GAAIlP,GAAG,CAAC,KAAK;IAClC,MAAMmP,qBAAoB,GAAInP,GAAG,CAAC,IAAI;;IAEtC;IACA,MAAMoP,oBAAmB,GAAIpP,GAAG,CAAC,KAAK;IACtC,MAAMqP,gBAAe,GAAIrP,GAAG,CAAC,IAAI;;IAEjC;IACA,MAAMsP,uBAAsB,GAAItP,GAAG,CAAC,KAAK;;IAEzC;IACA,MAAMuP,wBAAuB,GAAIvP,GAAG,CAAC,KAAK;IAC1C,MAAMwP,WAAU,GAAIxP,GAAG,CAAC,EAAE;;IAE1B;IACA,MAAMyP,uBAAsB,GAAIzP,GAAG,CAAC,KAAK;IACzC,MAAM0P,oBAAmB,GAAI1P,GAAG,CAAC,IAAI;IACrC,MAAM2P,iBAAgB,GAAI3P,GAAG,CAAC,CAAC,CAAC;IAChC,MAAM4P,gBAAe,GAAI5P,GAAG,CAAC,KAAK;;IAElC;IACA,MAAM6P,sBAAqB,GAAI7P,GAAG,CAAC,KAAK;IACxC,MAAM8P,sBAAqB,GAAI9P,GAAG,CAAC,EAAE;;IAErC;IACA,MAAM+P,oBAAmB,GAAIA,CAAA,KAAM;MACjCpB,gBAAgB,CAACvL,KAAI,GAAI,EAAC;MAC1BsL,qBAAqB,CAACtL,KAAI,GAAI,IAAG;MACjCjD,QAAQ,CAAC,MAAM;QACb;QACA,MAAM6P,YAAW,GAAItK,QAAQ,CAACuK,aAAa,CAAC,qBAAqB;QACjE,IAAID,YAAY,EAAE;UAChBA,YAAY,CAACE,KAAK,CAAC;QACrB;MACF,CAAC;IACH;;IAEA;IACA,MAAMC,oBAAmB,GAAIA,CAAA,KAAM;MACjCzB,qBAAqB,CAACtL,KAAI,GAAI,KAAI;MAClCuL,gBAAgB,CAACvL,KAAI,GAAI,EAAC;IAC5B;;IAEA;IACA,MAAMgN,mBAAkB,GAAI,MAAAA,CAAA,KAAY;MACtC,MAAMC,WAAU,GAAI1B,gBAAgB,CAACvL,KAAK,CAACkN,IAAI,CAAC;MAChD,IAAI,CAACD,WAAW,EAAE;QAChBhQ,SAAS,CAACiH,OAAO,CAAC,SAAS;QAC3B;MACF;;MAEA;MACA6I,oBAAoB,CAAC;;MAErB;MACA,MAAMI,cAAc,CAACF,WAAW;IAClC;;IAEA;IACA,MAAME,cAAa,GAAI,MAAOC,GAAG,IAAK;MACpC,IAAI5B,sBAAsB,CAACxL,KAAK,EAAE;QAChC/C,SAAS,CAACiH,OAAO,CAAC,gBAAgB;QAClC;MACF;;MAEA;MACA,MAAMmJ,UAAS,GAAI,YAAW,GAC5B,2BAA0B,GAC1B,kBAAiB,GACjB,mCAAkC,GAClC,IAAG,GACH,mBAAkB,GAClB,mBAAkB,GAClB,gBAAe,GACf,aAAY,GACZ,uBAAsB,GACtB,IAAG,GACH,+BAA8B,GAAID,GAAE;MAEtCvJ,OAAO,CAACa,GAAG,CAAC,aAAa,EAAE2I,UAAU;MAErC,MAAMC,QAAO,GAAI;QACfnJ,GAAG,EAAE,2DAA2D;QAChEoJ,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,6CAA6C;UAC9D,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEjK,IAAI,CAACkB,SAAS,CAAC;UACnB,OAAO,EAAE,2BAA2B;UACpC,UAAU,EAAE,CACV;YACE,MAAM,EAAE,QAAQ;YAChB,SAAS,EAAE;UACb,CAAC,EACD;YACE,MAAM,EAAE,MAAM;YACd,SAAS,EAAE0I;UACb;QAEJ,CAAC;MACH;;MAEA;MACA7B,sBAAsB,CAACxL,KAAI,GAAI,IAAG;MAClC/C,SAAS,CAAC0Q,IAAI,CAAC,sBAAsB;MAErC,IAAI;QACF,MAAMC,QAAO,GAAI,MAAMC,KAAK,CAACP,QAAQ,CAACnJ,GAAG,EAAE;UACzCoJ,MAAM,EAAED,QAAQ,CAACC,MAAM;UACvBE,OAAO,EAAEH,QAAQ,CAACG,OAAO;UACzBK,IAAI,EAAER,QAAQ,CAACI;QACjB,CAAC;QAED,IAAI,CAACE,QAAQ,CAACG,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBJ,QAAQ,CAAClM,MAAM,EAAE;QAC1D;QAEA,MAAMuM,MAAK,GAAI,MAAML,QAAQ,CAACM,IAAI,CAAC;QACnC1C,sBAAsB,CAACxL,KAAI,GAAI,KAAI;;QAEnC;QACA,MAAMmO,gBAAe,GAAIF,MAAM,CAACG,OAAO,CAAC,CAAC,CAAC,CAACzO,OAAO,CAAC0O,OAAM;QACzDxK,OAAO,CAACa,GAAG,CAAC,cAAc,EAAEyJ,gBAAgB;;QAE5C;QACA,MAAMG,cAAa,GAAIH,gBAAe,CACnCI,OAAO,CAAC,eAAe,EAAE,EAAE,EAAE;QAAA,CAC7BA,OAAO,CAAC,gBAAgB,EAAE,EAAE,EAAE;QAAA,CAC9BA,OAAO,CAAC,YAAY,EAAE,EAAE,EAAE;QAAA,CAC1BA,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE;QAAA,CACvBA,OAAO,CAAC,KAAK,EAAE,IAAI,GAAE;;QAExB1K,OAAO,CAACa,GAAG,CAAC,SAAS,EAAE4J,cAAc;;QAErC;QACA,IAAI,CAACA,cAAa,IAAKA,cAAc,CAACpB,IAAI,CAAC,MAAM,EAAE,EAAE;UACnD,MAAM,IAAIc,KAAK,CAAC,aAAa;QAC/B;QAEAQ,gBAAgB,CAACF,cAAc;QAC/BrR,SAAS,CAACgI,OAAO,CAAC,SAAS;MAE7B,EAAE,OAAOrB,KAAK,EAAE;QACd4H,sBAAsB,CAACxL,KAAI,GAAI,KAAI;QACnC6D,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK;QACnC3G,SAAS,CAAC2G,KAAK,CAAC,SAAQ,GAAIA,KAAK,CAACjE,OAAO;MAC3C;IACF;;IAEA;IACA,MAAM6O,gBAAe,GAAKH,OAAO,IAAK;MACpC,IAAI;QACFxK,OAAO,CAACa,GAAG,CAAC,cAAc,EAAE2J,OAAO;;QAEnC;QACA,MAAMI,QAAO,GAAIJ,OAAO,CAACzL,KAAK,CAAC,IAAI;QACnC,MAAM8L,KAAI,GAAID,QAAQ,CAACrI,GAAG,CAACuI,IAAG,IAAKA,IAAI,CAACzB,IAAI,CAAC,CAAC,CAAC,CAAC0B,MAAM,CAACD,IAAG,IAAKA,IAAG,KAAM,EAAE;QAC1E9K,OAAO,CAACa,GAAG,CAAC,UAAU,EAAEgK,KAAK;QAE7B,IAAIA,KAAK,CAAC5L,MAAK,GAAI,CAAC,EAAE;UACpB,MAAM,IAAIkL,KAAK,CAAC,2BAA2BU,KAAK,CAAC5L,MAAM,GAAG;QAC5D;;QAEA;QACA,MAAM+L,aAAY,GAAIH,KAAK,CAAC,CAAC;QAC7B,MAAMI,cAAa,GAAID,aAAa,CAACE,KAAK,CAAC,aAAa;QACxD,IAAI,CAACD,cAAc,EAAE;UACnB,MAAM,IAAId,KAAK,CAAC,yBAAwB,GAAIa,aAAa;QAC3D;QAEA,MAAMzG,WAAU,GAAI0G,cAAc,CAAC,CAAC,CAAC,CAAC5B,IAAI,CAAC;QAC3C,MAAM5E,WAAU,GAAIwG,cAAc,CAAC,CAAC,CAAC,CAAC5B,IAAI,CAAC;;QAE3C;QACA,MAAM8B,aAAY,GAAIN,KAAK,CAAC,CAAC,CAAC,CAAC9L,KAAK,CAAC,GAAG,CAAC,CAACwD,GAAG,CAAC8B,KAAI,IAAKA,KAAK,CAACgF,IAAI,CAAC,CAAC,CAAC,CAAC0B,MAAM,CAAC1G,KAAI,IAAKA,KAAI,KAAM,EAAE;;QAEjG;QACA,MAAM+G,aAAY,GAAIP,KAAK,CAAC,CAAC,CAAC,CAAC9L,KAAK,CAAC,GAAG,CAAC,CAACwD,GAAG,CAAC8B,KAAI,IAAKA,KAAK,CAACgF,IAAI,CAAC,CAAC,CAAC,CAAC0B,MAAM,CAAC1G,KAAI,IAAKA,KAAI,KAAM,EAAE;;QAEjG;QACA,MAAMgH,UAAS,GAAIR,KAAK,CAAC,CAAC,CAAC,CAAC9L,KAAK,CAAC,GAAG,CAAC,CAACwD,GAAG,CAAC7E,IAAG,IAAKA,IAAI,CAAC2L,IAAI,CAAC,CAAC,CAAC,CAAC0B,MAAM,CAACrN,IAAG,IAAKA,IAAG,KAAM,EAAE;;QAE1F;QACA,IAAIyN,aAAa,CAAClM,MAAK,KAAMmM,aAAa,CAACnM,MAAK,IAAKkM,aAAa,CAAClM,MAAK,KAAMoM,UAAU,CAACpM,MAAM,EAAE;UAC/F,MAAM,IAAIkL,KAAK,CAAC,cAAcgB,aAAa,CAAClM,MAAM,QAAQmM,aAAa,CAACnM,MAAM,OAAOoM,UAAU,CAACpM,MAAM,GAAG;QAC3G;;QAEA;QACA,MAAMrB,MAAK,GAAI,EAAC;QAChB,KAAK,IAAIoB,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAImM,aAAa,CAAClM,MAAM,EAAED,CAAC,EAAE,EAAE;UAC7C,MAAMqF,KAAI,GAAI;YACZnD,EAAE,EAAE5C,IAAI,CAACgN,GAAG,CAAC,IAAItM,CAAC;YAClBuF,WAAW,EAAE6G,aAAa,CAACpM,CAAC,CAAC;YAC7ByF,WAAW,EAAE0G,aAAa,CAACnM,CAAC,CAAC;YAC7BtB,IAAI,EAAE2N,UAAU,CAACrM,CAAC,CAAC;YACnB4F,WAAW,EAAE2G,gBAAgB,CAACF,UAAU,CAACrM,CAAC,CAAC,CAAC;YAC5CnD,QAAQ,EAAE,IAAI;YAAE;YAChBkJ,UAAU,EAAE/F,CAAA,GAAI,KAAKA,CAAA,GAAI,CAAC;YAAE;YAC5BiG,OAAO,EAAE,IAAI;YACbC,WAAW,EAAE;UACf;UACAtH,MAAM,CAAC4N,IAAI,CAACnH,KAAK;QACnB;;QAEA;QACAsB,kBAAkB,CAACxJ,KAAK,CAACoI,WAAU,GAAIA,WAAU;QACjDoB,kBAAkB,CAACxJ,KAAK,CAACsI,WAAU,GAAIA,WAAU;QACjDkB,kBAAkB,CAACxJ,KAAK,CAACyB,MAAK,GAAIA,MAAK;;QAEvC;QACA,IAAI,CAAC+H,kBAAkB,CAACxJ,KAAK,CAAC2J,SAAS,EAAE;UACvCH,kBAAkB,CAACxJ,KAAK,CAAC2J,SAAQ,GAAI,CAAC;QACxC;;QAEA;QACAH,kBAAkB,CAACxJ,KAAK,CAAC2J,SAAS,CAACC,UAAS,GAAI,IAAG;QACnDJ,kBAAkB,CAACxJ,KAAK,CAAC2J,SAAS,CAACE,WAAU,GAAI,IAAG;QACpDL,kBAAkB,CAACxJ,KAAK,CAAC2J,SAAS,CAACG,aAAY,GAAI,IAAG;QACtDN,kBAAkB,CAACxJ,KAAK,CAAC2J,SAAS,CAACI,aAAY,GAAI,IAAG;QACtDP,kBAAkB,CAACxJ,KAAK,CAAC2J,SAAS,CAACK,WAAU,GAAI,KAAI;QAErDnG,OAAO,CAACa,GAAG,CAAC,SAAS,EAAE;UACrB0D,WAAW,EAAEA,WAAW;UACxBE,WAAW,EAAEA,WAAW;UACxBgH,WAAW,EAAE7N,MAAM,CAACqB;QACtB,CAAC;MAEH,EAAE,OAAOc,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC3G,SAAS,CAAC2G,KAAK,CAAC,cAAa,GAAIA,KAAK,CAACjE,OAAO;MAChD;IACF;;IAEA;IACA,MAAMyP,gBAAe,GAAKG,SAAS,IAAK;MACtC,MAAMhO,IAAG,GAAIgO,SAAS,CAACC,WAAW,CAAC;MAEnC,IAAIjO,IAAI,CAACkO,QAAQ,CAAC,KAAK,KAAKlO,IAAI,CAACkO,QAAQ,CAAC,QAAQ,CAAC,EAAE;QACnD,OAAO,KAAI;MACb,OAAO,IAAIlO,IAAI,CAACkO,QAAQ,CAAC,SAAS,KAAKlO,IAAI,CAACkO,QAAQ,CAAC,OAAO,KAAKlO,IAAI,CAACkO,QAAQ,CAAC,QAAQ,CAAC,EAAE;QACxF,OAAO,KAAI;MACb,OAAO,IAAIlO,IAAI,CAACkO,QAAQ,CAAC,MAAM,KAAKlO,IAAI,CAACkO,QAAQ,CAAC,UAAU,CAAC,EAAE;QAC7D,OAAO,MAAK;MACd,OAAO,IAAIlO,IAAI,CAACkO,QAAQ,CAAC,UAAU,KAAKlO,IAAI,CAACkO,QAAQ,CAAC,WAAW,CAAC,EAAE;QAClE,OAAO,MAAK;MACd,OAAO,IAAIlO,IAAI,CAACkO,QAAQ,CAAC,MAAM,CAAC,EAAE;QAChC,OAAO,MAAK;MACd,OAAO,IAAIlO,IAAI,CAACkO,QAAQ,CAAC,SAAS,KAAKlO,IAAI,CAACkO,QAAQ,CAAC,KAAK,CAAC,EAAE;QAC3D,OAAO,MAAK;MACd,OAAO,IAAIlO,IAAI,CAACkO,QAAQ,CAAC,SAAS,CAAC,EAAE;QACnC,OAAO,KAAI;MACb,OAAO;QACL,OAAO,KAAI;MACb;IACF;;IAEA;;IAEA;IACA,MAAM7J,2BAA0B,GAAI,MAAAA,CAAA,KAAY;MAC9C6F,yBAAyB,CAACzL,KAAI,GAAI,IAAG;MACrC,MAAM0P,oBAAoB,CAAC;IAC7B;;IAEA;IACA,MAAM5J,4BAA2B,GAAI,MAAAA,CAAA,KAAY;MAC/C4F,0BAA0B,CAAC1L,KAAI,GAAI,IAAG;MACtC,MAAM2P,qBAAqB,CAAC;IAC9B;;IAEA;IACA,MAAMD,oBAAmB,GAAI,MAAAA,CAAA,KAAY;MACvC,IAAI;QACF5D,gBAAgB,CAAC9L,KAAI,GAAI,IAAG;QAC5B,MAAM4N,QAAO,GAAI,MAAMC,KAAK,CAACzQ,IAAI,GAAC,0BAA0B;QAC5D,MAAM6Q,MAAK,GAAI,MAAML,QAAQ,CAACM,IAAI,CAAC;QACnC,IAAID,MAAM,CAACxJ,IAAG,KAAM,GAAG,EAAE;UACvBmH,gBAAgB,CAAC5L,KAAI,GAAIiO,MAAM,CAACrJ,OAAM,IAAK,EAAC;QAC9C,OAAO;UACL3H,SAAS,CAAC2G,KAAK,CAAC,UAAU;QAC5B;MACF,EAAE,OAAOA,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC3G,SAAS,CAAC2G,KAAK,CAAC,UAAU;MAC5B,UAAU;QACRkI,gBAAgB,CAAC9L,KAAI,GAAI,KAAI;MAC/B;IACF;;IAEA;IACA,MAAM2P,qBAAoB,GAAI,MAAAA,CAAA,KAAY;MACxC,IAAI;QACF7D,gBAAgB,CAAC9L,KAAI,GAAI,IAAG;QAC5B,MAAM4N,QAAO,GAAI,MAAMC,KAAK,CAACzQ,IAAI,GAAC,2BAA2B;QAC7D,MAAM6Q,MAAK,GAAI,MAAML,QAAQ,CAACM,IAAI,CAAC;QACnC,IAAID,MAAM,CAACxJ,IAAG,KAAM,GAAG,EAAE;UACvBoH,iBAAiB,CAAC7L,KAAI,GAAIiO,MAAM,CAACrJ,OAAM,IAAK,EAAC;QAC/C,OAAO;UACL3H,SAAS,CAAC2G,KAAK,CAAC,UAAU;QAC5B;MACF,EAAE,OAAOA,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC3G,SAAS,CAAC2G,KAAK,CAAC,UAAU;MAC5B,UAAU;QACRkI,gBAAgB,CAAC9L,KAAI,GAAI,KAAI;MAC/B;IACF;;IAEA;IACA,MAAM4P,qBAAoB,GAAKC,QAAQ,IAAK;MAC1C;MACA3P,WAAW,CAACO,eAAc,GAAIoP,QAAQ,CAACC,GAAE;MACzCrE,yBAAyB,CAACzL,KAAI,GAAI,KAAI;MACtC/C,SAAS,CAACgI,OAAO,CAAC,YAAY4K,QAAQ,CAACE,KAAK,SAASF,QAAQ,CAACC,GAAG,GAAG;IACtE;;IAEA;IACA,MAAME,sBAAqB,GAAKH,QAAQ,IAAK;MAC3C;MACA3P,WAAW,CAACQ,gBAAe,GAAImP,QAAQ,CAACC,GAAE;MAC1CpE,0BAA0B,CAAC1L,KAAI,GAAI,KAAI;MACvC/C,SAAS,CAACgI,OAAO,CAAC,YAAY4K,QAAQ,CAACE,KAAK,SAASF,QAAQ,CAACC,GAAG,GAAG;IACtE;;IAEA;IACA,MAAMG,kBAAiB,GAAKJ,QAAQ,IAAK;MACvC9D,qBAAqB,CAAC/L,KAAI,GAAI6P,QAAO;MACrClE,wBAAwB,CAAC3L,KAAI,GAAI,IAAG;IACtC;;IAEA;IACA,MAAMkQ,WAAU,GAAIlT,QAAQ,CAAC,MAAM;MACjC,OAAOkD,WAAW,CAACvB,IAAG,IACfuB,WAAW,CAACI,YAAW,IACvBJ,WAAW,CAACG,WAAU,IACtBe,aAAa,CAACpB,KAAK,CAAC8C,MAAK,GAAI,KAC7B,CAACkJ,oBAAoB,CAAChM,KAAI;IACnC,CAAC;;IAED;IACA,MAAMmQ,eAAc,GAAI,MAAAA,CAAA,KAAY;MAClC,IAAI,CAACD,WAAW,CAAClQ,KAAK,EAAE;QACtB/C,SAAS,CAACiH,OAAO,CAAC,WAAW;QAC7B;MACF;MAEA,IAAI;QACF8H,oBAAoB,CAAChM,KAAI,GAAI,IAAG;QAChCiM,gBAAgB,CAACjM,KAAI,GAAI,IAAG;;QAE5B;QACA,MAAMsH,WAAU,GAAI;UAClB8I,aAAa,EAAElQ,WAAW,CAACG,WAAW;UACtCC,YAAY,EAAEJ,WAAW,CAACI,YAAY;UACtC+P,WAAW,EAAEnQ,WAAW,CAACvB,IAAI;UAC7B6B,WAAW,EAAEN,WAAW,CAACM,WAAU,IAAK,KAAK;UAC7CL,YAAY,EAAED,WAAW,CAACC,YAAW,IAAK,OAAO;UACjDM,eAAe,EAAEP,WAAW,CAACO,eAAe;UAC5CC,gBAAgB,EAAER,WAAW,CAACQ,gBAAgB;UAC9C4P,MAAM,EAAElP,aAAa,CAACpB,KAAK,CAACoG,GAAG,CAAC0B,KAAI,KAAM;YACxC/C,EAAE,EAAE+C,KAAK,CAACG,GAAG;YACbG,WAAW,EAAEN,KAAK,CAACyI,KAAK;YACxBjI,WAAW,EAAER,KAAK,CAACuB,KAAK;YACxBM,SAAS,EAAE7B,KAAK,CAAC0I,GAAE,GAAI/M,IAAI,CAACC,KAAK,CAACoE,KAAK,CAAC0I,GAAG,IAAI,CAAC,CAAC;YACjD/O,MAAM,EAAEqG,KAAK,CAACrG,MAAK,IAAK;UAC1B,CAAC,CAAC;QACJ;QAEAoC,OAAO,CAACa,GAAG,CAAC,SAAS,EAAE4C,WAAW;;QAElC;QACA,MAAMsG,QAAO,GAAI,MAAMC,KAAK,CAACzQ,IAAG,GAAI,oBAAoB,EAAE;UACxDmQ,MAAM,EAAE,MAAM;UACdE,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDK,IAAI,EAAErK,IAAI,CAACkB,SAAS,CAAC2C,WAAW;QAClC,CAAC;QAED,MAAM2G,MAAK,GAAI,MAAML,QAAQ,CAACM,IAAI,CAAC;QAEnC,IAAID,MAAM,CAACxJ,IAAG,KAAM,GAAG,EAAE;UACvB;UACA,MAAMgM,SAAQ,GAAIxC,MAAM,CAACrJ,OAAO,CAAC8L,KAAI,IAAKzC,MAAM,CAACrJ,OAAO,CAAC8L,KAAK,CAAChD,IAAG,GAAIO,MAAM,CAACrJ,OAAO,CAAC8L,KAAK,CAAChD,IAAG,GAAI,CAAC;UACnG,MAAMiD,eAAc,GAAI1C,MAAM,CAACrJ,OAAO,CAACgM,WAAU,IAAK,IAAG;UAEzD3E,gBAAgB,CAACjM,KAAI,GAAI;YACvB0B,MAAM,EAAE,SAAS;YACjB/B,OAAO,EAAE,SAAS;YAClB+Q,KAAK,EAAED,SAAS;YAChBG,WAAW,EAAED;UACf;;UAEA;UACA,MAAME,iBAAiB,CAAC;UACxB,MAAMC,kBAAkB,CAAC;UAEzB7T,SAAS,CAACgI,OAAO,CAAC,SAAS;QAC7B,OAAO;UACL,MAAM,IAAI+I,KAAK,CAACC,MAAM,CAAC/I,GAAE,IAAK,QAAQ;QACxC;MAEF,EAAE,OAAOtB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9BqI,gBAAgB,CAACjM,KAAI,GAAI;UACvB0B,MAAM,EAAE,OAAO;UACf/B,OAAO,EAAE,SAAQ,GAAIiE,KAAK,CAACjE,OAAO;UAClC+Q,KAAK,EAAE,IAAI;UACXE,WAAW,EAAE;QACf;QACA3T,SAAS,CAAC2G,KAAK,CAAC,SAAQ,GAAIA,KAAK,CAACjE,OAAO;MAC3C,UAAU;QACRqM,oBAAoB,CAAChM,KAAI,GAAI,KAAI;MACnC;IACF;;IAEA;IACA,MAAM6Q,iBAAgB,GAAI,MAAAA,CAAA,KAAY;MACpC,IAAI,CAAC1P,kBAAkB,CAACnB,KAAI,IAAK,CAACoB,aAAa,CAACpB,KAAI,IAAKoB,aAAa,CAACpB,KAAK,CAAC8C,MAAK,KAAM,CAAC,EAAE;QACzF7F,SAAS,CAACiH,OAAO,CAAC,YAAY;QAC9B;MACF;MAEA,IAAI;QACFgI,uBAAuB,CAAClM,KAAI,GAAI,IAAG;;QAEnC;QACA,MAAMG,YAAW,GAAID,WAAW,CAACC,YAAW,IAAK,OAAM;QACvD,IAAI4Q,MAAK,GAAI,EAAC;QAEd,IAAI5Q,YAAW,KAAM,OAAO,EAAE;UAC5B4Q,MAAK,GAAIC,mBAAmB,CAAC;QAC/B,OAAO,IAAI7Q,YAAW,KAAM,WAAW,EAAE;UACvC4Q,MAAK,GAAIE,uBAAuB,CAAC;QACnC;QAEArP,UAAU,CAAC5B,KAAI,GAAI+Q,MAAK;QACxB9T,SAAS,CAACgI,OAAO,CAAC,YAAY;MAEhC,EAAE,OAAOrB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK;QACjC3G,SAAS,CAAC2G,KAAK,CAAC,YAAW,GAAIA,KAAK,CAACjE,OAAO;MAC9C,UAAU;QACRuM,uBAAuB,CAAClM,KAAI,GAAI,KAAI;MACtC;IACF;;IAEA;IACA,MAAMgR,mBAAkB,GAAIA,CAAA,KAAM;MAChC,IAAID,MAAK,GAAI,MAAM7Q,WAAW,CAACvB,IAAG,IAAK,IAAI,kBAAiB;MAC5DoS,MAAK,IAAK,aAAa7Q,WAAW,CAACI,YAAY,IAAG;MAClDyQ,MAAK,IAAK,YAAY,IAAI5O,IAAI,CAAC,CAAC,CAACwB,cAAc,CAAC,CAAC,MAAK;;MAEtD;MACAoN,MAAK,IAAK,mCAAmC7Q,WAAW,CAACI,YAAY,gEAA+D;MACpIyQ,MAAK,IAAK,SAAS7Q,WAAW,CAACI,YAAY,SAAQ;;MAEnD;MACAc,aAAa,CAACpB,KAAK,CAACkR,OAAO,CAACpJ,KAAI,IAAK;QACnCiJ,MAAK,IAAKI,wBAAwB,CAACrJ,KAAK;QACxCiJ,MAAK,IAAK,IAAG;MACf,CAAC;MAED,OAAOA,MAAK;IACd;;IAEA;IACA,MAAME,uBAAsB,GAAIA,CAAA,KAAM;MACpC,IAAIF,MAAK,GAAI,MAAM7Q,WAAW,CAACvB,IAAG,IAAK,IAAI,uBAAsB;MACjEoS,MAAK,IAAK,aAAa7Q,WAAW,CAACI,YAAY,IAAG;MAClDyQ,MAAK,IAAK,YAAY,IAAI5O,IAAI,CAAC,CAAC,CAACwB,cAAc,CAAC,CAAC,MAAK;;MAEtD;MACAoN,MAAK,IAAK,4DAA4D7Q,WAAW,CAACI,YAAY,MAAK;MACnGyQ,MAAK,IAAK,oBAAoB7Q,WAAW,CAACI,YAAY,MAAK;MAC3DyQ,MAAK,IAAK,QAAO;MACjBA,MAAK,IAAK,QAAQ7Q,WAAW,CAACI,YAAY,MAAK;MAC/CyQ,MAAK,IAAK,QAAO;;MAEjB;MACA3P,aAAa,CAACpB,KAAK,CAACkR,OAAO,CAACpJ,KAAI,IAAK;QACnCiJ,MAAK,IAAKK,4BAA4B,CAACtJ,KAAK;QAC5CiJ,MAAK,IAAK,IAAG;MACf,CAAC;MAED,OAAOA,MAAK;IACd;;IAEA;IACA,MAAMI,wBAAuB,GAAKrJ,KAAK,IAAK;MAC1C,IAAIiJ,MAAK,GAAI,SAASjJ,KAAK,CAACyI,KAAI,IAAKzI,KAAK,CAACuB,KAAK,IAAG;MACnD0H,MAAK,IAAK,0BAA0BjJ,KAAK,CAACuB,KAAK,OAAM;MACrD0H,MAAK,IAAK,kBAAkBjJ,KAAK,CAACuB,KAAK,QAAO;MAE9C,MAAM5H,MAAK,GAAIqG,KAAK,CAACrG,MAAK,IAAK,EAAC;MAChC,MAAM4P,YAAW,GAAI5P,MAAM,CAAC2E,GAAG,CAAC,CAAC8B,KAAK,EAAEoJ,KAAK,KAAK;QAChD,IAAIC,WAAU,GAAI,OAAOrJ,KAAK,CAACI,WAAW,MAAMkJ,kBAAkB,CAACtJ,KAAK,CAAC3G,IAAI,CAAC,EAAC;;QAE/E;QACA,IAAI2G,KAAK,CAACI,WAAW,CAACkH,WAAW,CAAC,MAAM,IAAG,IAAK8B,KAAI,KAAM,CAAC,EAAE;UAC3D;UACA,IAAIpJ,KAAK,CAAC3G,IAAG,IAAK2G,KAAK,CAAC3G,IAAI,CAACiO,WAAW,CAAC,MAAM,KAAK,EAAE;YACpD+B,WAAU,IAAK,0BAAyB;UAC1C,OAAO;YACLA,WAAU,IAAK,WAAU;UAC3B;QACF,OAAO,IAAIrJ,KAAK,CAACxI,QAAQ,EAAE;UACzB6R,WAAU,IAAK,WAAU;QAC3B;;QAEA;QACA,IAAIrJ,KAAK,CAACE,WAAW,EAAE;UACrBmJ,WAAU,IAAK,aAAarJ,KAAK,CAACE,WAAW,GAAE;QACjD;QAEA,OAAOmJ,WAAU;MACnB,CAAC;MAEDR,MAAK,IAAKM,YAAY,CAAChK,IAAI,CAAC,KAAK;;MAEjC;MACA,IAAI5F,MAAM,CAACqB,MAAK,GAAI,CAAC,EAAE;QACrB,MAAM2O,UAAS,GAAIhQ,MAAM,CAAC,CAAC,CAAC,CAAC6G,WAAU;QACvCyI,MAAK,IAAK,uBAAuBU,UAAU,KAAI;MACjD;MAEAV,MAAK,IAAK,sDAAsDjJ,KAAK,CAACyI,KAAI,IAAKzI,KAAK,CAACuB,KAAK,QAAO;MAEjG,OAAO0H,MAAK;IACd;;IAEA;IACA,MAAMK,4BAA2B,GAAKtJ,KAAK,IAAK;MAC9C,IAAIiJ,MAAK,GAAI,SAASjJ,KAAK,CAACyI,KAAI,IAAKzI,KAAK,CAACuB,KAAK,IAAG;MACnD0H,MAAK,IAAK,kBAAkBjJ,KAAK,CAACuB,KAAK,oCAAoCvB,KAAK,CAACuB,KAAK,MAAK;MAC3F0H,MAAK,IAAK,iBAAiBjJ,KAAK,CAACuB,KAAK,OAAM;MAE5C,MAAM5H,MAAK,GAAIqG,KAAK,CAACrG,MAAK,IAAK,EAAC;MAChC,MAAM4P,YAAW,GAAI5P,MAAM,CAAC2E,GAAG,CAAC,CAAC8B,KAAK,EAAEoJ,KAAK,KAAK;QAChD,IAAIC,WAAU,GAAI,MAAMrJ,KAAK,CAACI,WAAW,KAAKoJ,sBAAsB,CAACxJ,KAAK,CAAC3G,IAAI,CAAC,EAAC;;QAEjF;QACA,IAAI2G,KAAK,CAACI,WAAW,CAACkH,WAAW,CAAC,MAAM,IAAG,IAAK8B,KAAI,KAAM,CAAC,EAAE;UAC3DC,WAAU,IAAK,gBAAe;QAChC;;QAEA;QACA,IAAIrJ,KAAK,CAACxI,QAAQ,EAAE;UAClB6R,WAAU,IAAK,WAAU;QAC3B;QAEA,OAAOA,WAAU;MACnB,CAAC;MAEDR,MAAK,IAAKM,YAAY,CAAChK,IAAI,CAAC,KAAK;;MAEjC;MACA,IAAI5F,MAAM,CAACqB,MAAK,GAAI,CAAC,EAAE;QACrB,MAAM2O,UAAS,GAAIhQ,MAAM,CAAC,CAAC,CAAC,CAAC6G,WAAU;QACvCyI,MAAK,IAAK,uBAAuBjJ,KAAK,CAACuB,KAAK,mBAAmBoI,UAAU,IAAG;MAC9E;MAEAV,MAAK,IAAK,QAAO;;MAEjB;MACA,IAAIjJ,KAAK,CAACyI,KAAK,EAAE;QACfQ,MAAK,IAAK,kDAAkDjJ,KAAK,CAACyI,KAAK,iCAAiCzI,KAAK,CAACuB,KAAK,MAAK;MAC1H;;MAEA;MACA5H,MAAM,CAACyP,OAAO,CAAChJ,KAAI,IAAK;QACtB,IAAIA,KAAK,CAACE,WAAW,EAAE;UACrB2I,MAAK,IAAK,kDAAkD7I,KAAK,CAACE,WAAW,iCAAiCN,KAAK,CAACuB,KAAK,iBAAiBnB,KAAK,CAACI,WAAW,MAAK;QAClK;MACF,CAAC;MAEDyI,MAAK,IAAK,QAAO;MAEjB,OAAOA,MAAK;IACd;;IAEA;IACA,MAAMS,kBAAiB,GAAKjQ,IAAI,IAAK;MACnC,IAAI,CAACA,IAAI,EAAE,OAAO,cAAa;MAE/B,MAAMoQ,SAAQ,GAAIpQ,IAAI,CAACiO,WAAW,CAAC;MACnC,IAAImC,SAAQ,KAAM,KAAK,EAAE,OAAO,KAAI;MACpC,IAAIA,SAAS,CAAClC,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAOlO,IAAI,CAACqQ,WAAW,CAAC;MAC3D,IAAID,SAAQ,KAAM,MAAM,EAAE,OAAO,MAAK;MACtC,IAAIA,SAAQ,KAAM,UAAU,EAAE,OAAO,UAAS;MAC9C,IAAIA,SAAS,CAAClC,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAOlO,IAAI,CAACqQ,WAAW,CAAC;MAE3D,OAAOrQ,IAAI,CAACqQ,WAAW,CAAC;IAC1B;;IAEA;IACA,MAAMF,sBAAqB,GAAKnQ,IAAI,IAAK;MACvC,IAAI,CAACA,IAAI,EAAE,OAAO,eAAc;MAEhC,MAAMoQ,SAAQ,GAAIpQ,IAAI,CAACiO,WAAW,CAAC;MACnC,IAAImC,SAAQ,KAAM,KAAK,EAAE,OAAO,KAAI;MACpC,IAAIA,SAAS,CAAClC,QAAQ,CAAC,SAAS,CAAC,EAAE;QACjC;QACA,OAAOlO,IAAI,CAACgN,OAAO,CAAC,UAAU,EAAE,UAAU;MAC5C;MACA,IAAIoD,SAAQ,KAAM,MAAM,EAAE,OAAO,OAAM;MACvC,IAAIA,SAAQ,KAAM,UAAU,EAAE,OAAO,UAAS;MAC9C,IAAIA,SAAS,CAAClC,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAOlO,IAAI,CAACqQ,WAAW,CAAC;MAE3D,OAAOrQ,IAAI,CAACqQ,WAAW,CAAC;IAC1B;;IAEA;IACA,MAAMC,eAAc,GAAIA,CAAA,KAAM;MAC5B,IAAI,CAACjQ,UAAU,CAAC5B,KAAI,IAAK4B,UAAU,CAAC5B,KAAK,CAACkN,IAAI,CAAC,MAAM,EAAE,EAAE;QACvDjQ,SAAS,CAACiH,OAAO,CAAC,WAAW;QAC7B;MACF;MAEA,IAAI;QACF,MAAM4N,IAAG,GAAI,IAAIC,IAAI,CAAC,CAACnQ,UAAU,CAAC5B,KAAK,CAAC,EAAE;UAAEuB,IAAI,EAAE;QAA2B,CAAC;QAC9E,MAAM4C,GAAE,GAAI6N,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI;QAC3C,MAAMK,IAAG,GAAI7P,QAAQ,CAAC8P,aAAa,CAAC,GAAG;QACvCD,IAAI,CAACE,IAAG,GAAIlO,GAAE;QAEd,MAAMhE,YAAW,GAAID,WAAW,CAACC,YAAW,IAAK,OAAM;QACvD,MAAMmS,QAAO,GAAI,GAAGpS,WAAW,CAACI,YAAW,IAAK,UAAU,IAAIH,YAAY,MAAK;QAC/EgS,IAAI,CAACI,QAAO,GAAID,QAAO;QAEvBhQ,QAAQ,CAACwL,IAAI,CAAC0E,WAAW,CAACL,IAAI;QAC9BA,IAAI,CAACM,KAAK,CAAC;QACXnQ,QAAQ,CAACwL,IAAI,CAAC4E,WAAW,CAACP,IAAI;QAC9BH,MAAM,CAACC,GAAG,CAACU,eAAe,CAACxO,GAAG;QAE9BlH,SAAS,CAACgI,OAAO,CAAC,YAAY;MAChC,EAAE,OAAOrB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK;QACjC3G,SAAS,CAAC2G,KAAK,CAAC,YAAW,GAAIA,KAAK,CAACjE,OAAO;MAC9C;IACF;;IAEA;IACA,MAAMiT,aAAY,GAAI,MAAAA,CAAA,KAAY;MAChC,IAAI,CAAChR,UAAU,CAAC5B,KAAI,IAAK4B,UAAU,CAAC5B,KAAK,CAACkN,IAAI,CAAC,MAAM,EAAE,EAAE;QACvDjQ,SAAS,CAACiH,OAAO,CAAC,WAAW;QAC7B;MACF;MAEA,IAAI;QACF,MAAM2O,SAAS,CAACC,SAAS,CAACC,SAAS,CAACnR,UAAU,CAAC5B,KAAK;QACpD/C,SAAS,CAACgI,OAAO,CAAC,eAAe;MACnC,EAAE,OAAOrB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC;QACA,IAAI;UACF,MAAMoP,QAAO,GAAI1Q,QAAQ,CAAC8P,aAAa,CAAC,UAAU;UAClDY,QAAQ,CAAChT,KAAI,GAAI4B,UAAU,CAAC5B,KAAI;UAChCsC,QAAQ,CAACwL,IAAI,CAAC0E,WAAW,CAACQ,QAAQ;UAClCA,QAAQ,CAACC,MAAM,CAAC;UAChB3Q,QAAQ,CAAC4Q,WAAW,CAAC,MAAM;UAC3B5Q,QAAQ,CAACwL,IAAI,CAAC4E,WAAW,CAACM,QAAQ;UAClC/V,SAAS,CAACgI,OAAO,CAAC,eAAe;QACnC,EAAE,OAAOkO,aAAa,EAAE;UACtBtP,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEuP,aAAa;UACzClW,SAAS,CAAC2G,KAAK,CAAC,gBAAgB;QAClC;MACF;IACF;;IAEA;IACA,MAAMkN,kBAAiB,GAAI,MAAAA,CAAA,KAAY;MACrC,IAAI,CAAC3P,kBAAkB,CAACnB,KAAI,IAAK,CAACoB,aAAa,CAACpB,KAAI,IAAKoB,aAAa,CAACpB,KAAK,CAAC8C,MAAK,KAAM,CAAC,EAAE;QACzF7F,SAAS,CAACiH,OAAO,CAAC,YAAY;QAC9B;MACF;MAEA,IAAI;QACFiI,wBAAwB,CAACnM,KAAI,GAAI,IAAG;;QAEpC;QACA,MAAMoT,cAAa,GAAIhS,aAAa,CAACpB,KAAK,CAAC4O,MAAM,CAAC9G,KAAI,IAAK;UACzD,MAAMuL,SAAQ,GAAIlK,QAAQ,CAACrB,KAAK,CAACjB,GAAE,IAAK,GAAG;UAC3C,OAAOwM,SAAQ,GAAI;QACrB,CAAC;QAED,IAAID,cAAc,CAACtQ,MAAK,KAAM,CAAC,EAAE;UAC/B7F,SAAS,CAACiH,OAAO,CAAC,8BAA8B;UAChD;QACF;QAEA,IAAI6M,MAAK,GAAI,EAAC;;QAEd;QACA,KAAK,MAAMjJ,KAAI,IAAKsL,cAAc,EAAE;UAClCrC,MAAK,IAAKuC,2BAA2B,CAACxL,KAAK;UAC3CiJ,MAAK,IAAK,IAAG;QACf;;QAEA;QACAA,MAAK,IAAK,SAAS7Q,WAAW,CAACvB,IAAG,IAAK,aAAa,IAAG;QACvDoS,MAAK,IAAK,yCAAwC;QAClDA,MAAK,IAAK,oBAAmB;QAC7BA,MAAK,IAAK,aAAY;QACtBA,MAAK,IAAK,oCAAmC;QAE7C3E,WAAW,CAACpM,KAAI,GAAI+Q,MAAK;QACzB9T,SAAS,CAACgI,OAAO,CAAC,WAAW;MAE/B,EAAE,OAAOrB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC3G,SAAS,CAAC2G,KAAK,CAAC,WAAU,GAAIA,KAAK,CAACjE,OAAO;MAC7C,UAAU;QACRwM,wBAAwB,CAACnM,KAAI,GAAI,KAAI;MACvC;IACF;;IAEA;IACA,MAAMsT,2BAA0B,GAAKxL,KAAK,IAAK;MAC7C,MAAMrG,MAAK,GAAIqG,KAAK,CAACrG,MAAK,IAAK,EAAC;MAChC,MAAM4R,SAAQ,GAAIlK,QAAQ,CAACrB,KAAK,CAACjB,GAAE,IAAK,GAAG;MAE3C,IAAIkK,MAAK,GAAI,8BAA8BjJ,KAAK,CAACuB,KAAK,QAAO;;MAE7D;MACA,MAAMgI,YAAW,GAAI5P,MAAM,CAAC2E,GAAG,CAAC,CAAC8B,KAAK,EAAEoJ,KAAK,KAAK;QAChD,IAAIC,WAAU,GAAI,GAAGrJ,KAAK,CAACI,WAAW,MAAMkJ,kBAAkB,CAACtJ,KAAK,CAAC3G,IAAI,CAAC,EAAC;;QAE3E;QACA,IAAI2G,KAAK,CAACI,WAAW,CAACkH,WAAW,CAAC,MAAM,IAAG,IAAK8B,KAAI,KAAM,CAAC,EAAE;UAC3D;UACA,IAAIpJ,KAAK,CAAC3G,IAAG,IAAK2G,KAAK,CAAC3G,IAAI,CAACiO,WAAW,CAAC,MAAM,KAAK,EAAE;YACpD+B,WAAU,IAAK,8BAA6B;UAC9C,OAAO;YACLA,WAAU,IAAK,0BAAyB;UAC1C;QACF,OAAO,IAAIrJ,KAAK,CAACI,WAAW,CAACkH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,SAAS,KAClDvH,KAAK,CAACI,WAAW,CAACkH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,UAAU,CAAC,EAAE;UAC/D8B,WAAU,IAAK,0BAAyB;QAC1C,OAAO,IAAIrJ,KAAK,CAACxI,QAAQ,EAAE;UACzB6R,WAAU,IAAK,cAAa;QAC9B,OAAO;UACLA,WAAU,IAAK,WAAU;QAC3B;;QAEA;QACA,IAAIrJ,KAAK,CAACE,WAAW,EAAE;UACrBmJ,WAAU,IAAK,aAAarJ,KAAK,CAACE,WAAW,GAAE;QACjD;QAEA,OAAOmJ,WAAU;MACnB,CAAC;MAEDR,MAAK,IAAKM,YAAY,CAAChK,IAAI,CAAC,MAAM,IAAI,KAAI;MAC1C0J,MAAK,IAAK,4DAA2D;;MAErE;MACA,IAAIsC,SAAQ,GAAI,CAAC,EAAE;QACjBtC,MAAK,IAAK,KAAKsC,SAAS,oBAAmB;MAC7C;MAEA,OAAOtC,MAAK;IACd;;IAEA;IACA,MAAMwC,cAAa,GAAI,MAAAA,CAAA,KAAY;MACjC,IAAI,CAACnH,WAAW,CAACpM,KAAI,IAAKoM,WAAW,CAACpM,KAAK,CAACkN,IAAI,CAAC,MAAM,EAAE,EAAE;QACzDjQ,SAAS,CAACiH,OAAO,CAAC,UAAU;QAC5B;MACF;MAEA,IAAI;QACF,MAAM2O,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC3G,WAAW,CAACpM,KAAK;QACrD/C,SAAS,CAACgI,OAAO,CAAC,cAAc;MAClC,EAAE,OAAOrB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC;QACA,IAAI;UACF,MAAMoP,QAAO,GAAI1Q,QAAQ,CAAC8P,aAAa,CAAC,UAAU;UAClDY,QAAQ,CAAChT,KAAI,GAAIoM,WAAW,CAACpM,KAAI;UACjCsC,QAAQ,CAACwL,IAAI,CAAC0E,WAAW,CAACQ,QAAQ;UAClCA,QAAQ,CAACC,MAAM,CAAC;UAChB3Q,QAAQ,CAAC4Q,WAAW,CAAC,MAAM;UAC3B5Q,QAAQ,CAACwL,IAAI,CAAC4E,WAAW,CAACM,QAAQ;UAClC/V,SAAS,CAACgI,OAAO,CAAC,cAAc;QAClC,EAAE,OAAOkO,aAAa,EAAE;UACtBtP,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEuP,aAAa;UACzClW,SAAS,CAAC2G,KAAK,CAAC,gBAAgB;QAClC;MACF;IACF;;IAEA;IACA,MAAM4P,eAAc,GAAKC,QAAQ,IAAK;MACpC,IAAIA,QAAQ,EAAE;QACZ;QACA,MAAMnB,QAAO,GAAImB,QAAQ,CAAC7Q,KAAK,CAAC,GAAG,CAAC,CAAC8Q,GAAG,CAAC;QACzC;QACA,MAAMC,WAAU,GAAI,GAAGvW,IAAI,+BAA+B4H,kBAAkB,CAACsN,QAAQ,CAAC,EAAC;QACvFN,MAAM,CAAC4B,IAAI,CAACD,WAAW,EAAE,QAAQ;MACnC,OAAO;QACL1W,SAAS,CAAC2G,KAAK,CAAC,QAAQ;MAC1B;IACF;;IAEA;IACA,MAAMiQ,mBAAkB,GAAKC,KAAK,IAAK;MACrC,IAAI,CAACA,KAAK,EAAE,OAAO,EAAC;;MAEpB;MACA,MAAMC,QAAO,GAAID,KAAK,CAAC/E,KAAK,CAAC,wBAAwB;MACrD,IAAIgF,QAAO,IAAKA,QAAQ,CAAC,CAAC,CAAC,EAAE;QAC3B,OAAOA,QAAQ,CAAC,CAAC;MACnB;MAEA,OAAO,EAAC;IACV;;IAEA;IACA,MAAMC,gBAAe,GAAKC,KAAK,IAAK;MAClCA,KAAK,CAACC,MAAM,CAACC,KAAK,CAACC,OAAM,GAAI,MAAK;MAClC,MAAMC,MAAK,GAAIJ,KAAK,CAACC,MAAM,CAACI,aAAY;MACxC,IAAID,MAAM,EAAE;QACVA,MAAM,CAACE,SAAQ,GAAI,iDAAgD;MACrE;IACF;;IAEA;IACA,MAAMC,kBAAiB,GAAK1M,KAAK,IAAK;MACpC;MACA,IAAIA,KAAK,CAACrG,MAAK,IAAKgT,KAAK,CAACC,OAAO,CAAC5M,KAAK,CAACrG,MAAM,CAAC,EAAE;QAC/C,OAAOqG,KAAK,CAACrG,MAAM,CAACqB,MAAK;MAC3B;MACA;MACA,OAAO;IACT;;IAEA;IACA,MAAM6R,iBAAgB,GAAK7M,KAAK,IAAK;MACnC,IAAI,CAACA,KAAK,CAAC0I,GAAG,EAAE,OAAO,EAAC;MAExB,IAAI;QACF,MAAM7G,SAAQ,GAAIlG,IAAI,CAACC,KAAK,CAACoE,KAAK,CAAC0I,GAAG;QACtC,MAAMoE,eAAc,GAAI,EAAC;;QAEzB;QACA,IAAIjL,SAAS,CAACC,UAAU,EAAEgL,eAAe,CAACvF,IAAI,CAAC,MAAM;QACrD,IAAI1F,SAAS,CAACE,WAAW,EAAE+K,eAAe,CAACvF,IAAI,CAAC,MAAM;QACtD,IAAI1F,SAAS,CAACG,aAAa,EAAE8K,eAAe,CAACvF,IAAI,CAAC,MAAM;QACxD,IAAI1F,SAAS,CAACI,aAAa,EAAE6K,eAAe,CAACvF,IAAI,CAAC,MAAM;QACxD,IAAI1F,SAAS,CAACK,WAAW,EAAE4K,eAAe,CAACvF,IAAI,CAAC,MAAM;QACtD,IAAI1F,SAAS,CAACY,WAAW,EAAEqK,eAAe,CAACvF,IAAI,CAAC,MAAM;QACtD,IAAI1F,SAAS,CAACa,YAAY,EAAEoK,eAAe,CAACvF,IAAI,CAAC,MAAM;QACvD,IAAI1F,SAAS,CAACc,cAAc,EAAEmK,eAAe,CAACvF,IAAI,CAAC,MAAM;QACzD,IAAI1F,SAAS,CAACkB,OAAO,EAAE+J,eAAe,CAACvF,IAAI,CAAC,OAAO;QAEnD,OAAOuF,eAAe,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAE;MACrC,EAAE,OAAOjR,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK;QACjC,OAAO,EAAC;MACV;IACF;;IAEA;IACA,MAAMkR,oBAAmB,GAAIA,CAAChN,KAAI,GAAI,IAAI,KAAK;MAC7C,IAAIA,KAAK,EAAE;QACT;QACA0B,kBAAkB,CAACxJ,KAAI,GAAI;UACzB+E,EAAE,EAAE+C,KAAK,CAACG,GAAG;UACbG,WAAW,EAAEN,KAAK,CAACyI,KAAI,IAAK,EAAE;UAC9BjI,WAAW,EAAER,KAAK,CAACuB,KAAI,IAAK,EAAE;UAC9BI,SAAS,EAAEN,QAAQ,CAACrB,KAAK,CAAChB,GAAE,IAAK,GAAG,CAAC;UACrC4C,YAAY,EAAE,GAAG;UACjBR,iBAAiB,EAAEC,QAAQ,CAACrB,KAAK,CAACjB,GAAE,IAAK,GAAG,CAAC;UAC7C8C,SAAS,EAAE7B,KAAK,CAAC0I,GAAE,GAAI/M,IAAI,CAACC,KAAK,CAACoE,KAAK,CAAC0I,GAAG,IAAI;YAC7C5G,UAAU,EAAE,IAAI;YAAEC,WAAW,EAAE,IAAI;YAAEC,aAAa,EAAE,IAAI;YACxDC,aAAa,EAAE,IAAI;YAAEC,WAAW,EAAE,KAAK;YAAEC,WAAW,EAAE,KAAK;YAC3DC,WAAW,EAAE,KAAK;YAAEC,YAAY,EAAE,KAAK;YAAEC,eAAe,EAAE,KAAK;YAC/DC,cAAc,EAAE,KAAK;YAAEC,eAAe,EAAE,KAAK;YAC7CC,WAAW,EAAE,KAAK;YAAEC,YAAY,EAAE,KAAK;YAAEC,cAAc,EAAE,KAAK;YAC9DC,cAAc,EAAE,KAAK;YAAEC,YAAY,EAAE,KAAK;YAAEC,aAAa,EAAE,KAAK;YAChEC,OAAO,EAAE,KAAK;YAAEC,QAAQ,EAAE,KAAK;YAAEC,UAAU,EAAE,KAAK;YAClDC,UAAU,EAAE,KAAK;YAAEC,QAAQ,EAAE;UAC/B,CAAC;UACDxJ,MAAM,EAAEqG,KAAK,CAACrG,MAAK,IAAK,CACtB;YACEsD,EAAE,EAAE,CAAC;YACLqD,WAAW,EAAE,MAAM;YACnBE,WAAW,EAAE,IAAI;YACjB/G,IAAI,EAAE,KAAK;YACXkH,WAAW,EAAE,KAAK;YAClB/I,QAAQ,EAAE,IAAI;YACdkJ,UAAU,EAAE,KAAK;YACjBE,OAAO,EAAE,IAAI;YACbC,WAAW,EAAE;UACf;QAEJ;MACF,OAAO;QACL;QACAgM,gBAAgB,CAAC;MACnB;MACAzL,oBAAoB,CAACtJ,KAAI,GAAI,IAAG;IAClC;;IAEA;IACA,MAAM+U,gBAAe,GAAIA,CAAA,KAAM;MAC7BvL,kBAAkB,CAACxJ,KAAI,GAAI;QACzB+E,EAAE,EAAE,IAAI;QACRqD,WAAW,EAAE,EAAE;QACfE,WAAW,EAAE,EAAE;QACfmB,SAAS,EAAE,CAAC;QACZC,YAAY,EAAE,GAAG;QACjBR,iBAAiB,EAAE,CAAC;QACpBS,SAAS,EAAE;UACTC,UAAU,EAAE,IAAI;UAAEC,WAAW,EAAE,IAAI;UAAEC,aAAa,EAAE,IAAI;UACxDC,aAAa,EAAE,IAAI;UAAEC,WAAW,EAAE,KAAK;UAAEC,WAAW,EAAE,KAAK;UAC3DC,WAAW,EAAE,KAAK;UAAEC,YAAY,EAAE,KAAK;UAAEC,eAAe,EAAE,KAAK;UAC/DC,cAAc,EAAE,KAAK;UAAEC,eAAe,EAAE,KAAK;UAC7CC,WAAW,EAAE,KAAK;UAAEC,YAAY,EAAE,KAAK;UAAEC,cAAc,EAAE,KAAK;UAC9DC,cAAc,EAAE,KAAK;UAAEC,YAAY,EAAE,KAAK;UAAEC,aAAa,EAAE,KAAK;UAChEC,OAAO,EAAE,KAAK;UAAEC,QAAQ,EAAE,KAAK;UAAEC,UAAU,EAAE,KAAK;UAClDC,UAAU,EAAE,KAAK;UAAEC,QAAQ,EAAE;QAC/B,CAAC;QACDxJ,MAAM,EAAE,CACN;UACEsD,EAAE,EAAE,CAAC;UACLqD,WAAW,EAAE,MAAM;UACnBE,WAAW,EAAE,IAAI;UACjB/G,IAAI,EAAE,KAAK;UACXkH,WAAW,EAAE,KAAK;UAClB/I,QAAQ,EAAE,IAAI;UACdkJ,UAAU,EAAE,KAAK;UACjBE,OAAO,EAAE,IAAI;UACbC,WAAW,EAAE;QACf;MAEJ;IACF;;IAEA;IACA,MAAMiM,qBAAoB,GAAIA,CAAA,KAAM;MAClC1L,oBAAoB,CAACtJ,KAAI,GAAI,KAAI;MACjCuJ,eAAe,CAACvJ,KAAI,GAAI,gBAAe;IACzC;;IAEA;IACA,MAAMiV,cAAa,GAAKC,KAAK,IAAK;MAChC3L,eAAe,CAACvJ,KAAI,GAAIkV,KAAI;IAC9B;;IAEA;IACA,MAAMC,mBAAkB,GAAIA,CAAA,KAAM;MAChC,MAAMC,QAAO,GAAI;QACfrQ,EAAE,EAAE5C,IAAI,CAACgN,GAAG,CAAC,CAAC;QACd/G,WAAW,EAAE,EAAE;QACfE,WAAW,EAAE,EAAE;QACf/G,IAAI,EAAE,cAAc;QACpBkH,WAAW,EAAE,KAAK;QAClB/I,QAAQ,EAAE,IAAI;QAAE;QAChBkJ,UAAU,EAAE,KAAK;QACjBE,OAAO,EAAE,IAAI;QACbC,WAAW,EAAE,KAAK;QAClBC,YAAY,EAAE,EAAE;QAAE;QAClBC,aAAa,EAAE,EAAC,CAAE;MACpB;MACAO,kBAAkB,CAACxJ,KAAK,CAACyB,MAAM,CAAC4N,IAAI,CAAC+F,QAAQ;IAC/C;;IAEA;IACA,MAAMC,cAAa,GAAI,MAAAA,CAAA,KAAY;MACjC,IAAI;QACF,MAAMnY,YAAY,CAACoY,OAAO,CACxB,qBAAqB,EACrB,MAAM,EACN;UACEC,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtBjU,IAAI,EAAE;QACR,CACF;QAEAiI,kBAAkB,CAACxJ,KAAK,CAACyB,MAAK,GAAI,EAAC;QACnCxE,SAAS,CAACgI,OAAO,CAAC,QAAQ;MAC5B,EAAE,OAAOrB,KAAK,EAAE;QACd,IAAIA,KAAI,KAAM,QAAQ,EAAE;UACtBC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;QAChC;MACF;IACF;;IAEA;IACA,MAAM6R,qBAAoB,GAAI,MAAOnE,KAAK,IAAK;MAC7C,IAAI;QACF,MAAMpU,YAAY,CAACoY,OAAO,CACxB,qBAAqB,EACrB,MAAM,EACN;UACEC,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtBjU,IAAI,EAAE;QACR,CACF;QAEAiI,kBAAkB,CAACxJ,KAAK,CAACyB,MAAM,CAACiU,MAAM,CAACpE,KAAK,EAAE,CAAC;QAC/CrU,SAAS,CAACgI,OAAO,CAAC,QAAQ;MAC5B,EAAE,OAAOrB,KAAK,EAAE;QACd,IAAIA,KAAI,KAAM,QAAQ,EAAE;UACtBC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;QAChC;MACF;IACF;;IAEA;IACA,MAAM+R,WAAU,GAAKrE,KAAK,IAAK;MAC7B,IAAIA,KAAI,GAAI,CAAC,EAAE;QACb,MAAMpJ,KAAI,GAAIsB,kBAAkB,CAACxJ,KAAK,CAACyB,MAAM,CAACiU,MAAM,CAACpE,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;QAChE9H,kBAAkB,CAACxJ,KAAK,CAACyB,MAAM,CAACiU,MAAM,CAACpE,KAAI,GAAI,CAAC,EAAE,CAAC,EAAEpJ,KAAK;MAC5D;IACF;;IAEA;IACA,MAAM0N,aAAY,GAAKtE,KAAK,IAAK;MAC/B,IAAIA,KAAI,GAAI9H,kBAAkB,CAACxJ,KAAK,CAACyB,MAAM,CAACqB,MAAK,GAAI,CAAC,EAAE;QACtD,MAAMoF,KAAI,GAAIsB,kBAAkB,CAACxJ,KAAK,CAACyB,MAAM,CAACiU,MAAM,CAACpE,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;QAChE9H,kBAAkB,CAACxJ,KAAK,CAACyB,MAAM,CAACiU,MAAM,CAACpE,KAAI,GAAI,CAAC,EAAE,CAAC,EAAEpJ,KAAK;MAC5D;IACF;;IAEA;IACA,MAAM2N,iBAAgB,GAAIA,CAAC3N,KAAK,EAAEoJ,KAAK,KAAK;MAC1ChF,oBAAoB,CAACtM,KAAI,GAAI;QAAE,GAAGkI;MAAM;MACxCqE,iBAAiB,CAACvM,KAAI,GAAIsR,KAAI;MAC9B9E,gBAAgB,CAACxM,KAAI,GAAIkI,KAAK,CAACU,UAAS,IAAK,KAAI;MACjDyD,uBAAuB,CAACrM,KAAI,GAAI,IAAG;IACrC;;IAEA;IACA,MAAM8V,wBAAuB,GAAIA,CAAA,KAAM;MACrCzJ,uBAAuB,CAACrM,KAAI,GAAI,KAAI;MACpCsM,oBAAoB,CAACtM,KAAI,GAAI,IAAG;MAChCuM,iBAAiB,CAACvM,KAAI,GAAI,CAAC;IAC7B;;IAEA;IACA,MAAM+V,iBAAgB,GAAIA,CAAA,KAAM;MAC9B,IAAIxJ,iBAAiB,CAACvM,KAAI,IAAK,KAAKsM,oBAAoB,CAACtM,KAAK,EAAE;QAC9D;QACA,MAAMkI,KAAI,GAAIsB,kBAAkB,CAACxJ,KAAK,CAACyB,MAAM,CAAC8K,iBAAiB,CAACvM,KAAK;QACrEkI,KAAK,CAACc,YAAW,GAAIsD,oBAAoB,CAACtM,KAAK,CAACgJ,YAAW;QAC3Dd,KAAK,CAACe,aAAY,GAAIqD,oBAAoB,CAACtM,KAAK,CAACiJ,aAAY;QAC7Df,KAAK,CAACU,UAAS,GAAI4D,gBAAgB,CAACxM,KAAI;QAExC/C,SAAS,CAACgI,OAAO,CAAC,UAAU;QAC5B6Q,wBAAwB,CAAC;MAC3B;IACF;;IAEA;IACA,MAAME,wBAAuB,GAAI,MAAAA,CAAA,KAAY;MAC3C,IAAI,CAAC7U,kBAAkB,CAACnB,KAAI,IAAK,CAACmB,kBAAkB,CAACnB,KAAK,CAACsG,GAAG,EAAE;QAC9DrJ,SAAS,CAACiH,OAAO,CAAC,UAAU;QAC5B;MACF;MAEA,IAAI;QACF;QACA,MAAM+R,0BAA0B,CAAC;QACjCxJ,sBAAsB,CAACzM,KAAI,GAAI,IAAG;MACpC,EAAE,OAAO4D,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK;QAC/B3G,SAAS,CAAC2G,KAAK,CAAC,SAAS;MAC3B;IACF;;IAEA;IACA,MAAMqS,0BAAyB,GAAI,MAAAA,CAAA,KAAY;MAC7C,IAAI;QACF,MAAM9R,GAAE,GAAI/G,IAAG,GAAI,yCAAwC;QAC3D,MAAMmH,GAAE,GAAI,MAAMpH,OAAO,CAACqH,IAAI,CAACL,GAAG,EAAE;UAAEmC,GAAG,EAAEnF,kBAAkB,CAACnB,KAAK,CAACsG;QAAI,CAAC;QAEzE,IAAI/B,GAAG,CAACE,IAAG,KAAM,GAAE,IAAKF,GAAG,CAACK,OAAO,EAAE;UACnC8H,sBAAsB,CAAC1M,KAAI,GAAIuE,GAAG,CAACK,OAAO,CAACwB,GAAG,CAAC0B,KAAI,IAAK;YACtD;YACA,IAAIoO,WAAU,GAAIpO,KAAK,CAACyI,KAAI,IAAKzI,KAAK,CAACuB,KAAI;YAC3C,IAAIvB,KAAK,CAACrG,MAAK,IAAKqG,KAAK,CAACrG,MAAM,CAACqB,MAAK,GAAI,CAAC,EAAE;cAC3C,MAAMqT,WAAU,GAAIrO,KAAK,CAACrG,MAAM,CAAC,CAAC;cAClC,IAAI0U,WAAU,IAAKA,WAAW,CAAC/N,WAAU,IAAK,CAAC+N,WAAW,CAAC/N,WAAW,CAACqH,QAAQ,CAAC,IAAI,CAAC,EAAE;gBACrFyG,WAAU,GAAIC,WAAW,CAAC/N,WAAU;cACtC,OAAO,IAAIN,KAAK,CAACrG,MAAM,CAACqB,MAAK,GAAI,CAAC,EAAE;gBAClC,MAAMsT,UAAS,GAAItO,KAAK,CAACrG,MAAM,CAAC,CAAC;gBACjC,IAAI2U,UAAS,IAAKA,UAAU,CAAChO,WAAW,EAAE;kBACxC8N,WAAU,GAAIE,UAAU,CAAChO,WAAU;gBACrC;cACF;YACF;YAEA,OAAO;cACLqJ,UAAU,EAAE3J,KAAK,CAACrG,MAAK,IAAKqG,KAAK,CAACrG,MAAM,CAACqB,MAAK,GAAI,IAAIgF,KAAK,CAACrG,MAAM,CAAC,CAAC,CAAC,CAAC6G,WAAU,GAAI,IAAI;cACxF4N,WAAW,EAAEA,WAAW;cACxBG,SAAS,EAAEvO,KAAK,CAACuB,KAAK;cACtBiN,SAAS,EAAE,GAAG;cACdC,QAAQ,EAAE;YACZ;UACF,CAAC;QACH;MACF,EAAE,OAAO3S,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK;QAC/B,MAAMA,KAAI;MACZ;IACF;;IAEA;IACA,MAAM4S,uBAAsB,GAAIA,CAAA,KAAM;MACpC/J,sBAAsB,CAACzM,KAAI,GAAI,KAAI;MACnC0M,sBAAsB,CAAC1M,KAAI,GAAI,EAAC;IAClC;;IAEA;IACA,MAAMyW,4BAA2B,GAAIA,CAAA,KAAM;MACzC,MAAMC,cAAa,GAAIhK,sBAAsB,CAAC1M,KAAK,CAAC4O,MAAM,CAAC9G,KAAI,IAAKA,KAAK,CAACyO,QAAQ;MAClF,IAAIG,cAAc,CAAC5T,MAAK,KAAM,CAAC,EAAE;QAC/B7F,SAAS,CAACiH,OAAO,CAAC,YAAY;QAC9B;MACF;;MAEA;MACA,MAAMyS,eAAc,GAAID,cAAc,CAACtQ,GAAG,CAAC0B,KAAI,IAC7C,GAAGA,KAAK,CAAC2J,UAAU,IAAI3J,KAAK,CAACoO,WAAW,IAAIpO,KAAK,CAACuO,SAAS,IAAIvO,KAAK,CAACwO,SAAS,EAChF,CAAC,CAACjP,IAAI,CAAC,GAAG;MAEV,IAAIiF,oBAAoB,CAACtM,KAAK,EAAE;QAC9BsM,oBAAoB,CAACtM,KAAK,CAACgJ,YAAW,GAAI2N,eAAc;MAC1D;MAEAH,uBAAuB,CAAC;MACxBvZ,SAAS,CAACgI,OAAO,CAAC,SAAS;IAC7B;;IAEA;IACA,MAAM2R,eAAc,GAAI,MAAAA,CAAA,KAAY;MAClC;MACA,IAAI,CAACpN,kBAAkB,CAACxJ,KAAK,CAACoI,WAAW,CAAC8E,IAAI,CAAC,CAAC,EAAE;QAChDjQ,SAAS,CAACiH,OAAO,CAAC,WAAW;QAC7B;MACF;MAEA,IAAI,CAACsF,kBAAkB,CAACxJ,KAAK,CAACsI,WAAW,CAAC4E,IAAI,CAAC,CAAC,EAAE;QAChDjQ,SAAS,CAACiH,OAAO,CAAC,WAAW;QAC7B;MACF;;MAEA;MACA,KAAK,IAAIgE,KAAI,IAAKsB,kBAAkB,CAACxJ,KAAK,CAACyB,MAAM,EAAE;QACjD,IAAI,CAACyG,KAAK,CAACE,WAAW,CAAC8E,IAAI,CAAC,KAAK,CAAChF,KAAK,CAACI,WAAW,CAAC4E,IAAI,CAAC,CAAC,EAAE;UAC1DjQ,SAAS,CAACiH,OAAO,CAAC,mBAAmB;UACrC;QACF;MACF;;MAEA;MACA,IAAI,CAAC/C,kBAAkB,CAACnB,KAAI,IAAK,CAACmB,kBAAkB,CAACnB,KAAK,CAACsG,GAAG,EAAE;QAC9DrJ,SAAS,CAAC2G,KAAK,CAAC,gBAAgB;QAChC;MACF;MAEA,IAAI;QACF;QACA,MAAMpC,SAAQ,GAAI;UAChB8E,GAAG,EAAEnF,kBAAkB,CAACnB,KAAK,CAACsG,GAAG;UACjCiK,KAAK,EAAE/G,kBAAkB,CAACxJ,KAAK,CAACoI,WAAW;UAC3CiB,KAAK,EAAEG,kBAAkB,CAACxJ,KAAK,CAACsI,WAAW;UAC3CkI,GAAG,EAAE/M,IAAI,CAACkB,SAAS,CAAC6E,kBAAkB,CAACxJ,KAAK,CAAC2J,SAAS,CAAC;UACvDkN,KAAK,EAAE,EAAE;UACTC,KAAK,EAAE,EAAE;UACTjQ,GAAG,EAAE,CAAC2C,kBAAkB,CAACxJ,KAAK,CAACkJ,iBAAgB,IAAK,CAAC,EAAE6N,QAAQ,CAAC,CAAC;UACjEjQ,GAAG,EAAE,CAAC0C,kBAAkB,CAACxJ,KAAK,CAACyJ,SAAQ,IAAK,CAAC,EAAEsN,QAAQ,CAAC;QAC1D;;QAEA;QACA,IAAIvN,kBAAkB,CAACxJ,KAAK,CAAC+E,EAAE,EAAE;UAC/BvD,SAAS,CAACyG,GAAE,GAAIuB,kBAAkB,CAACxJ,KAAK,CAAC+E,EAAC;QAC5C;;QAEA;QACA,MAAM2C,QAAO,GAAI8B,kBAAkB,CAACxJ,KAAK,CAAC+E,EAAC;QAC3C,MAAMZ,GAAE,GAAI/G,IAAG,IAAKsK,QAAO,GAAI,gBAAe,GAAI,aAAa;QAC/D,MAAMnD,GAAE,GAAI,MAAMpH,OAAO,CAACqH,IAAI,CAACL,GAAG,EAAE3C,SAAS;QAE7C,IAAI+C,GAAG,CAACE,IAAG,KAAM,GAAG,EAAE;UACpB,IAAIuS,OAAM,GAAIxN,kBAAkB,CAACxJ,KAAK,CAAC+E,EAAC;UACxC,IAAI,CAAC2C,QAAO,IAAKnD,GAAG,CAACK,OAAO,EAAE;YAC5B;YACAoS,OAAM,GAAIzS,GAAG,CAACK,OAAM;YACpB4E,kBAAkB,CAACxJ,KAAK,CAAC+E,EAAC,GAAIiS,OAAM;UACtC;;UAEA;UACA,IAAIxN,kBAAkB,CAACxJ,KAAK,CAACyB,MAAM,CAACqB,MAAK,GAAI,CAAC,EAAE;YAC9C;YACA,IAAI4E,QAAQ,EAAE;cACZ,IAAI;gBACF,MAAMvK,OAAO,CAACqH,IAAI,CAACpH,IAAG,GAAI,oBAAoB,EAAE;kBAAE6K,GAAG,EAAE+O;gBAAQ,CAAC;cAClE,EAAE,OAAOpT,KAAK,EAAE;gBACdC,OAAO,CAACuF,IAAI,CAAC,WAAW,EAAExF,KAAK;cACjC;YACF;;YAEA;YACA,KAAK,IAAIsE,KAAI,IAAKsB,kBAAkB,CAACxJ,KAAK,CAACyB,MAAM,EAAE;cACjD,MAAMwV,SAAQ,GAAI;gBAChBhP,GAAG,EAAE+O,OAAO;gBACZzO,MAAM,EAAEL,KAAK,CAACI,WAAW;gBACzBD,OAAO,EAAEH,KAAK,CAACE,WAAW;gBAC1BI,MAAM,EAAEN,KAAK,CAAC3G,IAAI;gBAClBmH,MAAM,EAAER,KAAK,CAACO,WAAW;gBACzByO,MAAM,EAAE,EAAE;gBACVvO,IAAI,EAAET,KAAK,CAACxI,QAAO,GAAI,GAAE,GAAI,GAAG;gBAChCmJ,IAAI,EAAEX,KAAK,CAACU,UAAS,GAAI,GAAE,GAAI,GAAG;gBAClC/B,GAAG,EAAEqB,KAAK,CAACY,OAAM,GAAI,GAAE,GAAI,GAAG;gBAC9BhC,GAAG,EAAEoB,KAAK,CAACa,WAAU,GAAI,GAAE,GAAI,GAAG;gBAClC7B,GAAG,EAAEgB,KAAK,CAACc,YAAW,IAAK,EAAE;gBAC7BjC,GAAG,EAAEmB,KAAK,CAACe,aAAY,IAAK,EAAE;gBAC9BjC,GAAG,EAAE,EAAE;gBACPC,GAAG,EAAE;cACP;cAEA,IAAI;gBACF,MAAM9J,OAAO,CAACqH,IAAI,CAACpH,IAAG,GAAI,YAAY,EAAE6Z,SAAS;cACnD,EAAE,OAAOrT,KAAK,EAAE;gBACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEsE,KAAK,EAAEtE,KAAK;cACvC;YACF;UACF;UAEA3G,SAAS,CAACgI,OAAO,CAAC,SAAS;UAC3B+P,qBAAqB,CAAC;UACtB;UACA,IAAI7T,kBAAkB,CAACnB,KAAK,CAACsG,GAAG,EAAE;YAChCuB,iBAAiB,CAAC1G,kBAAkB,CAACnB,KAAK,CAACsG,GAAG;UAChD;QACF,OAAO;UACLrJ,SAAS,CAAC2G,KAAK,CAACW,GAAG,CAACW,GAAE,IAAK,OAAO;QACpC;MACF,EAAE,OAAOtB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK;QAC/B3G,SAAS,CAAC2G,KAAK,CAAC,iBAAiB;MACnC;IACF;;IAEA;IACA,MAAMuT,WAAU,GAAI,MAAOrP,KAAK,IAAK;MACnC,IAAI;QACF,MAAM5K,YAAY,CAACoY,OAAO,CACxB,WAAWxN,KAAK,CAACyI,KAAI,IAAKzI,KAAK,CAACuB,KAAK,MAAM,EAC3C,MAAM,EACN;UACEkM,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtBjU,IAAI,EAAE;QACR,CACF;QAEA,MAAM4C,GAAE,GAAI/G,IAAG,GAAI,iBAAgB,GAAI0K,KAAK,CAACG,GAAE;QAC/C,MAAM1D,GAAE,GAAI,MAAMpH,OAAO,CAACqH,IAAI,CAACL,GAAG,EAAE,CAAC,CAAC;QAEtC,IAAII,GAAG,CAACE,IAAG,KAAM,GAAG,EAAE;UACpBxH,SAAS,CAACgI,OAAO,CAAC,MAAM;UACxB;UACA,IAAI9D,kBAAkB,CAACnB,KAAI,IAAKmB,kBAAkB,CAACnB,KAAK,CAACsG,GAAG,EAAE;YAC5DuB,iBAAiB,CAAC1G,kBAAkB,CAACnB,KAAK,CAACsG,GAAG;UAChD;QACF,OAAO;UACLrJ,SAAS,CAAC2G,KAAK,CAACW,GAAG,CAACW,GAAE,IAAK,MAAM;QACnC;MACF,EAAE,OAAOtB,KAAK,EAAE;QACd,IAAIA,KAAI,KAAM,QAAQ,EAAE;UACtBC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK;UAC7B3G,SAAS,CAAC2G,KAAK,CAAC,cAAc;QAChC;MACF;IACF;;IAEA;IACA,MAAMwT,QAAO,GAAI,MAAAA,CAAA,KAAY;MAC3B,IAAItY,SAAS,CAACkB,KAAI,KAAM,SAAS,EAAE;QACjC;QACA,IAAI,CAACH,eAAe,CAACG,KAAK,EAAE;UAC1B/C,SAAS,CAACiH,OAAO,CAAC,UAAU;UAC5B;QACF;QACA,IAAI,CAAChE,WAAW,CAACvB,IAAI,EAAE;UACrB1B,SAAS,CAACiH,OAAO,CAAC,WAAW;UAC7B;QACF;;QAEA;QACA,IAAIhE,WAAW,CAACE,YAAW,KAAM,KAAK,EAAE;UACtC,IAAI,CAACF,WAAW,CAACG,WAAW,EAAE;YAC5BpD,SAAS,CAACiH,OAAO,CAAC,SAAS;YAC3B;UACF;UACA,IAAI,CAAChE,WAAW,CAACI,YAAY,EAAE;YAC7BrD,SAAS,CAACiH,OAAO,CAAC,UAAU;YAC5B;UACF;QACF,OAAO,IAAIhE,WAAW,CAACE,YAAW,KAAM,UAAU,EAAE;UAClD,IAAI,CAACF,WAAW,CAACK,gBAAgB,EAAE;YACjCtD,SAAS,CAACiH,OAAO,CAAC,UAAU;YAC5B;UACF;QACF;;QAEA;QACA/C,kBAAkB,CAACnB,KAAI,GAAI;UACzBrB,IAAI,EAAEuB,WAAW,CAACvB,IAAI;UACtB0B,WAAW,EAAEH,WAAW,CAACG,WAAW;UACpCC,YAAY,EAAEJ,WAAW,CAACI,YAAY;UACtCgG,GAAG,EAAEpG,WAAW,CAACK,gBAAe,IAAK;QACvC;;QAEA;QACA,MAAM0N,MAAK,GAAI,MAAM7G,mBAAmB,CAAC;QACzC,IAAI,CAAC6G,MAAM,CAAChJ,OAAO,EAAE;;QAErB;QACA,IAAIgJ,MAAM,CAACtH,SAAS,EAAE;UACpBxF,kBAAkB,CAACnB,KAAK,CAACsG,GAAE,GAAI2H,MAAM,CAACtH,SAAQ;QAChD;;QAEA;QACA,IAAIzG,WAAW,CAACE,YAAW,KAAM,UAAS,IAAKF,WAAW,CAACK,gBAAgB,EAAE;UAC3EsH,iBAAiB,CAAC3H,WAAW,CAACK,gBAAgB;QAChD,OAAO,IAAIY,kBAAkB,CAACnB,KAAK,CAACsG,GAAG,EAAE;UACvC;UACAuB,iBAAiB,CAAC1G,kBAAkB,CAACnB,KAAK,CAACsG,GAAG;QAChD;QAEAxH,SAAS,CAACkB,KAAI,GAAI,MAAK;QACvB/C,SAAS,CAACgI,OAAO,CAAC,kBAAkB;MACtC,OAAO,IAAInG,SAAS,CAACkB,KAAI,KAAM,MAAM,EAAE;QACrC;QACA,IAAI,CAACoB,aAAa,CAACpB,KAAI,IAAKoB,aAAa,CAACpB,KAAK,CAAC8C,MAAK,KAAM,CAAC,EAAE;UAC5D7F,SAAS,CAACiH,OAAO,CAAC,cAAc;UAChC;QACF;QACApF,SAAS,CAACkB,KAAI,GAAI,UAAS;QAC3B/C,SAAS,CAACgI,OAAO,CAAC,cAAc;MAClC;IACF;IAEA,MAAMoS,cAAa,GAAKC,GAAG,IAAK;MAC9BzT,OAAO,CAACa,GAAG,CAAC,SAAS,EAAE4S,GAAG,CAACC,KAAK,CAAC5Y,IAAI;IACvC;;IAEA;IACA7B,SAAS,CAAC,MAAM;MACdsG,gBAAgB,CAAC;IACnB,CAAC;IAED,OAAO;MACLtE,SAAS;MACTC,QAAQ;MACRC,UAAU;MACVC,kBAAkB;MAClBC,YAAY;MACZC,YAAY;MACZC,QAAQ;MACRG,SAAS;MACTE,UAAU;MACVI,eAAe;MACfC,eAAe;MACfC,kBAAkB;MAClBG,WAAW;MACXiB,kBAAkB;MAClBC,aAAa;MACbC,oBAAoB;MACpBiI,oBAAoB;MACpBC,eAAe;MACfC,kBAAkB;MAClBlI,UAAU;MACVE,SAAS;MACTI,UAAU;MACVC,IAAI;MACJwV,cAAc;MACdpT,WAAW;MACXkB,iBAAiB;MACjBE,YAAY;MACZE,aAAa;MACbE,cAAc;MACdE,iBAAiB;MACjBE,sBAAsB;MACtBE,wBAAwB;MACxBW,mBAAmB;MACnBmB,iBAAiB;MACjBiN,oBAAoB;MACpBE,qBAAqB;MACrBC,cAAc;MACdE,mBAAmB;MACnBE,cAAc;MACdI,qBAAqB;MACrBE,WAAW;MACXC,aAAa;MACbgB,eAAe;MACfO,WAAW;MACXC,QAAQ;MACR5C,kBAAkB;MAClBG,iBAAiB;MACjBzJ,gBAAgB;MAChBC,iBAAiB;MACjBC,aAAa;MACbC,WAAW;MACXsB,oBAAoB;MAEpB;MACArB,qBAAqB;MACrBC,gBAAgB;MAChBC,sBAAsB;MACtBuB,oBAAoB;MACpBC,mBAAmB;MACnBG,cAAc;MACdqB,gBAAgB;MAChBY,gBAAgB;MAEhB;MACA3D,yBAAyB;MACzBC,0BAA0B;MAC1BC,wBAAwB;MACxBC,gBAAgB;MAChBC,iBAAiB;MACjBC,gBAAgB;MAChBC,qBAAqB;MACrBnG,2BAA2B;MAC3BE,4BAA4B;MAC5B8J,qBAAqB;MACrBI,sBAAsB;MACtBC,kBAAkB;MAClB4D,mBAAmB;MACnBG,gBAAgB;MAEhB;MACAhI,oBAAoB;MACpBC,gBAAgB;MAChBiE,WAAW;MACXC,eAAe;MACfqD,eAAe;MAEf;MACAtH,uBAAuB;MACvB2E,iBAAiB;MACjBgB,eAAe;MACfe,aAAa;MAEb;MACAzG,wBAAwB;MACxBC,WAAW;MACX0E,kBAAkB;MAClByC,cAAc;MAEd;MACAlH,uBAAuB;MACvBC,oBAAoB;MACpBE,gBAAgB;MAChBqJ,iBAAiB;MACjBC,wBAAwB;MACxBC,iBAAiB;MACjBC,wBAAwB;MAExB;MACAvJ,sBAAsB;MACtBC,sBAAsB;MACtB8J,uBAAuB;MACvBC;IACF;EACF;AACF", "ignoreList": []}]}