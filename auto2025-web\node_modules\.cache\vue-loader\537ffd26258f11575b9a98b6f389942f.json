{"remainingRequest": "J:\\auto2025\\auto2025-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!J:\\auto2025\\auto2025-web\\src\\views\\web\\CodeGenerator.vue?vue&type=style&index=0&id=c61fc0be&scoped=true&lang=css", "dependencies": [{"path": "J:\\auto2025\\auto2025-web\\src\\views\\web\\CodeGenerator.vue", "mtime": 1749465006927}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748675479377}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 1748675490923}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748675483546}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748675476717}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1748675485115}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["J:\\auto2025\\auto2025-web\\src\\views\\web\\CodeGenerator.vue"], "names": [], "mappings": ";EAmlCE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAExC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB;;EAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACpB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB;;EAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACX;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB;;EAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/B;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB", "file": "J:/auto2025/auto2025-web/src/views/web/CodeGenerator.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"code-generator-container\">\n    <!-- 用户信息浮动显示 -->\n    <div v-if=\"isLoggedIn\" class=\"user-info-float\">\n      <el-dropdown @command=\"handleUserCommand\">\n        <span class=\"user-info-trigger\">\n          <el-icon>\n            <User />\n          </el-icon>\n          {{ userInfo.username }}\n          <el-icon class=\"el-icon--right\">\n            <ArrowDown />\n          </el-icon>\n        </span>\n        <template #dropdown>\n          <el-dropdown-menu>\n            <el-dropdown-item command=\"logout\">退出登录</el-dropdown-item>\n          </el-dropdown-menu>\n        </template>\n      </el-dropdown>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"main-content\">\n      <el-tabs v-model=\"activeTab\" type=\"card\" class=\"generator-tabs\" @tab-click=\"handleTabClick\">\n        <!-- 设置项目 -->\n        <el-tab-pane label=\"设置项目\" name=\"project\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <Folder />\n              </el-icon>\n              设置项目\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <!-- 页面标题 -->\n            <div class=\"page-header\">\n              <div class=\"header-content\">\n                <h1 class=\"page-title\">\n                  <el-icon class=\"title-icon\">\n                    <Setting />\n                  </el-icon>\n                  代码生成器\n                </h1>\n                <p class=\"page-description\">快速生成高质量的代码，提升开发效率</p>\n              </div>\n            </div>\n\n            <!-- 项目类型选择 -->\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>支持的项目类型</h3>\n                <p>选择您需要的技术栈，快速生成项目模板</p>\n              </div>\n\n              <!-- 功能卡片网格 -->\n              <div class=\"features-grid\">\n                <div class=\"feature-card\" @click=\"selectProject('springboot-thymeleaf')\"\n                  :class=\"{ active: selectedProject === 'springboot-thymeleaf' }\">\n                  <h3>🍃 SpringBoot + Thymeleaf</h3>\n                  <p>传统的服务端渲染架构，适合企业级应用开发，集成Thymeleaf模板引擎</p>\n                </div>\n\n                <div class=\"feature-card\" @click=\"selectProject('springboot-miniprogram')\"\n                  :class=\"{ active: selectedProject === 'springboot-miniprogram' }\">\n                  <h3>📱 SpringBoot + 小程序</h3>\n                  <p>微信小程序后端API开发，提供完整的用户认证和数据管理功能</p>\n                </div>\n\n                <div class=\"feature-card\" @click=\"selectProject('springboot-vue')\"\n                  :class=\"{ active: selectedProject === 'springboot-vue' }\">\n                  <h3>⚡ SpringBoot + Vue</h3>\n                  <p>现代化前后端分离架构，Vue.js前端 + SpringBoot后端API</p>\n                </div>\n\n                <div class=\"feature-card\" @click=\"selectProject('ssm-vue')\"\n                  :class=\"{ active: selectedProject === 'ssm-vue' }\">\n                  <h3>🔧 SSM + Vue</h3>\n                  <p>经典的SSM框架（Spring + SpringMVC + MyBatis）配合Vue.js前端</p>\n                </div>\n              </div>\n            </div>\n\n            <!-- 项目配置区域 -->\n            <div v-if=\"selectedProject\" class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>项目配置 - {{ getProjectName(selectedProject) }}</h3>\n                <p>配置项目基本信息和生成参数</p>\n              </div>\n              <div class=\"form-section\">\n                <el-form :model=\"projectForm\" label-width=\"120px\" class=\"project-form\">\n                  <el-row :gutter=\"20\">\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"数据库类型\">\n                        <el-select v-model=\"projectForm.databaseType\" placeholder=\"请选择数据库类型\" style=\"width: 100%\">\n                          <el-option label=\"MySQL\" value=\"mysql\" />\n                          <el-option label=\"SQL Server\" value=\"sqlserver\" />\n                        </el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"数据库模式\">\n                        <el-select v-model=\"projectForm.databaseMode\" placeholder=\"请选择数据库模式\" style=\"width: 100%\"\n                          @change=\"handleDatabaseModeChange\">\n                          <el-option label=\"新建数据库\" value=\"new\" />\n                          <el-option label=\"已有数据库\" value=\"existing\" />\n                        </el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"项目中文名称\">\n                        <el-input v-model=\"projectForm.name\" placeholder=\"请输入项目中文名称\" />\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n\n                  <el-row :gutter=\"20\" v-if=\"projectForm.databaseMode === 'new'\">\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"项目编号\">\n                        <el-input v-model=\"projectForm.projectCode\" placeholder=\"项目编号\" />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"数据库名称\">\n                        <el-input v-model=\"projectForm.databaseName\" placeholder=\"请输入数据库名称\" />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"学校名称\">\n                        <el-input v-model=\"projectForm.schoolName\" placeholder=\"请输入学校名称\" />\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n\n                  <el-row :gutter=\"20\" v-if=\"projectForm.databaseMode === 'existing'\">\n                    <el-col :span=\"24\">\n                      <el-form-item label=\"选择数据库\">\n                        <el-select v-model=\"projectForm.selectedDatabase\" placeholder=\"请选择数据库\" style=\"width: 100%\"\n                          @change=\"handleProjectSelect\" :loading=\"projectsLoading\">\n                          <el-option v-for=\"db in availableDatabases\" :key=\"db.value\" :label=\"db.text\" :value=\"db.value\"\n                            :disabled=\"!db.value\" />\n                        </el-select>\n                        <div class=\"form-text\" v-if=\"availableDatabases.length > 0\">\n                          格式：项目编号--数据库名称 (项目中文名称)\n                        </div>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n\n                  <el-row :gutter=\"20\">\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"后台模板\">\n                        <el-input v-model=\"projectForm.backendTemplate\" placeholder=\"点击选择后台模板\" readonly\n                          @click=\"openTemplateModal\" />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"前台模板\">\n                        <el-input v-model=\"projectForm.frontendTemplate\" placeholder=\"点击选择前台模板\" readonly\n                          @click=\"openFrontTemplateModal\" />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"复制项目\">\n                        <el-input v-model=\"projectForm.copyProject\" placeholder=\"请输入要复制的项目\" />\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n\n                  <el-row :gutter=\"20\">\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"layer弹出层\">\n                        <el-select v-model=\"projectForm.layer\" style=\"width: 100%\">\n                          <el-option label=\"否\" value=\"否\" />\n                          <el-option label=\"是\" value=\"是\" />\n                        </el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"统计图表\">\n                        <el-select v-model=\"projectForm.charts\" style=\"width: 100%\">\n                          <el-option label=\"否\" value=\"否\" />\n                          <el-option label=\"是\" value=\"是\" />\n                        </el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <!-- 空列，保持布局平衡 -->\n                    </el-col>\n                  </el-row>\n\n                  <el-form-item label=\"Session信息\">\n                    <el-row :gutter=\"10\">\n                      <el-col :span=\"6\">\n                        <el-input v-model=\"projectForm.adminId\" placeholder=\"管理员ID\" />\n                      </el-col>\n                      <el-col :span=\"6\">\n                        <el-input v-model=\"projectForm.adminName\" placeholder=\"管理员姓名\" />\n                      </el-col>\n                      <el-col :span=\"6\">\n                        <el-input v-model=\"projectForm.adminRole\" placeholder=\"管理员角色\" />\n                      </el-col>\n                      <el-col :span=\"6\">\n                        <el-input v-model=\"projectForm.adminLoginName\" placeholder=\"登录名\" />\n                      </el-col>\n                    </el-row>\n                  </el-form-item>\n                </el-form>\n\n                <div class=\"form-actions\">\n                  <el-button type=\"primary\" @click=\"nextStep\" size=\"large\">\n                    🎯 下一步：设计表单\n                  </el-button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 设计表单 -->\n        <el-tab-pane label=\"设计表单\" name=\"form\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <Edit />\n              </el-icon>\n              设计表单\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>数据表管理</h3>\n                <p>设计您的数据库表结构和表单字段</p>\n              </div>\n\n              <!-- 项目信息显示 -->\n              <div v-if=\"currentProjectInfo\" class=\"project-info-section\">\n                <div class=\"project-info-card\">\n                  <h4>当前项目：{{ currentProjectInfo.name }}</h4>\n                  <div class=\"project-details\">\n                    <span class=\"project-detail-item\">\n                      <strong>项目编号：</strong>{{ currentProjectInfo.projectCode }}\n                    </span>\n                    <span class=\"project-detail-item\">\n                      <strong>数据库：</strong>{{ currentProjectInfo.databaseName }}\n                    </span>\n                    <span class=\"project-detail-item\">\n                      <strong>类型：</strong>{{ getProjectName(selectedProject) }}\n                    </span>\n                  </div>\n                </div>\n              </div>\n\n              <!-- 表单列表 -->\n              <div class=\"table-designer\">\n                <div class=\"table-list-header\">\n                  <h4>数据表列表</h4>\n                  <el-button type=\"primary\" :icon=\"Plus\" @click=\"openTableDesignModal()\">\n                    添加新表\n                  </el-button>\n                </div>\n\n                <div v-if=\"projectTablesLoading\" class=\"loading-section\">\n                  <el-icon class=\"is-loading\">\n                    <Loading />\n                  </el-icon>\n                  <span>正在加载表单数据...</span>\n                </div>\n\n                <div v-else-if=\"projectTables.length === 0\" class=\"empty-section\">\n                  <el-empty description=\"暂无数据表\">\n                    <el-button type=\"primary\" @click=\"openTableDesignModal()\">创建第一个表</el-button>\n                  </el-empty>\n                </div>\n\n                <div v-else class=\"table-list\">\n                  <div class=\"table-item\" v-for=\"table in projectTables\" :key=\"table.tid\"\n                    @click=\"openTableDesignModal(table)\">\n                    <div class=\"table-item-header\">\n                      <h5>{{ table.tword || table.tname }}</h5>\n                      <div class=\"table-item-actions\">\n                        <el-button size=\"small\" :icon=\"Edit\" @click.stop=\"openTableDesignModal(table)\">编辑</el-button>\n                        <el-button size=\"small\" type=\"danger\" :icon=\"Delete\"\n                          @click.stop=\"deleteTable(table)\">删除</el-button>\n                      </div>\n                    </div>\n                    <p class=\"table-item-description\">\n                      {{ table.tname }} ({{ getTableFieldCount(table) }}个字段)\n                    </p>\n                    <div class=\"table-item-functions\" v-if=\"table.tgn\">\n                      <span class=\"function-tag\" v-for=\"func in getTableFunctions(table)\" :key=\"func\">{{ func }}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"form-actions\" style=\"margin-top: 30px;\">\n                <el-button @click=\"activeTab = 'project'\" size=\"large\">\n                  ← 上一步：项目配置\n                </el-button>\n                <el-button type=\"primary\" @click=\"nextStep\" size=\"large\">\n                  下一步：生成项目 →\n                </el-button>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 生成项目 -->\n        <el-tab-pane label=\"生成项目\" name=\"generate\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <Cpu />\n              </el-icon>\n              生成项目\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>生成项目</h3>\n                <p>确认配置信息并生成您的项目</p>\n              </div>\n\n              <!-- 项目配置摘要 -->\n              <div class=\"generation-summary\">\n                <h4>项目配置摘要</h4>\n                <el-row :gutter=\"20\">\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">项目名称:</span>\n                      <span class=\"summary-value\">{{ projectForm.name || '未设置' }}</span>\n                    </div>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">数据库名称:</span>\n                      <span class=\"summary-value\">{{ projectForm.databaseName || '未设置' }}</span>\n                    </div>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">项目编号:</span>\n                      <span class=\"summary-value\">{{ projectForm.projectCode || '未设置' }}</span>\n                    </div>\n                  </el-col>\n                </el-row>\n                <el-row :gutter=\"20\" style=\"margin-top: 15px;\">\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">数据表数量:</span>\n                      <span class=\"summary-value\">{{ projectTables.length }} 个</span>\n                    </div>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">后台模板:</span>\n                      <span class=\"summary-value\">{{ projectForm.backendTemplate || '未选择' }}</span>\n                    </div>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <div class=\"summary-item\">\n                      <span class=\"summary-label\">前台模板:</span>\n                      <span class=\"summary-value\">{{ projectForm.frontendTemplate || '未选择' }}</span>\n                    </div>\n                  </el-col>\n                </el-row>\n              </div>\n\n              <!-- 生成操作区域 -->\n              <div class=\"generation-actions\">\n                <el-button\n                  type=\"primary\"\n                  size=\"large\"\n                  @click=\"generateProject\"\n                  :loading=\"generationInProgress\"\n                  :disabled=\"!canGenerate || generationInProgress\">\n                  <el-icon v-if=\"!generationInProgress\">\n                    <Cpu />\n                  </el-icon>\n                  <el-icon v-else>\n                    <Loading />\n                  </el-icon>\n                  {{ generationInProgress ? '正在生成项目，请稍候...' : '🚀 生成项目' }}\n                </el-button>\n\n                <!-- 生成进度提示 -->\n                <div v-if=\"generationInProgress\" class=\"generation-progress\">\n                  <el-progress :percentage=\"100\" :show-text=\"false\" status=\"success\" :indeterminate=\"true\" />\n                  <p class=\"progress-text\">正在生成代码文件和项目结构...</p>\n                </div>\n              </div>\n\n              <!-- 项目生成结果显示区域 -->\n              <div v-if=\"generationResult\" class=\"generation-result\">\n                <h4>项目生成结果</h4>\n\n                <!-- 生成状态 -->\n                <div class=\"generation-status\" :class=\"generationResult.status\">\n                  <el-icon class=\"status-icon\">\n                    <Check v-if=\"generationResult.status === 'success'\" />\n                    <Close v-else />\n                  </el-icon>\n                  <span class=\"status-text\">{{ generationResult.message }}</span>\n                </div>\n\n                <!-- 文件列表 -->\n                <div v-if=\"generationResult.files\" class=\"file-list-section\">\n                  <h5>生成的文件列表 ({{ generationResult.files.savedCount }}/{{ generationResult.files.totalCount }})</h5>\n\n                  <!-- 成功文件列表 -->\n                  <div v-if=\"generationResult.files.savedFiles && generationResult.files.savedFiles.length > 0\" class=\"success-files\">\n                    <h6>✅ 成功生成的文件:</h6>\n                    <el-scrollbar max-height=\"200px\">\n                      <ul class=\"file-list success\">\n                        <li v-for=\"file in generationResult.files.savedFiles\" :key=\"file\">\n                          <el-icon class=\"file-icon\"><Document /></el-icon>\n                          <span class=\"file-name\">{{ file }}</span>\n                        </li>\n                      </ul>\n                    </el-scrollbar>\n                  </div>\n\n                  <!-- 失败文件列表 -->\n                  <div v-if=\"generationResult.files.failedFiles && generationResult.files.failedFiles.length > 0\" class=\"failed-files\">\n                    <h6>❌ 生成失败的文件:</h6>\n                    <el-scrollbar max-height=\"200px\">\n                      <ul class=\"file-list error\">\n                        <li v-for=\"file in generationResult.files.failedFiles\" :key=\"file.fileName\">\n                          <el-icon class=\"file-icon\"><Warning /></el-icon>\n                          <span class=\"file-name\">{{ file.fileName }}</span>\n                          <span class=\"file-error\">{{ file.error }}</span>\n                        </li>\n                      </ul>\n                    </el-scrollbar>\n                  </div>\n                </div>\n\n                <!-- 压缩结果 -->\n                <div v-if=\"generationResult.compression\" class=\"compression-result\">\n                  <h5>项目压缩结果</h5>\n                  <div class=\"compression-info\" :class=\"generationResult.compression.status\">\n                    <el-icon class=\"status-icon\">\n                      <Box v-if=\"generationResult.compression.status === 'success'\" />\n                      <Close v-else />\n                    </el-icon>\n                    <span class=\"compression-text\">{{ generationResult.compression.message }}</span>\n                    <div v-if=\"generationResult.compression.data\" class=\"compression-details\">\n                      <p>文件名: {{ generationResult.compression.data.zipFileName }}</p>\n                      <p>文件大小: {{ generationResult.compression.data.fileSize }}</p>\n                      <el-button type=\"success\" @click=\"downloadProject(generationResult.compression.data.zipFilePath)\">\n                        <el-icon><Download /></el-icon>\n                        下载项目文件\n                      </el-button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 数据 -->\n        <el-tab-pane label=\"数据\" name=\"data\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <DataBoard />\n              </el-icon>\n              数据\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>数据脚本管理</h3>\n                <p>生成和管理数据库建表及插入数据脚本</p>\n              </div>\n              <div class=\"data-section\">\n                <div class=\"data-toolbar\">\n                  <el-button type=\"primary\" :icon=\"Plus\" @click=\"generateDataScript\" :loading=\"dataGenerationInProgress\">生成数据脚本</el-button>\n                  <el-button :icon=\"DocumentCopy\" @click=\"copyDataScript\">复制脚本</el-button>\n                </div>\n                <div class=\"data-editor\">\n                  <el-input v-model=\"dataContent\" type=\"textarea\" :rows=\"15\" placeholder=\"数据脚本将在这里显示...\"\n                    class=\"data-textarea\" />\n                </div>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- SQL脚本 -->\n        <el-tab-pane label=\"SQL脚本\" name=\"sql\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <DocumentCopy />\n              </el-icon>\n              SQL脚本\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>SQL脚本管理</h3>\n                <p>生成和管理数据库脚本</p>\n              </div>\n              <div class=\"sql-section\">\n                <div class=\"sql-toolbar\">\n                  <el-button type=\"primary\" :icon=\"Plus\" @click=\"generateSqlScript\" :loading=\"sqlGenerationInProgress\">生成建表脚本</el-button>\n                  <el-button :icon=\"Download\" @click=\"exportSqlScript\">导出脚本</el-button>\n                  <el-button :icon=\"DocumentCopy\" @click=\"copySqlScript\">复制脚本</el-button>\n                </div>\n                <div class=\"sql-editor\">\n                  <el-input v-model=\"sqlContent\" type=\"textarea\" :rows=\"25\" placeholder=\"SQL脚本将在这里显示...\"\n                    class=\"sql-textarea\" />\n                </div>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 错误日志 -->\n        <el-tab-pane label=\"错误日志\" name=\"logs\">\n          <template #label>\n            <span class=\"tab-label\">\n              <el-icon>\n                <Warning />\n              </el-icon>\n              错误日志\n            </span>\n          </template>\n          <div class=\"tab-content\">\n            <div class=\"content-card\">\n              <div class=\"card-header\">\n                <h3>错误日志</h3>\n                <p>查看生成过程中的错误和警告信息</p>\n              </div>\n              <div class=\"logs-section\">\n                <div class=\"logs-toolbar\">\n                  <el-button :icon=\"Refresh\">刷新日志</el-button>\n                  <el-button :icon=\"Delete\">清空日志</el-button>\n                  <el-select v-model=\"logLevel\" placeholder=\"日志级别\" style=\"width: 120px\">\n                    <el-option label=\"全部\" value=\"all\" />\n                    <el-option label=\"错误\" value=\"error\" />\n                    <el-option label=\"警告\" value=\"warning\" />\n                    <el-option label=\"信息\" value=\"info\" />\n                  </el-select>\n                </div>\n                <div class=\"logs-content\">\n                  <div class=\"log-item\" v-for=\"(log, index) in logs\" :key=\"index\" :class=\"log.level\">\n                    <div class=\"log-time\">{{ log.time }}</div>\n                    <div class=\"log-level\">{{ log.level.toUpperCase() }}</div>\n                    <div class=\"log-message\">{{ log.message }}</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </el-tab-pane>\n      </el-tabs>\n    </div>\n\n    <!-- 登录对话框 -->\n    <el-dialog v-model=\"loginDialogVisible\" title=\"用户登录\" width=\"400px\" :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\" :show-close=\"false\">\n      <el-form :model=\"loginForm\" :rules=\"loginRules\" ref=\"loginFormRef\" label-width=\"80px\">\n        <el-form-item label=\"用户名\" prop=\"username\">\n          <el-input v-model=\"loginForm.username\" placeholder=\"请输入用户名\" />\n        </el-form-item>\n        <el-form-item label=\"密码\" prop=\"password\">\n          <el-input v-model=\"loginForm.password\" type=\"password\" placeholder=\"请输入密码\" @keyup.enter=\"handleLogin\" />\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button type=\"primary\" @click=\"handleLogin\" :loading=\"loginLoading\">\n            登录\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n\n    <!-- 表设计弹窗 -->\n    <el-dialog v-model=\"showTableDesignModal\" :title=\"currentTableDesign.id ? '编辑数据表' : '创建数据表'\" width=\"85%\"\n      :close-on-click-modal=\"false\" :before-close=\"closeTableDesignModal\" class=\"table-design-dialog\" top=\"5vh\"\n      destroy-on-close>\n      <div class=\"table-design-container\">\n        <!-- 自定义Tab导航 -->\n        <div class=\"custom-tabs\">\n          <div class=\"tab-nav\">\n            <div class=\"tab-item\" :class=\"{ active: currentTableTab === 'table-settings' }\"\n              @click=\"switchTableTab('table-settings')\">\n              <el-icon class=\"tab-icon\">\n                <Setting />\n              </el-icon>\n              <span class=\"tab-text\">表设置</span>\n            </div>\n            <div class=\"tab-item\" :class=\"{ active: currentTableTab === 'field-design' }\"\n              @click=\"switchTableTab('field-design')\">\n              <el-icon class=\"tab-icon\">\n                <DataBoard />\n              </el-icon>\n              <span class=\"tab-text\">字段设计</span>\n            </div>\n          </div>\n\n          <!-- Tab内容 -->\n          <div class=\"tab-content-wrapper\">\n            <!-- 表设置内容 -->\n            <div v-show=\"currentTableTab === 'table-settings'\" class=\"tab-content\">\n              <!-- 基本信息 -->\n              <div class=\"basic-info-section\">\n                <el-form :model=\"currentTableDesign\" label-width=\"100px\" class=\"design-form\" size=\"default\">\n                  <el-row :gutter=\"16\">\n                    <el-col :span=\"6\">\n                      <el-form-item label=\"表中文名称\" required>\n                        <el-input v-model=\"currentTableDesign.chineseName\" placeholder=\"请输入表的中文名称\" clearable />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"6\">\n                      <el-form-item label=\"表英文名称\" required>\n                        <el-input v-model=\"currentTableDesign.englishName\" placeholder=\"请输入表的英文名称\" clearable />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"6\">\n                      <el-form-item label=\"菜单显示顺序\">\n                        <el-input-number v-model=\"currentTableDesign.menuOrder\" :min=\"1\" :max=\"999\" placeholder=\"1\" style=\"width: 100%;\" />\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"6\">\n                      <el-form-item label=\"生成数据条数\">\n                        <el-input-number v-model=\"currentTableDesign.generateDataCount\" :min=\"0\" :max=\"1000\"\n                          placeholder=\"生成数据条数\" style=\"width: 100%;\" />\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n\n                  <el-row :gutter=\"16\">\n                    <el-col :span=\"24\">\n                      <el-form-item label=\"AI助手\">\n                        <el-button type=\"primary\" @click=\"showAiGeneratorModal('table')\" style=\"width: 100%;\">\n                          <el-icon>\n                            <Cpu />\n                          </el-icon>\n                          AI生成表结构\n                        </el-button>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                </el-form>\n              </div>\n\n              <!-- 功能选择 -->\n              <div class=\"function-selection-section\">\n                <h4 style=\"margin: 20px 0 15px 0; color: #2c3e50; font-size: 16px;\">功能选择</h4>\n                <el-row :gutter=\"20\">\n                  <!-- 后台功能 -->\n                  <el-col :span=\"8\">\n                    <div class=\"function-group\">\n                      <h5>后台功能</h5>\n                      <div class=\"function-checkboxes\">\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendAdd\">后台添加</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendEdit\">后台修改</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendDelete\">后台删除</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendDetail\">后台详情</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendList\">后台列表</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.batchImport\">批量导入</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.batchExport\">批量导出</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendLogin\">后台登录</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendRegister\">后台注册</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendProfile\">后台个人信息</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.backendPassword\">后台修改密码</el-checkbox>\n                      </div>\n                    </div>\n                  </el-col>\n\n                  <!-- 前台功能 -->\n                  <el-col :span=\"8\">\n                    <div class=\"function-group\">\n                      <h5>前台功能</h5>\n                      <div class=\"function-checkboxes\">\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendAdd\">前台添加</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendEdit\">前台修改</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendDelete\">前台删除</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendDetail\">前台详情</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendList\">前台列表</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.frontendLogin\">前台个人信息和密码</el-checkbox>\n                      </div>\n                    </div>\n                  </el-col>\n\n                  <!-- 小程序功能 -->\n                  <el-col :span=\"8\">\n                    <div class=\"function-group\">\n                      <h5>小程序功能</h5>\n                      <div class=\"function-checkboxes\">\n                        <el-checkbox v-model=\"currentTableDesign.functions.miniAdd\">小程序添加</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.miniEdit\">小程序编辑</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.miniDelete\">小程序删除</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.miniDetail\">小程序详情</el-checkbox>\n                        <el-checkbox v-model=\"currentTableDesign.functions.miniList\">小程序List</el-checkbox>\n                      </div>\n                    </div>\n                  </el-col>\n                </el-row>\n              </div>\n            </div>\n\n            <!-- 字段设计内容 -->\n            <div v-show=\"currentTableTab === 'field-design'\" class=\"tab-content\">\n              <!-- 工具栏 -->\n              <div class=\"field-toolbar-compact\">\n                <el-space size=\"default\" wrap>\n                  <el-button type=\"success\" @click=\"addNewFieldToDesign\">\n                    <el-icon>\n                      <Plus />\n                    </el-icon>\n                    添加字段\n                  </el-button>\n                  <el-button type=\"warning\" @click=\"resetFields\">\n                    <el-icon>\n                      <Refresh />\n                    </el-icon>\n                    重置字段\n                  </el-button>\n                  <el-button type=\"danger\" @click=\"clearAllFields\">\n                    <el-icon>\n                      <Delete />\n                    </el-icon>\n                    清空字段\n                  </el-button>\n                  <el-divider direction=\"vertical\" />\n                  <el-text type=\"info\">\n                    当前字段数量: {{ currentTableDesign.fields?.length || 0 }}\n                  </el-text>\n                </el-space>\n              </div>\n\n              <!-- 字段表格 -->\n              <div class=\"field-table-section\">\n                <el-table :data=\"currentTableDesign.fields\" class=\"field-table\" border stripe\n                  empty-text=\"暂无字段，请点击添加字段按钮\" size=\"small\">\n                  <el-table-column label=\"序号\" type=\"index\" width=\"60\" align=\"center\" />\n\n                  <el-table-column label=\"中文名称\" min-width=\"100\">\n                    <template #default=\"{ row }\">\n                      <el-input v-model=\"row.chineseName\" placeholder=\"请输入中文名称\" size=\"small\" clearable />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"英文名称\" min-width=\"100\">\n                    <template #default=\"{ row }\">\n                      <el-input v-model=\"row.englishName\" placeholder=\"请输入英文名称\" size=\"small\" clearable />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"字段类型\" min-width=\"80\">\n                    <template #default=\"{ row }\">\n                      <el-select v-model=\"row.type\" placeholder=\"选择类型\" size=\"small\" style=\"width: 100%\">\n                        <el-option label=\"int\" value=\"int\" />\n                        <el-option label=\"varchar(50)\" value=\"varchar(50)\" />\n                        <el-option label=\"varchar(100)\" value=\"varchar(100)\" />\n                        <el-option label=\"varchar(200)\" value=\"varchar(200)\" />\n                        <el-option label=\"varchar(500)\" value=\"varchar(500)\" />\n                        <el-option label=\"text\" value=\"text\" />\n                        <el-option label=\"datetime\" value=\"datetime\" />\n                        <el-option label=\"decimal(10,2)\" value=\"decimal(10,2)\" />\n                      </el-select>\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"控件类型\" min-width=\"100\">\n                    <template #default=\"{ row }\">\n                      <el-select v-model=\"row.controlType\" placeholder=\"选择控件\" size=\"small\" style=\"width: 100%\">\n                        <el-option label=\"文本框\" value=\"文本框\" />\n                        <el-option label=\"多行文本\" value=\"多行文本\" />\n                        <el-option label=\"下拉框\" value=\"下拉框\" />\n                        <el-option label=\"单选按钮\" value=\"单选按钮\" />\n                        <el-option label=\"复选框\" value=\"复选框\" />\n                        <el-option label=\"日期选择\" value=\"日期选择\" />\n                        <el-option label=\"时间选择\" value=\"时间选择\" />\n                        <el-option label=\"文件上传\" value=\"文件上传\" />\n                        <el-option label=\"图片上传\" value=\"图片上传\" />\n                        <el-option label=\"编辑器\" value=\"编辑器\" />\n                        <el-option label=\"自动当前时间\" value=\"自动当前时间\" />\n                      </el-select>\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"必填\" width=\"60\" align=\"center\">\n                    <template #default=\"{ row }\">\n                      <el-checkbox v-model=\"row.required\" />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"搜索\" width=\"60\" align=\"center\">\n                    <template #default=\"{ row }\">\n                      <el-checkbox v-model=\"row.searchable\" />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"显示\" width=\"60\" align=\"center\">\n                    <template #default=\"{ row }\">\n                      <el-checkbox v-model=\"row.visible\" />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"存在\" width=\"60\" align=\"center\">\n                    <template #default=\"{ row }\">\n                      <el-checkbox v-model=\"row.existsCheck\" />\n                    </template>\n                  </el-table-column>\n\n                  <el-table-column label=\"操作\" width=\"160\" align=\"center\" fixed=\"right\">\n                    <template #default=\"{ row, $index }\">\n                      <div class=\"field-actions\">\n                        <el-button size=\"small\" type=\"primary\" @click=\"moveFieldUp($index)\" :disabled=\"$index === 0\"\n                          circle>\n                          <el-icon>\n                            <ArrowUp />\n                          </el-icon>\n                        </el-button>\n                        <el-button size=\"small\" type=\"primary\" @click=\"moveFieldDown($index)\"\n                          :disabled=\"$index === currentTableDesign.fields.length - 1\" circle>\n                          <el-icon>\n                            <ArrowDown />\n                          </el-icon>\n                        </el-button>\n                        <el-button size=\"small\" type=\"warning\" @click=\"editFieldSettings(row, $index)\" circle>\n                          <el-icon>\n                            <Edit />\n                          </el-icon>\n                        </el-button>\n                        <el-button size=\"small\" type=\"danger\" @click=\"deleteFieldFromDesign($index)\" circle>\n                          <el-icon>\n                            <Delete />\n                          </el-icon>\n                        </el-button>\n                      </div>\n                    </template>\n                  </el-table-column>\n                </el-table>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-space size=\"large\">\n            <el-button @click=\"closeTableDesignModal\" size=\"large\">\n              <el-icon>\n                <Close />\n              </el-icon>\n              <span>取消</span>\n            </el-button>\n            <el-button type=\"primary\" @click=\"saveTableDesign\" size=\"large\">\n              <el-icon>\n                <Check />\n              </el-icon>\n              <span>保存表设计</span>\n            </el-button>\n          </el-space>\n        </div>\n      </template>\n    </el-dialog>\n\n    <!-- AI生成器弹窗 -->\n    <el-dialog v-model=\"showAiGeneratorDialog\" title=\"AI生成表结构\" width=\"600px\" :close-on-click-modal=\"false\"\n      :before-close=\"hideAiGeneratorModal\" class=\"ai-generator-dialog\" destroy-on-close>\n      <div class=\"ai-generator-container\">\n        <div class=\"ai-generator-header\">\n          <h4>🤖 AI智能生成表结构</h4>\n          <p>描述您要生成的表结构，AI将自动为您创建完整的数据表设计</p>\n        </div>\n\n        <div class=\"ai-generator-body\">\n          <el-form label-width=\"100px\">\n            <el-form-item label=\"表描述\">\n              <el-input v-model=\"aiGeneratorInput\" type=\"textarea\" :rows=\"4\"\n                placeholder=\"请输入表的描述，例如：学生信息管理表、商品管理系统、订单管理表等...\" class=\"ai-generator-input\"\n                :disabled=\"aiGenerationInProgress\" />\n            </el-form-item>\n          </el-form>\n\n          <div class=\"ai-generator-example\">\n            <h5>💡 示例：</h5>\n            <div class=\"example-list\">\n              <div class=\"example-item\">\n                <strong>表结构描述：</strong>\"学生信息管理表\" 或 \"商品管理系统\" 或 \"订单管理表\"\n              </div>\n              <div class=\"example-item\">\n                <strong>功能说明：</strong>AI会根据您的描述自动生成包含合适字段的完整表结构\n              </div>\n            </div>\n          </div>\n        </div>\n\n      </div>\n\n      <template #footer>\n        <div class=\"ai-generator-footer\">\n          <el-button @click=\"hideAiGeneratorModal\" :disabled=\"aiGenerationInProgress\">取消</el-button>\n          <el-button type=\"primary\" @click=\"confirmAiGeneration\" :loading=\"aiGenerationInProgress\">\n            <el-icon v-if=\"!aiGenerationInProgress\">\n              <Cpu />\n            </el-icon>\n            {{ aiGenerationInProgress ? '生成中...' : '🚀 生成' }}\n          </el-button>\n        </div>\n      </template>\n    </el-dialog>\n\n    <!-- 后台模板选择弹窗 -->\n    <el-dialog v-model=\"showBackendTemplateDialog\" title=\"选择后台模板\" width=\"1000px\" :close-on-click-modal=\"false\"\n      class=\"template-dialog\" destroy-on-close>\n      <div class=\"template-container\">\n        <div class=\"template-grid-4col\" v-loading=\"templatesLoading\">\n          <div v-for=\"template in backendTemplates\" :key=\"template.sid\" class=\"template-item-4col\">\n            <div class=\"template-image\" @click=\"selectBackendTemplate(template)\">\n              <img v-if=\"template.memo4\" :src=\"getTemplateImageUrl(template.memo4)\" :alt=\"template.sname\"\n                @error=\"handleImageError\" />\n              <div v-else class=\"no-image\">\n                <el-icon>\n                  <Picture />\n                </el-icon>\n                <span>暂无预览</span>\n              </div>\n            </div>\n            <div class=\"template-info\">\n              <h4 @click=\"selectBackendTemplate(template)\">{{ template.sname }}</h4>\n              <p class=\"template-id\">模板ID: {{ template.sid }}</p>\n              <div class=\"template-actions\">\n                <el-button type=\"primary\" size=\"small\" @click=\"selectBackendTemplate(template)\">\n                  选择模板\n                </el-button>\n                <el-button type=\"info\" size=\"small\" @click=\"viewTemplateDetail(template)\">\n                  查看详情\n                </el-button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-dialog>\n\n    <!-- 前台模板选择弹窗 -->\n    <el-dialog v-model=\"showFrontendTemplateDialog\" title=\"选择前台模板\" width=\"1000px\" :close-on-click-modal=\"false\"\n      class=\"template-dialog\" destroy-on-close>\n      <div class=\"template-container\">\n        <div class=\"template-grid-4col\" v-loading=\"templatesLoading\">\n          <div v-for=\"template in frontendTemplates\" :key=\"template.sid\" class=\"template-item-4col\">\n            <div class=\"template-image\" @click=\"selectFrontendTemplate(template)\">\n              <img v-if=\"template.memo4\" :src=\"getTemplateImageUrl(template.memo4)\" :alt=\"template.sname\"\n                @error=\"handleImageError\" />\n              <div v-else class=\"no-image\">\n                <el-icon>\n                  <Picture />\n                </el-icon>\n                <span>暂无预览</span>\n              </div>\n            </div>\n            <div class=\"template-info\">\n              <h4 @click=\"selectFrontendTemplate(template)\">{{ template.sname }}</h4>\n              <p class=\"template-id\">模板ID: {{ template.sid }}</p>\n              <div class=\"template-actions\">\n                <el-button type=\"primary\" size=\"small\" @click=\"selectFrontendTemplate(template)\">\n                  选择模板\n                </el-button>\n                <el-button type=\"info\" size=\"small\" @click=\"viewTemplateDetail(template)\">\n                  查看详情\n                </el-button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-dialog>\n\n    <!-- 模板详情查看弹窗 -->\n    <el-dialog v-model=\"showTemplateDetailDialog\" title=\"模板详情\" width=\"90%\" :close-on-click-modal=\"false\"\n      class=\"template-detail-dialog\" destroy-on-close top=\"5vh\">\n      <div class=\"template-detail-container\" v-if=\"currentTemplateDetail\">\n        <div class=\"template-detail-header\">\n          <h3>{{ currentTemplateDetail.sname }}</h3>\n          <p class=\"template-detail-id\">模板ID: {{ currentTemplateDetail.sid }}</p>\n        </div>\n        <div class=\"template-detail-image-container\">\n          <div v-if=\"currentTemplateDetail.memo4\" class=\"template-detail-image\">\n            <el-image\n              :src=\"getTemplateImageUrl(currentTemplateDetail.memo4)\"\n              :alt=\"currentTemplateDetail.sname\"\n              fit=\"contain\"\n              :preview-src-list=\"[getTemplateImageUrl(currentTemplateDetail.memo4)]\"\n              :initial-index=\"0\"\n              preview-teleported\n              class=\"template-preview-image\"\n            />\n          </div>\n          <div v-else class=\"no-image-large\">\n            <el-icon>\n              <Picture />\n            </el-icon>\n            <span>暂无预览图片</span>\n          </div>\n        </div>\n        <div class=\"template-detail-tips\">\n          <el-alert\n            title=\"提示\"\n            type=\"info\"\n            :closable=\"false\"\n            show-icon>\n            <template #default>\n              点击图片可以放大查看，支持缩放和拖拽操作\n            </template>\n          </el-alert>\n        </div>\n      </div>\n      <template #footer>\n        <div class=\"template-detail-footer\">\n          <el-button @click=\"showTemplateDetailDialog = false\" size=\"large\">关闭</el-button>\n        </div>\n      </template>\n    </el-dialog>\n\n    <!-- 字段设置弹窗 -->\n    <el-dialog v-model=\"showFieldSettingsDialog\" title=\"字段设置\" width=\"600px\" :close-on-click-modal=\"false\"\n      class=\"field-settings-dialog\" destroy-on-close>\n      <div class=\"field-settings-container\" v-if=\"currentFieldSettings\">\n        <el-form :model=\"currentFieldSettings\" label-width=\"100px\" size=\"default\">\n          <el-form-item label=\"字段名称\">\n            <el-input v-model=\"currentFieldSettings.chineseName\" readonly />\n          </el-form-item>\n\n          <el-form-item label=\"关联表\">\n            <el-input v-model=\"currentFieldSettings.relatedTable\"\n              placeholder=\"例如：doro,dbid,dormitory,1\"\n              @click=\"showRelatedTableSelector\"\n              readonly\n              style=\"cursor: pointer;\">\n              <template #suffix>\n                <el-icon style=\"cursor: pointer;\">\n                  <View />\n                </el-icon>\n              </template>\n            </el-input>\n          </el-form-item>\n\n          <el-form-item label=\"是否必填\">\n            <el-checkbox v-model=\"showInSearchList\">搜索列表显示</el-checkbox>\n          </el-form-item>\n\n          <el-form-item label=\"自定义选项\">\n            <el-input v-model=\"currentFieldSettings.customOptions\"\n              type=\"textarea\"\n              :rows=\"6\"\n              placeholder=\"一行一个选项，例如：&#10;选项1&#10;选项2&#10;选项3\" />\n          </el-form-item>\n        </el-form>\n      </div>\n\n      <template #footer>\n        <div class=\"field-settings-footer\">\n          <el-button @click=\"closeFieldSettingsDialog\">取消</el-button>\n          <el-button type=\"primary\" @click=\"saveFieldSettings\">保存</el-button>\n        </div>\n      </template>\n    </el-dialog>\n\n    <!-- 关联表选择弹窗 -->\n    <el-dialog v-model=\"showRelatedTableDialog\" title=\"选择关联表\" width=\"800px\" :close-on-click-modal=\"false\"\n      class=\"related-table-dialog\" destroy-on-close>\n      <div class=\"related-table-container\">\n        <el-table :data=\"availableRelatedTables\" border stripe size=\"small\" max-height=\"400\">\n          <el-table-column label=\"主键ID\" prop=\"primaryKey\" width=\"80\" align=\"center\" />\n          <el-table-column label=\"名称\" prop=\"displayName\" width=\"120\" />\n          <el-table-column label=\"表名称\" prop=\"tableName\" width=\"150\" />\n          <el-table-column label=\"联动\" width=\"100\" align=\"center\">\n            <template #default=\"{ row }\">\n              <el-input v-model=\"row.linkValue\" size=\"small\" style=\"width: 60px;\" />\n            </template>\n          </el-table-column>\n          <el-table-column label=\"选择\" width=\"80\" align=\"center\">\n            <template #default=\"{ row }\">\n              <el-checkbox v-model=\"row.selected\" />\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n\n      <template #footer>\n        <div class=\"related-table-footer\">\n          <el-button @click=\"closeRelatedTableDialog\">取消</el-button>\n          <el-button type=\"primary\" @click=\"confirmRelatedTableSelection\">确定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<style scoped>\n  @import '../../styles/CodeGenerator.css';\n\n  /* 生成进度样式 */\n  .generation-progress {\n    margin-top: 20px;\n    padding: 20px;\n    background: #f8f9fa;\n    border-radius: 8px;\n    text-align: center;\n  }\n\n  .progress-text {\n    margin-top: 10px;\n    color: #606266;\n    font-size: 14px;\n  }\n\n  /* 生成结果样式 */\n  .generation-result {\n    margin-top: 30px;\n    padding: 20px;\n    border: 1px solid #e4e7ed;\n    border-radius: 8px;\n    background: #fff;\n  }\n\n  .generation-status {\n    display: flex;\n    align-items: center;\n    margin-bottom: 20px;\n    padding: 15px;\n    border-radius: 6px;\n  }\n\n  .generation-status.success {\n    background: #f0f9ff;\n    border: 1px solid #67c23a;\n    color: #67c23a;\n  }\n\n  .generation-status.error {\n    background: #fef0f0;\n    border: 1px solid #f56c6c;\n    color: #f56c6c;\n  }\n\n  .status-icon {\n    margin-right: 10px;\n    font-size: 18px;\n  }\n\n  .status-text {\n    font-weight: 500;\n    font-size: 16px;\n  }\n\n  /* 文件列表样式 */\n  .file-list-section {\n    margin-top: 20px;\n  }\n\n  .file-list {\n    list-style: none;\n    padding: 0;\n    margin: 0;\n  }\n\n  .file-list li {\n    display: flex;\n    align-items: center;\n    padding: 8px 12px;\n    margin-bottom: 4px;\n    border-radius: 4px;\n    background: #f8f9fa;\n  }\n\n  .file-list.success li {\n    background: #f0f9ff;\n    border-left: 3px solid #67c23a;\n  }\n\n  .file-list.error li {\n    background: #fef0f0;\n    border-left: 3px solid #f56c6c;\n  }\n\n  .file-icon {\n    margin-right: 8px;\n    color: #909399;\n  }\n\n  .file-name {\n    flex: 1;\n    font-family: 'Courier New', monospace;\n    font-size: 13px;\n  }\n\n  .file-error {\n    color: #f56c6c;\n    font-size: 12px;\n    margin-left: 10px;\n  }\n\n  /* 压缩结果样式 */\n  .compression-result {\n    margin-top: 20px;\n    padding: 15px;\n    border: 1px solid #e4e7ed;\n    border-radius: 6px;\n    background: #fafafa;\n  }\n\n  .compression-info {\n    display: flex;\n    align-items: center;\n    flex-wrap: wrap;\n  }\n\n  .compression-text {\n    margin-left: 10px;\n    font-weight: 500;\n  }\n\n  .compression-details {\n    width: 100%;\n    margin-top: 15px;\n    padding-top: 15px;\n    border-top: 1px solid #e4e7ed;\n  }\n\n  .compression-details p {\n    margin: 5px 0;\n    color: #606266;\n  }\n</style>\n\n<script>\n  import { ref, reactive, onMounted, nextTick, computed } from 'vue'\n  import { ElMessage, ElMessageBox } from 'element-plus'\n  import request, { base } from \"../../../utils/http\"\n  import {\n    Setting, Folder, Edit, Cpu, DataBoard, DocumentCopy, Warning,\n    Plus, View, Download, Delete, Document, Monitor, Box, Refresh,\n    User, ArrowDown, ArrowUp, Loading, Close, Check, Picture\n  } from '@element-plus/icons-vue'\n\n  export default {\n    name: 'CodeGenerator',\n    components: {\n      Setting, Folder, Edit, Cpu, DataBoard, DocumentCopy, Warning,\n      Plus, View, Download, Delete, Document, Monitor, Box, Refresh,\n      User, ArrowDown, ArrowUp, Loading, Close, Check, Picture\n    },\n    setup() {\n      const activeTab = ref('project')\n      const logLevel = ref('all')\n\n      // 登录相关状态\n      const isLoggedIn = ref(false)\n      const loginDialogVisible = ref(false)\n      const loginLoading = ref(false)\n      const loginFormRef = ref(null)\n\n      // 用户信息\n      const userInfo = reactive({\n        username: '',\n        loginTime: ''\n      })\n\n      // 登录表单\n      const loginForm = reactive({\n        username: '',\n        password: ''\n      })\n\n      // 登录表单验证规则\n      const loginRules = {\n        username: [\n          { required: true, message: '请输入用户名', trigger: 'blur' }\n        ],\n        password: [\n          { required: true, message: '请输入密码', trigger: 'blur' }\n        ]\n      }\n\n      // 项目选择和配置\n      const selectedProject = ref('')\n      const projectsLoading = ref(false)\n      const availableDatabases = ref([\n        { value: '', text: '请选择数据库' }\n      ])\n\n      const projectForm = reactive({\n        databaseType: 'mysql',\n        databaseMode: 'new',\n        projectCode: '',\n        databaseName: '',\n        selectedDatabase: '',\n        name: '',\n        packageName: 'com',\n        backendTemplate: '',\n        frontendTemplate: '',\n        layer: '否',\n        charts: '否',\n        schoolName: '',\n        adminId: '',\n        adminName: '',\n        adminRole: '',\n        adminLoginName: '',\n        copyProject: ''\n      })\n\n      // 项目表单相关\n      const currentProjectInfo = ref(null)\n      const projectTables = ref([])\n      const projectTablesLoading = ref(false)\n\n      const formFields = ref([\n        { name: 'id', type: 'Long' },\n        { name: 'name', type: 'String' },\n        { name: 'email', type: 'String' },\n        { name: 'createTime', type: 'Date' }\n      ])\n\n      const tableData = ref([\n        { name: 'user', fields: 8, status: '已配置', updateTime: '2024-01-15 10:30:00' },\n        { name: 'role', fields: 5, status: '未配置', updateTime: '2024-01-15 09:15:00' },\n        { name: 'permission', fields: 6, status: '已配置', updateTime: '2024-01-14 16:45:00' }\n      ])\n\n      const sqlContent = ref(``)\n\n      const logs = ref([\n        { time: '2024-01-15 10:30:15', level: 'info', message: '开始生成代码...' },\n        { time: '2024-01-15 10:30:16', level: 'success', message: '实体类生成成功' },\n        { time: '2024-01-15 10:30:17', level: 'warning', message: '字段名称建议使用驼峰命名' },\n        { time: '2024-01-15 10:30:18', level: 'error', message: '数据库连接失败，请检查配置' }\n      ])\n\n      // Cookie操作工具函数\n      const setCookie = (name, value, days) => {\n        const expires = new Date()\n        expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000))\n        document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`\n      }\n\n      const getCookie = (name) => {\n        const nameEQ = name + \"=\"\n        const ca = document.cookie.split(';')\n        for (let i = 0; i < ca.length; i++) {\n          let c = ca[i]\n          while (c.charAt(0) === ' ') c = c.substring(1, c.length)\n          if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length)\n        }\n        return null\n      }\n\n      const deleteCookie = (name) => {\n        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`\n      }\n\n      // 检查登录状态\n      const checkLoginStatus = () => {\n        // 首先检查sessionStorage中的用户信息\n        const sessionUser = sessionStorage.getItem(\"user\")\n        const sessionUserLname = sessionStorage.getItem(\"userLname\")\n\n        if (sessionUser && sessionUserLname) {\n          try {\n            JSON.parse(sessionUser) // 验证JSON格式\n            userInfo.username = sessionUserLname\n            userInfo.loginTime = new Date().toLocaleString()\n            isLoggedIn.value = true\n            return\n          } catch (error) {\n            console.error('解析sessionStorage用户信息失败:', error)\n          }\n        }\n\n        // 如果sessionStorage中没有，再检查Cookie\n        const savedUser = getCookie('codeGeneratorUser')\n        if (savedUser) {\n          try {\n            const userData = JSON.parse(decodeURIComponent(savedUser))\n            userInfo.username = userData.username\n            userInfo.loginTime = userData.loginTime\n            isLoggedIn.value = true\n          } catch (error) {\n            console.error('解析Cookie用户信息失败:', error)\n            deleteCookie('codeGeneratorUser')\n          }\n        } else {\n          loginDialogVisible.value = true\n        }\n      }\n\n      // 处理登录\n      const handleLogin = async () => {\n        if (!loginFormRef.value) return\n\n        // 基本验证\n        if (!loginForm.username) {\n          ElMessage.warning('请输入用户名')\n          return\n        }\n        if (!loginForm.password) {\n          ElMessage.warning('请输入密码')\n          return\n        }\n\n        try {\n          loginLoading.value = true\n\n          // 调用登录API\n          const url = base + \"/admin/login\"\n          const loginData = {\n            lname: loginForm.username,\n            pwd: loginForm.password\n          }\n\n          const res = await request.post(url, loginData)\n          loginLoading.value = false\n\n          if (res.code == 200) {\n            console.log('登录成功:', JSON.stringify(res.resdata))\n\n            // 保存用户信息到sessionStorage（参考管理员登录）\n            sessionStorage.setItem(\"user\", JSON.stringify(res.resdata))\n            sessionStorage.setItem(\"userLname\", res.resdata.lname)\n            sessionStorage.setItem(\"role\", \"管理员\")\n\n            // 更新本地状态\n            userInfo.username = res.resdata.lname\n            userInfo.loginTime = new Date().toLocaleString()\n\n            // 保存到Cookie，有效期15天\n            const userData = {\n              username: res.resdata.lname,\n              loginTime: userInfo.loginTime,\n              userId: res.resdata.id\n            }\n            setCookie('codeGeneratorUser', encodeURIComponent(JSON.stringify(userData)), 15)\n\n            isLoggedIn.value = true\n            loginDialogVisible.value = false\n\n            // 重置表单\n            loginForm.username = ''\n            loginForm.password = ''\n\n            ElMessage.success('登录成功！')\n          } else {\n            ElMessage.error(res.msg || '登录失败')\n          }\n        } catch (error) {\n          loginLoading.value = false\n          console.error('登录失败:', error)\n          ElMessage.error('登录失败，请检查网络连接')\n        }\n      }\n\n      // 处理用户下拉菜单命令\n      const handleUserCommand = (command) => {\n        if (command === 'logout') {\n          handleLogout()\n        }\n      }\n\n      // 退出登录\n      const handleLogout = () => {\n        // 清除Cookie\n        deleteCookie('codeGeneratorUser')\n\n        // 清除sessionStorage\n        sessionStorage.removeItem(\"user\")\n        sessionStorage.removeItem(\"userLname\")\n        sessionStorage.removeItem(\"role\")\n\n        // 重置状态\n        isLoggedIn.value = false\n        userInfo.username = ''\n        userInfo.loginTime = ''\n        loginDialogVisible.value = true\n\n        ElMessage.success('已退出登录')\n      }\n\n      // 项目类型选择\n      const selectProject = (projectType) => {\n        selectedProject.value = projectType\n        console.log('选择项目类型:', projectType)\n      }\n\n      // 获取项目名称\n      const getProjectName = (projectType) => {\n        const projectNames = {\n          'springboot-thymeleaf': 'SpringBoot + Thymeleaf',\n          'springboot-miniprogram': 'SpringBoot + 小程序',\n          'springboot-vue': 'SpringBoot + Vue',\n          'ssm-vue': 'SSM + Vue'\n        }\n        return projectNames[projectType] || projectType\n      }\n\n      // 打开模板选择弹窗\n      const openTemplateModal = () => {\n        console.log('打开后台模板选择弹窗')\n        showBackendTemplateSelector()\n      }\n\n      const openFrontTemplateModal = () => {\n        console.log('打开前台模板选择弹窗')\n        showFrontendTemplateSelector()\n      }\n\n      // 处理数据库模式变化\n      const handleDatabaseModeChange = (mode) => {\n        if (mode === 'existing') {\n          loadProjects()\n        } else {\n          // 清空已有数据库选项\n          availableDatabases.value = [{ value: '', text: '请选择数据库' }]\n          projectForm.selectedDatabase = ''\n        }\n      }\n\n      // 加载项目列表\n      const loadProjects = async () => {\n        try {\n          projectsLoading.value = true\n          const url = base + \"/projects/list?currentPage=1&pageSize=1000\"\n          const params = {\n\n          }\n\n          const res = await request.post(url, { params })\n\n          if (res.code === 200) {\n            const projects = res.resdata || []\n            availableDatabases.value = [\n              { value: '', text: '请选择数据库' },\n              ...projects.map(project => ({\n                value: project.pid,\n                text: `${project.pno}--${project.daname} (${project.pname})`\n              }))\n            ]\n          } else {\n            ElMessage.error('加载项目列表失败')\n          }\n        } catch (error) {\n          console.error('加载项目列表失败:', error)\n          ElMessage.error('加载项目列表失败，请检查网络连接')\n        } finally {\n          projectsLoading.value = false\n        }\n      }\n\n      // 处理项目选择\n      const handleProjectSelect = async (projectId) => {\n        if (!projectId) return\n\n        try {\n          const url = base + \"/projects/get?id=\" + projectId;\n\n\n          const res = await request.post(url, {})\n\n          if (res.code === 200) {\n            const project = res.resdata\n            // 初始化表单数据\n            projectForm.projectCode = project.pno || ''\n            projectForm.databaseName = project.daname || ''\n            projectForm.name = project.pname || ''\n            projectForm.databaseType = project.dtype || 'mysql'\n            projectForm.backendTemplate = project.by1 || ''\n            projectForm.frontendTemplate = project.by2 || ''\n            projectForm.layer = project.by4 || '否'\n            projectForm.charts = project.by5 || '否'\n            projectForm.schoolName = project.by6 || ''\n\n            // 解析Session信息\n            if (project.by3) {\n              const sessionInfo = project.by3.split(',')\n              projectForm.adminId = sessionInfo[0] || ''\n              projectForm.adminName = sessionInfo[1] || ''\n              projectForm.adminRole = sessionInfo[2] || ''\n              projectForm.adminLoginName = sessionInfo[3] || ''\n            }\n\n            ElMessage.success('项目信息加载成功')\n          } else {\n            ElMessage.error('加载项目详情失败')\n          }\n        } catch (error) {\n          console.error('加载项目详情失败:', error)\n          ElMessage.error('加载项目详情失败，请检查网络连接')\n        }\n      }\n\n      // 保存或更新项目到数据库\n      const saveOrUpdateProject = async () => {\n        try {\n          // 构建Session信息\n          const sessionInfo = [\n            projectForm.adminId,\n            projectForm.adminName,\n            projectForm.adminRole,\n            projectForm.adminLoginName\n          ].join(',')\n\n          const projectData = {\n            ptype: selectedProject.value,\n            dtype: projectForm.databaseType,\n            pflag: projectForm.databaseMode === 'new' ? '1' : '2',\n            pno: projectForm.projectCode,\n            daname: projectForm.databaseName,\n            pname: projectForm.name,\n            by1: projectForm.backendTemplate,\n            by2: projectForm.frontendTemplate,\n            by3: sessionInfo,\n            by4: projectForm.layer,\n            by5: projectForm.charts,\n            by6: projectForm.schoolName,\n            by7: projectForm.copyProject,\n            lname: userInfo.username\n          }\n\n          // 判断是新建还是更新\n          const isUpdate = currentProjectInfo.value && currentProjectInfo.value.pid\n          let url, res\n\n          if (isUpdate) {\n            // 更新项目\n            projectData.pid = currentProjectInfo.value.pid\n            url = base + \"/projects/update\"\n            res = await request.post(url, projectData)\n          } else {\n            // 新建项目\n            url = base + \"/projects/add\"\n            res = await request.post(url, projectData)\n          }\n\n          if (res.code === 200) {\n            const message = isUpdate ? '项目更新成功' : '项目保存成功'\n            ElMessage.success(message)\n\n            // 如果是新建项目，保存返回的项目ID\n            if (!isUpdate && res.resdata && res.resdata.pid) {\n              currentProjectInfo.value = {\n                ...currentProjectInfo.value,\n                pid: res.resdata.pid\n              }\n            }\n\n            return { success: true, projectId: res.resdata?.pid || currentProjectInfo.value?.pid }\n          } else {\n            ElMessage.error(res.msg || '项目保存失败')\n            return { success: false }\n          }\n        } catch (error) {\n          console.error('保存项目失败:', error)\n          ElMessage.error('保存项目失败，请检查网络连接')\n          return { success: false }\n        }\n      }\n\n      // 加载项目表单列表\n      const loadProjectTables = async (projectId) => {\n        try {\n          projectTablesLoading.value = true\n          const url = base + \"/tables/list?currentPage=1&pageSize=1000\"\n          const params = {\n            pid: projectId\n          }\n\n          const res = await request.post(url, params)\n\n          if (res.code === 200) {\n            projectTables.value = res.resdata || []\n\n            // 为每个表加载字段数据\n            for (let table of projectTables.value) {\n              try {\n                const fieldsUrl = base + \"/mores/list?currentPage=1&pageSize=100\"\n                const fieldsRes = await request.post(fieldsUrl, { tid: table.tid })\n\n                if (fieldsRes.code === 200 && fieldsRes.resdata) {\n                  // 将字段数据转换为前端格式\n                  table.fields = fieldsRes.resdata.map(field => ({\n                    id: field.mid,\n                    tid: field.tid,\n                    chineseName: field.mozname,\n                    englishName: field.moname,\n                    type: field.motype,\n                    controlType: field.moflag,\n                    required: field.moyz === '1',\n                    searchable: field.mobt === '1',\n                    visible: field.by1 === '1',\n                    existsCheck: field.by2 === '1',\n                    relatedTable: field.by3 || '', // 关联表\n                    customOptions: field.by4 || '' // 自定义选项\n                  }))\n\n                  // 设置表的生成数据条数\n                  table.generateDataCount = parseInt(table.by1 || '0')\n                } else {\n                  table.fields = []\n                }\n              } catch (error) {\n                console.warn('加载表字段失败:', table.tname, error)\n                table.fields = []\n              }\n            }\n\n            console.log('加载项目表单成功:', projectTables.value)\n          } else {\n            ElMessage.error('加载项目表单失败')\n          }\n        } catch (error) {\n          console.error('加载项目表单失败:', error)\n          ElMessage.error('加载项目表单失败，请检查网络连接')\n        } finally {\n          projectTablesLoading.value = false\n        }\n      }\n\n      // 表设计相关\n      const showTableDesignModal = ref(false)\n      const currentTableTab = ref('table-settings')\n      const currentTableDesign = ref({\n        id: null,\n        chineseName: '',\n        englishName: '',\n        menuOrder: 1,\n        generateData: '0',\n        generateDataCount: 0,\n        functions: {\n          backendAdd: true, backendEdit: true, backendDelete: true,\n          backendDetail: true, backendList: false, batchImport: false,\n          batchExport: false, backendLogin: false, backendRegister: false,\n          backendProfile: false, backendPassword: false,\n          frontendAdd: false, frontendEdit: false, frontendDelete: false,\n          frontendDetail: false, frontendList: false, frontendLogin: false,\n          miniAdd: false, miniEdit: false, miniDelete: false,\n          miniDetail: false, miniList: false\n        },\n        fields: [\n          {\n            id: 1,\n            chineseName: '主键ID',\n            englishName: 'id',\n            type: 'int',\n            controlType: '文本框',\n            required: true,\n            searchable: false,\n            visible: true,\n            existsCheck: false,\n            relatedTable: '',\n            customOptions: ''\n          }\n        ]\n      })\n\n      // 功能选择的响应式数组\n      const backendFunctions = ref([])\n      const frontendFunctions = ref([])\n      const miniFunctions = ref([])\n\n      // 重置字段\n      const resetFields = () => {\n        currentTableDesign.value.fields = [\n          {\n            id: 1,\n            chineseName: '主键ID',\n            englishName: 'id',\n            type: 'int',\n            controlType: '文本框',\n            required: true,\n            searchable: false,\n            visible: true,\n            existsCheck: false\n          }\n        ]\n        ElMessage.success('字段已重置')\n      }\n\n      // AI生成器相关状态\n      const showAiGeneratorDialog = ref(false)\n      const aiGeneratorInput = ref('')\n      const aiGenerationInProgress = ref(false)\n\n      // 模板选择相关状态\n      const showBackendTemplateDialog = ref(false)\n      const showFrontendTemplateDialog = ref(false)\n      const showTemplateDetailDialog = ref(false)\n      const backendTemplates = ref([])\n      const frontendTemplates = ref([])\n      const templatesLoading = ref(false)\n      const currentTemplateDetail = ref(null)\n\n      // 项目生成相关状态\n      const generationInProgress = ref(false)\n      const generationResult = ref(null)\n\n      // SQL脚本生成相关状态\n      const sqlGenerationInProgress = ref(false)\n\n      // 数据脚本生成相关状态\n      const dataGenerationInProgress = ref(false)\n      const dataContent = ref('')\n\n      // 字段设置相关状态\n      const showFieldSettingsDialog = ref(false)\n      const currentFieldSettings = ref(null)\n      const currentFieldIndex = ref(-1)\n      const showInSearchList = ref(false)\n\n      // 关联表选择相关状态\n      const showRelatedTableDialog = ref(false)\n      const availableRelatedTables = ref([])\n\n      // 显示AI生成器弹窗\n      const showAiGeneratorModal = () => {\n        aiGeneratorInput.value = ''\n        showAiGeneratorDialog.value = true\n        nextTick(() => {\n          // 聚焦到输入框\n          const inputElement = document.querySelector('.ai-generator-input')\n          if (inputElement) {\n            inputElement.focus()\n          }\n        })\n      }\n\n      // 隐藏AI生成器弹窗\n      const hideAiGeneratorModal = () => {\n        showAiGeneratorDialog.value = false\n        aiGeneratorInput.value = ''\n      }\n\n      // 确认AI生成\n      const confirmAiGeneration = async () => {\n        const description = aiGeneratorInput.value.trim()\n        if (!description) {\n          ElMessage.warning('请输入描述内容')\n          return\n        }\n\n        // 隐藏弹窗\n        hideAiGeneratorModal()\n\n        // 调用AI生成\n        await doubaoGenerate(description)\n      }\n\n      // 调用豆包AI生成表单\n      const doubaoGenerate = async (str) => {\n        if (aiGenerationInProgress.value) {\n          ElMessage.warning('AI正在生成中，请稍候...')\n          return\n        }\n\n        // 构建prompt\n        const input_text = \"用户:users\\n\" +\n          \"aid|lname|password|role\\n\" +\n          \"用户id|用户名|密码|身份\\n\" +\n          \"int|varchar(50)|varchar(50)|int\\n\" +\n          \"\\n\" +\n          \"学习上面的格式。格式说明如下。\\n\" +\n          \"第1行表中文名称:表英文名称。\\n\" +\n          \"第2行字段列表，字段简写\\n\" +\n          \"第3行字段对应中文\\n\" +\n          \"第4行字段类型。如果是字符型加上长度。\\n\" +\n          \"\\n\" +\n          \"按上面的格式生成下面的内容。不要注释，只返回格式的内容\\n\" + str\n\n        console.log('发送给豆包AI的内容:', input_text)\n\n        const settings = {\n          url: \"https://ark.cn-beijing.volces.com/api/v3/chat/completions\",\n          method: \"POST\",\n          timeout: 30000,\n          headers: {\n            \"Authorization\": \"Bearer 8d71b27a-b4c9-484e-896b-247f7dda5412\",\n            \"Content-Type\": \"application/json\"\n          },\n          data: JSON.stringify({\n            \"model\": \"doubao-1.5-pro-32k-250115\",\n            \"messages\": [\n              {\n                \"role\": \"system\",\n                \"content\": \"你是一个数据库设计专家，专门帮助用户设计数据表结构。请严格按照指定的格式返回结果，不要添加任何额外的说明或注释，表名和字段中不要用下划线，不要大写字母。不要用关键字和保留字\"\n              },\n              {\n                \"role\": \"user\",\n                \"content\": input_text\n              }\n            ]\n          })\n        }\n\n        // 显示生成中状态\n        aiGenerationInProgress.value = true\n        ElMessage.info('正在调用豆包AI生成表结构，请稍候...')\n\n        try {\n          const response = await fetch(settings.url, {\n            method: settings.method,\n            headers: settings.headers,\n            body: settings.data\n          })\n\n          if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`)\n          }\n\n          const result = await response.json()\n          aiGenerationInProgress.value = false\n\n          // 从豆包AI响应中提取内容\n          const generatedContent = result.choices[0].message.content\n          console.log('豆包AI生成的原始内容:', generatedContent)\n\n          // 清理返回内容\n          const cleanedContent = generatedContent\n            .replace(/```[\\s\\S]*?\\n/, '') // 移除开头的markdown代码块标记\n            .replace(/\\n```[\\s\\S]*?$/, '') // 移除结尾的markdown代码块标记\n            .replace(/^\\s+|\\s+$/g, '') // 移除首尾空白\n            .replace(/\\r\\n/g, '\\n') // 统一换行符\n            .replace(/\\r/g, '\\n') // 处理Mac格式换行符\n\n          console.log('清理后的内容:', cleanedContent)\n\n          // 检查内容是否为空\n          if (!cleanedContent || cleanedContent.trim() === '') {\n            throw new Error('豆包AI返回的内容为空')\n          }\n\n          createFormFromAI(cleanedContent)\n          ElMessage.success('AI生成成功！')\n\n        } catch (error) {\n          aiGenerationInProgress.value = false\n          console.error('处理豆包AI响应时出错:', error)\n          ElMessage.error('AI生成失败：' + error.message)\n        }\n      }\n\n      // 解析AI生成的内容并创建表单\n      const createFormFromAI = (content) => {\n        try {\n          console.log('开始解析AI生成的内容:', content)\n\n          // 按行分割内容，移除空行\n          const allLines = content.split('\\n')\n          const lines = allLines.map(line => line.trim()).filter(line => line !== '')\n          console.log('过滤后的有效行:', lines)\n\n          if (lines.length < 4) {\n            throw new Error(`豆包AI返回的格式不完整，需要4行内容，实际只有${lines.length}行`)\n          }\n\n          // 解析第1行：表名\n          const tableNameLine = lines[0]\n          const tableNameMatch = tableNameLine.match(/^(.+):(.+)$/)\n          if (!tableNameMatch) {\n            throw new Error('表名格式不正确，应为：中文名:英文名，实际为：' + tableNameLine)\n          }\n\n          const chineseName = tableNameMatch[1].trim()\n          const englishName = tableNameMatch[2].trim()\n\n          // 解析第2行：英文字段名\n          const englishFields = lines[1].split('|').map(field => field.trim()).filter(field => field !== '')\n\n          // 解析第3行：中文字段名\n          const chineseFields = lines[2].split('|').map(field => field.trim()).filter(field => field !== '')\n\n          // 解析第4行：字段类型\n          const fieldTypes = lines[3].split('|').map(type => type.trim()).filter(type => type !== '')\n\n          // 验证字段数量一致性\n          if (englishFields.length !== chineseFields.length || englishFields.length !== fieldTypes.length) {\n            throw new Error(`字段数量不匹配：英文名${englishFields.length}个，中文名${chineseFields.length}个，类型${fieldTypes.length}个`)\n          }\n\n          // 构建字段数据\n          const fields = []\n          for (let i = 0; i < englishFields.length; i++) {\n            const field = {\n              id: Date.now() + i,\n              chineseName: chineseFields[i],\n              englishName: englishFields[i],\n              type: fieldTypes[i],\n              controlType: inferControlType(fieldTypes[i]),\n              required: true, // AI生成的字段默认必填项选中\n              searchable: i > 0 && i < 3, // 前几个字段设为可搜索\n              visible: true,\n              existsCheck: false\n            }\n            fields.push(field)\n          }\n\n          // 完整替换表结构\n          currentTableDesign.value.chineseName = chineseName\n          currentTableDesign.value.englishName = englishName\n          currentTableDesign.value.fields = fields\n\n          // 确保functions对象存在\n          if (!currentTableDesign.value.functions) {\n            currentTableDesign.value.functions = {}\n          }\n\n          // 默认选中后台功能\n          currentTableDesign.value.functions.backendAdd = true\n          currentTableDesign.value.functions.backendEdit = true\n          currentTableDesign.value.functions.backendDelete = true\n          currentTableDesign.value.functions.backendDetail = true\n          currentTableDesign.value.functions.backendList = false\n\n          console.log('表单创建成功:', {\n            chineseName: chineseName,\n            englishName: englishName,\n            fieldsCount: fields.length\n          })\n\n        } catch (error) {\n          console.error('解析AI内容失败:', error)\n          ElMessage.error('解析AI生成的内容失败：' + error.message)\n        }\n      }\n\n      // 根据字段类型推断控件类型\n      const inferControlType = (fieldType) => {\n        const type = fieldType.toLowerCase()\n\n        if (type.includes('int') || type.includes('bigint')) {\n          return '文本框'\n        } else if (type.includes('decimal') || type.includes('float') || type.includes('double')) {\n          return '文本框'\n        } else if (type.includes('text') || type.includes('longtext')) {\n          return '多行文本'\n        } else if (type.includes('datetime') || type.includes('timestamp')) {\n          return '日期时间'\n        } else if (type.includes('date')) {\n          return '日期选择'\n        } else if (type.includes('varchar') && type.includes('200')) {\n          return '多行文本'\n        } else if (type.includes('varchar')) {\n          return '文本框'\n        } else {\n          return '文本框'\n        }\n      }\n\n      // 模板选择相关函数\n\n      // 显示后台模板选择弹窗\n      const showBackendTemplateSelector = async () => {\n        showBackendTemplateDialog.value = true\n        await loadBackendTemplates()\n      }\n\n      // 显示前台模板选择弹窗\n      const showFrontendTemplateSelector = async () => {\n        showFrontendTemplateDialog.value = true\n        await loadFrontendTemplates()\n      }\n\n      // 加载后台模板\n      const loadBackendTemplates = async () => {\n        try {\n          templatesLoading.value = true\n          const response = await fetch(base+'/small/backend-templates')\n          const result = await response.json()\n          if (result.code === 200) {\n            backendTemplates.value = result.resdata || []\n          } else {\n            ElMessage.error('加载后台模板失败')\n          }\n        } catch (error) {\n          console.error('加载后台模板失败:', error)\n          ElMessage.error('加载后台模板失败')\n        } finally {\n          templatesLoading.value = false\n        }\n      }\n\n      // 加载前台模板\n      const loadFrontendTemplates = async () => {\n        try {\n          templatesLoading.value = true\n          const response = await fetch(base+'/small/frontend-templates')\n          const result = await response.json()\n          if (result.code === 200) {\n            frontendTemplates.value = result.resdata || []\n          } else {\n            ElMessage.error('加载前台模板失败')\n          }\n        } catch (error) {\n          console.error('加载前台模板失败:', error)\n          ElMessage.error('加载前台模板失败')\n        } finally {\n          templatesLoading.value = false\n        }\n      }\n\n      // 选择后台模板\n      const selectBackendTemplate = (template) => {\n        // 设置到项目表单的后台模板字段 - 保存模板ID\n        projectForm.backendTemplate = template.sid\n        showBackendTemplateDialog.value = false\n        ElMessage.success(`已选择后台模板: ${template.sname} (ID: ${template.sid})`)\n      }\n\n      // 选择前台模板\n      const selectFrontendTemplate = (template) => {\n        // 设置到项目表单的前台模板字段 - 保存模板ID\n        projectForm.frontendTemplate = template.sid\n        showFrontendTemplateDialog.value = false\n        ElMessage.success(`已选择前台模板: ${template.sname} (ID: ${template.sid})`)\n      }\n\n      // 查看模板详情\n      const viewTemplateDetail = (template) => {\n        currentTemplateDetail.value = template\n        showTemplateDetailDialog.value = true\n      }\n\n      // 计算属性：是否可以生成项目\n      const canGenerate = computed(() => {\n        return projectForm.name &&\n               projectForm.databaseName &&\n               projectForm.projectCode &&\n               projectTables.value.length > 0 &&\n               !generationInProgress.value\n      })\n\n      // 生成项目\n      const generateProject = async () => {\n        if (!canGenerate.value) {\n          ElMessage.warning('请完善项目配置信息')\n          return\n        }\n\n        try {\n          generationInProgress.value = true\n          generationResult.value = null\n\n          // 构建项目数据\n          const projectData = {\n            projectNumber: projectForm.projectCode,\n            databaseName: projectForm.databaseName,\n            projectName: projectForm.name,\n            packageName: projectForm.packageName || 'com',\n            databaseType: projectForm.databaseType || 'mysql',\n            backendTemplate: projectForm.backendTemplate,\n            frontendTemplate: projectForm.frontendTemplate,\n            tables: projectTables.value.map(table => ({\n              id: table.tid,\n              chineseName: table.tword,\n              englishName: table.tname,\n              functions: table.tgn ? JSON.parse(table.tgn) : {},\n              fields: table.fields || []\n            }))\n          }\n\n          console.log('开始生成项目:', projectData)\n\n          // 调用Java后端API生成项目\n          const response = await fetch(base + '/projects/generate', {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(projectData)\n          })\n\n          const result = await response.json()\n\n          if (result.code === 200) {\n            // 适配后端返回的数据结构\n            const filesData = result.resdata.files && result.resdata.files.data ? result.resdata.files.data : {}\n            const compressionData = result.resdata.compression || null\n\n            generationResult.value = {\n              status: 'success',\n              message: '项目生成成功！',\n              files: filesData,\n              compression: compressionData\n            }\n\n            // 生成项目成功后，自动生成SQL脚本和数据脚本\n            await generateSqlScript()\n            await generateDataScript()\n\n            ElMessage.success('项目生成成功！')\n          } else {\n            throw new Error(result.msg || '项目生成失败')\n          }\n\n        } catch (error) {\n          console.error('项目生成失败:', error)\n          generationResult.value = {\n            status: 'error',\n            message: '项目生成失败：' + error.message,\n            files: null,\n            compression: null\n          }\n          ElMessage.error('项目生成失败：' + error.message)\n        } finally {\n          generationInProgress.value = false\n        }\n      }\n\n      // 生成SQL脚本\n      const generateSqlScript = async () => {\n        if (!currentProjectInfo.value || !projectTables.value || projectTables.value.length === 0) {\n          ElMessage.warning('请先配置项目和数据表')\n          return\n        }\n\n        try {\n          sqlGenerationInProgress.value = true\n\n          // 根据数据库类型生成对应的SQL脚本\n          const databaseType = projectForm.databaseType || 'mysql'\n          let script = ''\n\n          if (databaseType === 'mysql') {\n            script = generateMySqlScript()\n          } else if (databaseType === 'sqlserver') {\n            script = generateSqlServerScript()\n          }\n\n          sqlContent.value = script\n          ElMessage.success('SQL脚本生成成功！')\n\n        } catch (error) {\n          console.error('生成SQL脚本失败:', error)\n          ElMessage.error('生成SQL脚本失败：' + error.message)\n        } finally {\n          sqlGenerationInProgress.value = false\n        }\n      }\n\n      // 生成MySQL脚本\n      const generateMySqlScript = () => {\n        let script = `-- ${projectForm.name || '项目'} 数据库脚本 (MySQL)\\n`\n        script += `-- 数据库名称: ${projectForm.databaseName}\\n`\n        script += `-- 生成时间: ${new Date().toLocaleString()}\\n\\n`\n\n        // 创建数据库\n        script += `CREATE DATABASE IF NOT EXISTS \\`${projectForm.databaseName}\\` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\\n`\n        script += `USE \\`${projectForm.databaseName}\\`;\\n\\n`\n\n        // 为每个表生成建表语句\n        projectTables.value.forEach(table => {\n          script += generateMySqlTableScript(table)\n          script += '\\n'\n        })\n\n        return script\n      }\n\n      // 生成SQL Server脚本\n      const generateSqlServerScript = () => {\n        let script = `-- ${projectForm.name || '项目'} 数据库脚本 (SQL Server)\\n`\n        script += `-- 数据库名称: ${projectForm.databaseName}\\n`\n        script += `-- 生成时间: ${new Date().toLocaleString()}\\n\\n`\n\n        // 创建数据库\n        script += `IF NOT EXISTS (SELECT * FROM sys.databases WHERE name = '${projectForm.databaseName}')\\n`\n        script += `CREATE DATABASE [${projectForm.databaseName}];\\n`\n        script += `GO\\n\\n`\n        script += `USE [${projectForm.databaseName}];\\n`\n        script += `GO\\n\\n`\n\n        // 为每个表生成建表语句\n        projectTables.value.forEach(table => {\n          script += generateSqlServerTableScript(table)\n          script += '\\n'\n        })\n\n        return script\n      }\n\n      // 生成MySQL表脚本\n      const generateMySqlTableScript = (table) => {\n        let script = `-- 表: ${table.tword || table.tname}\\n`\n        script += `DROP TABLE IF EXISTS \\`${table.tname}\\`;\\n`\n        script += `CREATE TABLE \\`${table.tname}\\` (\\n`\n\n        const fields = table.fields || []\n        const fieldScripts = fields.map((field, index) => {\n          let fieldScript = `  \\`${field.englishName}\\` ${convertToMySqlType(field.type)}`\n\n          // 主键处理 - 修复第一个字段的AUTO_INCREMENT问题\n          if (field.englishName.toLowerCase() === 'id' || index === 0) {\n            // 只有int类型的字段才能使用AUTO_INCREMENT\n            if (field.type && field.type.toLowerCase() === 'int') {\n              fieldScript += ' AUTO_INCREMENT NOT NULL'\n            } else {\n              fieldScript += ' NOT NULL'\n            }\n          } else if (field.required) {\n            fieldScript += ' NOT NULL'\n          }\n\n          // 注释\n          if (field.chineseName) {\n            fieldScript += ` COMMENT '${field.chineseName}'`\n          }\n\n          return fieldScript\n        })\n\n        script += fieldScripts.join(',\\n')\n\n        // 主键约束\n        if (fields.length > 0) {\n          const primaryKey = fields[0].englishName\n          script += `,\\n  PRIMARY KEY (\\`${primaryKey}\\`)`\n        }\n\n        script += `\\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='${table.tword || table.tname}';\\n\\n`\n\n        return script\n      }\n\n      // 生成SQL Server表脚本\n      const generateSqlServerTableScript = (table) => {\n        let script = `-- 表: ${table.tword || table.tname}\\n`\n        script += `IF OBJECT_ID('[${table.tname}]', 'U') IS NOT NULL DROP TABLE [${table.tname}];\\n`\n        script += `CREATE TABLE [${table.tname}] (\\n`\n\n        const fields = table.fields || []\n        const fieldScripts = fields.map((field, index) => {\n          let fieldScript = `  [${field.englishName}] ${convertToSqlServerType(field.type)}`\n\n          // 主键处理\n          if (field.englishName.toLowerCase() === 'id' || index === 0) {\n            fieldScript += ' IDENTITY(1,1)'\n          }\n\n          // 非空约束\n          if (field.required) {\n            fieldScript += ' NOT NULL'\n          }\n\n          return fieldScript\n        })\n\n        script += fieldScripts.join(',\\n')\n\n        // 主键约束\n        if (fields.length > 0) {\n          const primaryKey = fields[0].englishName\n          script += `,\\n  CONSTRAINT [PK_${table.tname}] PRIMARY KEY ([${primaryKey}])`\n        }\n\n        script += `\\n);\\n`\n\n        // 添加表注释\n        if (table.tword) {\n          script += `EXEC sp_addextendedproperty 'MS_Description', '${table.tword}', 'SCHEMA', 'dbo', 'TABLE', '${table.tname}';\\n`\n        }\n\n        // 添加字段注释\n        fields.forEach(field => {\n          if (field.chineseName) {\n            script += `EXEC sp_addextendedproperty 'MS_Description', '${field.chineseName}', 'SCHEMA', 'dbo', 'TABLE', '${table.tname}', 'COLUMN', '${field.englishName}';\\n`\n          }\n        })\n\n        script += `GO\\n\\n`\n\n        return script\n      }\n\n      // 转换为MySQL数据类型\n      const convertToMySqlType = (type) => {\n        if (!type) return 'VARCHAR(100)'\n\n        const lowerType = type.toLowerCase()\n        if (lowerType === 'int') return 'INT'\n        if (lowerType.includes('varchar')) return type.toUpperCase()\n        if (lowerType === 'text') return 'TEXT'\n        if (lowerType === 'datetime') return 'DATETIME'\n        if (lowerType.includes('decimal')) return type.toUpperCase()\n\n        return type.toUpperCase()\n      }\n\n      // 转换为SQL Server数据类型\n      const convertToSqlServerType = (type) => {\n        if (!type) return 'NVARCHAR(100)'\n\n        const lowerType = type.toLowerCase()\n        if (lowerType === 'int') return 'INT'\n        if (lowerType.includes('varchar')) {\n          // 将varchar转换为nvarchar\n          return type.replace(/varchar/i, 'NVARCHAR')\n        }\n        if (lowerType === 'text') return 'NTEXT'\n        if (lowerType === 'datetime') return 'DATETIME'\n        if (lowerType.includes('decimal')) return type.toUpperCase()\n\n        return type.toUpperCase()\n      }\n\n      // 导出SQL脚本\n      const exportSqlScript = () => {\n        if (!sqlContent.value || sqlContent.value.trim() === '') {\n          ElMessage.warning('请先生成SQL脚本')\n          return\n        }\n\n        try {\n          const blob = new Blob([sqlContent.value], { type: 'text/plain;charset=utf-8' })\n          const url = window.URL.createObjectURL(blob)\n          const link = document.createElement('a')\n          link.href = url\n\n          const databaseType = projectForm.databaseType || 'mysql'\n          const fileName = `${projectForm.databaseName || 'database'}_${databaseType}.sql`\n          link.download = fileName\n\n          document.body.appendChild(link)\n          link.click()\n          document.body.removeChild(link)\n          window.URL.revokeObjectURL(url)\n\n          ElMessage.success('SQL脚本导出成功！')\n        } catch (error) {\n          console.error('导出SQL脚本失败:', error)\n          ElMessage.error('导出SQL脚本失败：' + error.message)\n        }\n      }\n\n      // 复制SQL脚本到剪切板\n      const copySqlScript = async () => {\n        if (!sqlContent.value || sqlContent.value.trim() === '') {\n          ElMessage.warning('请先生成SQL脚本')\n          return\n        }\n\n        try {\n          await navigator.clipboard.writeText(sqlContent.value)\n          ElMessage.success('SQL脚本已复制到剪切板！')\n        } catch (error) {\n          console.error('复制到剪切板失败:', error)\n          // 降级方案：使用传统的复制方法\n          try {\n            const textArea = document.createElement('textarea')\n            textArea.value = sqlContent.value\n            document.body.appendChild(textArea)\n            textArea.select()\n            document.execCommand('copy')\n            document.body.removeChild(textArea)\n            ElMessage.success('SQL脚本已复制到剪切板！')\n          } catch (fallbackError) {\n            console.error('降级复制方法也失败:', fallbackError)\n            ElMessage.error('复制到剪切板失败，请手动复制')\n          }\n        }\n      }\n\n      // 生成数据脚本\n      const generateDataScript = async () => {\n        if (!currentProjectInfo.value || !projectTables.value || projectTables.value.length === 0) {\n          ElMessage.warning('请先配置项目和数据表')\n          return\n        }\n\n        try {\n          dataGenerationInProgress.value = true\n\n          // 筛选出需要生成数据的表（生成数据条数大于0）\n          const tablesWithData = projectTables.value.filter(table => {\n            const dataCount = parseInt(table.by1 || '0')\n            return dataCount > 0\n          })\n\n          if (tablesWithData.length === 0) {\n            ElMessage.warning('没有设置生成数据条数的表，请先在表设计中设置生成数据条数')\n            return\n          }\n\n          let script = ''\n\n          // 为每个需要生成数据的表生成建表语句和插入数据\n          for (const table of tablesWithData) {\n            script += generateTableWithDataScript(table)\n            script += '\\n'\n          }\n\n          // 添加生成要求说明\n          script += `项目名称是：${projectForm.name || '智慧社区网格化管理系统'}\\n`\n          script += `按要求生成的条数，生成上面所有的数据，数据内容要多一些，数据模拟真实的数据\\n`\n          script += `如果有密码，密码为123456。\\n`\n          script += `时间字段为当前时间\\n`\n          script += `生成的数据为中文，只生成insert into数据，不要注释说明\\n`\n\n          dataContent.value = script\n          ElMessage.success('数据脚本生成成功！')\n\n        } catch (error) {\n          console.error('生成数据脚本失败:', error)\n          ElMessage.error('生成数据脚本失败：' + error.message)\n        } finally {\n          dataGenerationInProgress.value = false\n        }\n      }\n\n      // 生成单个表的建表语句和数据\n      const generateTableWithDataScript = (table) => {\n        const fields = table.fields || []\n        const dataCount = parseInt(table.by1 || '0')\n\n        let script = `create table if NOT EXISTS ${table.tname} \\n(\\n`\n\n        // 生成字段定义\n        const fieldScripts = fields.map((field, index) => {\n          let fieldScript = `${field.englishName}   ${convertToMySqlType(field.type)}`\n\n          // 主键处理 - 修复第一个字段的AUTO_INCREMENT问题\n          if (field.englishName.toLowerCase() === 'id' || index === 0) {\n            // 只有int类型的字段才能使用AUTO_INCREMENT\n            if (field.type && field.type.toLowerCase() === 'int') {\n              fieldScript += ' auto_increment  primary key'\n            } else {\n              fieldScript += ' not null    primary key'\n            }\n          } else if (field.englishName.toLowerCase().includes('account') ||\n                     field.englishName.toLowerCase().includes('username')) {\n            fieldScript += ' not null    primary key'\n          } else if (field.required) {\n            fieldScript += ' not null   '\n          } else {\n            fieldScript += '  null   '\n          }\n\n          // 注释\n          if (field.chineseName) {\n            fieldScript += ` comment '${field.chineseName}'`\n          }\n\n          return fieldScript\n        })\n\n        script += fieldScripts.join(' ,\\n') + ' \\n'\n        script += `) ENGINE=MyISAM AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;\\n\\n`\n\n        // 添加生成数据条数说明\n        if (dataCount > 0) {\n          script += `生成${dataCount}条insert into数据\\n\\n`\n        }\n\n        return script\n      }\n\n      // 复制数据脚本到剪切板\n      const copyDataScript = async () => {\n        if (!dataContent.value || dataContent.value.trim() === '') {\n          ElMessage.warning('请先生成数据脚本')\n          return\n        }\n\n        try {\n          await navigator.clipboard.writeText(dataContent.value)\n          ElMessage.success('数据脚本已复制到剪切板！')\n        } catch (error) {\n          console.error('复制到剪切板失败:', error)\n          // 降级方案：使用传统的复制方法\n          try {\n            const textArea = document.createElement('textarea')\n            textArea.value = dataContent.value\n            document.body.appendChild(textArea)\n            textArea.select()\n            document.execCommand('copy')\n            document.body.removeChild(textArea)\n            ElMessage.success('数据脚本已复制到剪切板！')\n          } catch (fallbackError) {\n            console.error('降级复制方法也失败:', fallbackError)\n            ElMessage.error('复制到剪切板失败，请手动复制')\n          }\n        }\n      }\n\n      // 下载项目文件\n      const downloadProject = (filePath) => {\n        if (filePath) {\n          // 从filePath中提取文件名\n          const fileName = filePath.split('/').pop()\n          // 使用新的下载接口\n          const downloadUrl = `${base}/projects/download?fileName=${encodeURIComponent(fileName)}`\n          window.open(downloadUrl, '_blank')\n        } else {\n          ElMessage.error('下载链接无效')\n        }\n      }\n\n      // 获取模板图片URL\n      const getTemplateImageUrl = (memo4) => {\n        if (!memo4) return ''\n\n        // 从HTML中提取图片URL\n        const imgMatch = memo4.match(/<img[^>]+src=\"([^\"]+)\"/)\n        if (imgMatch && imgMatch[1]) {\n          return imgMatch[1]\n        }\n\n        return ''\n      }\n\n      // 处理图片加载错误\n      const handleImageError = (event) => {\n        event.target.style.display = 'none'\n        const parent = event.target.parentElement\n        if (parent) {\n          parent.innerHTML = '<div class=\"no-image\"><span>图片加载失败</span></div>'\n        }\n      }\n\n      // 获取表格字段数量\n      const getTableFieldCount = (table) => {\n        // 如果有字段数据，返回字段数量\n        if (table.fields && Array.isArray(table.fields)) {\n          return table.fields.length\n        }\n        // 否则返回0\n        return 0\n      }\n\n      // 获取表格功能列表\n      const getTableFunctions = (table) => {\n        if (!table.tgn) return []\n\n        try {\n          const functions = JSON.parse(table.tgn)\n          const activeFunctions = []\n\n          // 检查各种功能是否启用\n          if (functions.backendAdd) activeFunctions.push('后台添加')\n          if (functions.backendEdit) activeFunctions.push('后台修改')\n          if (functions.backendDelete) activeFunctions.push('后台删除')\n          if (functions.backendDetail) activeFunctions.push('后台详情')\n          if (functions.backendList) activeFunctions.push('后台列表')\n          if (functions.frontendAdd) activeFunctions.push('前台添加')\n          if (functions.frontendEdit) activeFunctions.push('前台修改')\n          if (functions.frontendDelete) activeFunctions.push('前台删除')\n          if (functions.miniAdd) activeFunctions.push('小程序添加')\n\n          return activeFunctions.slice(0, 3) // 只显示前3个功能\n        } catch (error) {\n          console.error('解析表功能配置失败:', error)\n          return []\n        }\n      }\n\n      // 打开表设计弹窗\n      const openTableDesignModal = (table = null) => {\n        if (table) {\n          // 编辑现有表\n          currentTableDesign.value = {\n            id: table.tid,\n            chineseName: table.tword || '',\n            englishName: table.tname || '',\n            menuOrder: parseInt(table.by2 || '1'),\n            generateData: '0',\n            generateDataCount: parseInt(table.by1 || '0'),\n            functions: table.tgn ? JSON.parse(table.tgn) : {\n              backendAdd: true, backendEdit: true, backendDelete: true,\n              backendDetail: true, backendList: false, batchImport: false,\n              batchExport: false, backendLogin: false, backendRegister: false,\n              backendProfile: false, backendPassword: false,\n              frontendAdd: false, frontendEdit: false, frontendDelete: false,\n              frontendDetail: false, frontendList: false, frontendLogin: false,\n              miniAdd: false, miniEdit: false, miniDelete: false,\n              miniDetail: false, miniList: false\n            },\n            fields: table.fields || [\n              {\n                id: 1,\n                chineseName: '主键ID',\n                englishName: 'id',\n                type: 'int',\n                controlType: '文本框',\n                required: true,\n                searchable: false,\n                visible: true,\n                existsCheck: false\n              }\n            ]\n          }\n        } else {\n          // 创建新表\n          resetTableDesign()\n        }\n        showTableDesignModal.value = true\n      }\n\n      // 重置表设计数据\n      const resetTableDesign = () => {\n        currentTableDesign.value = {\n          id: null,\n          chineseName: '',\n          englishName: '',\n          menuOrder: 1,\n          generateData: '0',\n          generateDataCount: 0,\n          functions: {\n            backendAdd: true, backendEdit: true, backendDelete: true,\n            backendDetail: true, backendList: false, batchImport: false,\n            batchExport: false, backendLogin: false, backendRegister: false,\n            backendProfile: false, backendPassword: false,\n            frontendAdd: false, frontendEdit: false, frontendDelete: false,\n            frontendDetail: false, frontendList: false, frontendLogin: false,\n            miniAdd: false, miniEdit: false, miniDelete: false,\n            miniDetail: false, miniList: false\n          },\n          fields: [\n            {\n              id: 1,\n              chineseName: '主键ID',\n              englishName: 'id',\n              type: 'int',\n              controlType: '文本框',\n              required: true,\n              searchable: false,\n              visible: true,\n              existsCheck: false\n            }\n          ]\n        }\n      }\n\n      // 关闭表设计弹窗\n      const closeTableDesignModal = () => {\n        showTableDesignModal.value = false\n        currentTableTab.value = 'table-settings'\n      }\n\n      // 表设计弹窗Tab切换功能\n      const switchTableTab = (tabId) => {\n        currentTableTab.value = tabId\n      }\n\n      // 添加字段到设计中\n      const addNewFieldToDesign = () => {\n        const newField = {\n          id: Date.now(),\n          chineseName: '',\n          englishName: '',\n          type: 'varchar(100)',\n          controlType: '文本框',\n          required: true, // 默认必填项选中\n          searchable: false,\n          visible: true,\n          existsCheck: false,\n          relatedTable: '', // 关联表\n          customOptions: '' // 自定义选项\n        }\n        currentTableDesign.value.fields.push(newField)\n      }\n\n      // 清空所有字段\n      const clearAllFields = async () => {\n        try {\n          await ElMessageBox.confirm(\n            '确定要清空所有字段吗？此操作不可撤销。',\n            '清空字段',\n            {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              type: 'warning',\n            }\n          )\n\n          currentTableDesign.value.fields = []\n          ElMessage.success('字段清空成功')\n        } catch (error) {\n          if (error !== 'cancel') {\n            console.error('清空字段失败:', error)\n          }\n        }\n      }\n\n      // 删除设计中的字段\n      const deleteFieldFromDesign = async (index) => {\n        try {\n          await ElMessageBox.confirm(\n            '确定要删除这个字段吗？此操作不可撤销。',\n            '删除字段',\n            {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              type: 'warning',\n            }\n          )\n\n          currentTableDesign.value.fields.splice(index, 1)\n          ElMessage.success('字段删除成功')\n        } catch (error) {\n          if (error !== 'cancel') {\n            console.error('删除字段失败:', error)\n          }\n        }\n      }\n\n      // 上移字段\n      const moveFieldUp = (index) => {\n        if (index > 0) {\n          const field = currentTableDesign.value.fields.splice(index, 1)[0]\n          currentTableDesign.value.fields.splice(index - 1, 0, field)\n        }\n      }\n\n      // 下移字段\n      const moveFieldDown = (index) => {\n        if (index < currentTableDesign.value.fields.length - 1) {\n          const field = currentTableDesign.value.fields.splice(index, 1)[0]\n          currentTableDesign.value.fields.splice(index + 1, 0, field)\n        }\n      }\n\n      // 编辑字段设置\n      const editFieldSettings = (field, index) => {\n        currentFieldSettings.value = { ...field }\n        currentFieldIndex.value = index\n        showInSearchList.value = field.searchable || false\n        showFieldSettingsDialog.value = true\n      }\n\n      // 关闭字段设置弹窗\n      const closeFieldSettingsDialog = () => {\n        showFieldSettingsDialog.value = false\n        currentFieldSettings.value = null\n        currentFieldIndex.value = -1\n      }\n\n      // 保存字段设置\n      const saveFieldSettings = () => {\n        if (currentFieldIndex.value >= 0 && currentFieldSettings.value) {\n          // 更新字段数据\n          const field = currentTableDesign.value.fields[currentFieldIndex.value]\n          field.relatedTable = currentFieldSettings.value.relatedTable\n          field.customOptions = currentFieldSettings.value.customOptions\n          field.searchable = showInSearchList.value\n\n          ElMessage.success('字段设置保存成功')\n          closeFieldSettingsDialog()\n        }\n      }\n\n      // 显示关联表选择器\n      const showRelatedTableSelector = async () => {\n        if (!currentProjectInfo.value || !currentProjectInfo.value.pid) {\n          ElMessage.warning('请先保存项目信息')\n          return\n        }\n\n        try {\n          // 加载项目的所有表\n          await loadAvailableRelatedTables()\n          showRelatedTableDialog.value = true\n        } catch (error) {\n          console.error('加载关联表失败:', error)\n          ElMessage.error('加载关联表失败')\n        }\n      }\n\n      // 加载可用的关联表\n      const loadAvailableRelatedTables = async () => {\n        try {\n          const url = base + \"/tables/list?currentPage=1&pageSize=100\"\n          const res = await request.post(url, { pid: currentProjectInfo.value.pid })\n\n          if (res.code === 200 && res.resdata) {\n            availableRelatedTables.value = res.resdata.map(table => {\n              // 获取表的第二个字段作为显示名称，如果第二个字段是密码则使用第三个字段\n              let displayName = table.tword || table.tname\n              if (table.fields && table.fields.length > 1) {\n                const secondField = table.fields[1]\n                if (secondField && secondField.chineseName && !secondField.chineseName.includes('密码')) {\n                  displayName = secondField.chineseName\n                } else if (table.fields.length > 2) {\n                  const thirdField = table.fields[2]\n                  if (thirdField && thirdField.chineseName) {\n                    displayName = thirdField.chineseName\n                  }\n                }\n              }\n\n              return {\n                primaryKey: table.fields && table.fields.length > 0 ? table.fields[0].englishName : 'id',\n                displayName: displayName,\n                tableName: table.tname,\n                linkValue: '1',\n                selected: false\n              }\n            })\n          }\n        } catch (error) {\n          console.error('加载关联表失败:', error)\n          throw error\n        }\n      }\n\n      // 关闭关联表选择弹窗\n      const closeRelatedTableDialog = () => {\n        showRelatedTableDialog.value = false\n        availableRelatedTables.value = []\n      }\n\n      // 确认关联表选择\n      const confirmRelatedTableSelection = () => {\n        const selectedTables = availableRelatedTables.value.filter(table => table.selected)\n        if (selectedTables.length === 0) {\n          ElMessage.warning('请至少选择一个关联表')\n          return\n        }\n\n        // 构建关联表字符串，格式：doro,dbid,dormitory,1\n        const relatedTableStr = selectedTables.map(table =>\n          `${table.primaryKey},${table.displayName},${table.tableName},${table.linkValue}`\n        ).join(';')\n\n        if (currentFieldSettings.value) {\n          currentFieldSettings.value.relatedTable = relatedTableStr\n        }\n\n        closeRelatedTableDialog()\n        ElMessage.success('关联表设置成功')\n      }\n\n      // 保存表设计\n      const saveTableDesign = async () => {\n        // 验证必填字段\n        if (!currentTableDesign.value.chineseName.trim()) {\n          ElMessage.warning('请输入表的中文名称')\n          return\n        }\n\n        if (!currentTableDesign.value.englishName.trim()) {\n          ElMessage.warning('请输入表的英文名称')\n          return\n        }\n\n        // 验证字段\n        for (let field of currentTableDesign.value.fields) {\n          if (!field.chineseName.trim() || !field.englishName.trim()) {\n            ElMessage.warning('请完善所有字段的中文名称和英文名称')\n            return\n          }\n        }\n\n        // 确保有项目ID\n        if (!currentProjectInfo.value || !currentProjectInfo.value.pid) {\n          ElMessage.error('项目信息缺失，请重新配置项目')\n          return\n        }\n\n        try {\n          // 准备表数据\n          const tableData = {\n            pid: currentProjectInfo.value.pid,\n            tword: currentTableDesign.value.chineseName,\n            tname: currentTableDesign.value.englishName,\n            tgn: JSON.stringify(currentTableDesign.value.functions),\n            tlist: '',\n            vlist: '',\n            by1: (currentTableDesign.value.generateDataCount || 0).toString(),\n            by2: (currentTableDesign.value.menuOrder || 1).toString()\n          }\n\n          // 如果是编辑模式，添加tid\n          if (currentTableDesign.value.id) {\n            tableData.tid = currentTableDesign.value.id\n          }\n\n          // 保存表信息\n          const isUpdate = currentTableDesign.value.id\n          const url = base + (isUpdate ? \"/tables/update\" : \"/tables/add\")\n          const res = await request.post(url, tableData)\n\n          if (res.code === 200) {\n            let tableId = currentTableDesign.value.id\n            if (!isUpdate && res.resdata) {\n              // 后端返回的是新创建的表ID\n              tableId = res.resdata\n              currentTableDesign.value.id = tableId\n            }\n\n            // 如果有字段，保存字段信息\n            if (currentTableDesign.value.fields.length > 0) {\n              // 先删除原有字段（如果是更新模式）\n              if (isUpdate) {\n                try {\n                  await request.post(base + \"/mores/deleteByTid\", { tid: tableId })\n                } catch (error) {\n                  console.warn('删除原有字段失败:', error)\n                }\n              }\n\n              // 保存新字段\n              for (let field of currentTableDesign.value.fields) {\n                const fieldData = {\n                  tid: tableId,\n                  moname: field.englishName,\n                  mozname: field.chineseName,\n                  motype: field.type,\n                  moflag: field.controlType,\n                  molong: '',\n                  moyz: field.required ? '1' : '0',\n                  mobt: field.searchable ? '1' : '0',\n                  by1: field.visible ? '1' : '0',\n                  by2: field.existsCheck ? '1' : '0',\n                  by3: field.relatedTable || '',\n                  by4: field.customOptions || '',\n                  by5: '',\n                  by6: ''\n                }\n\n                try {\n                  await request.post(base + \"/mores/add\", fieldData)\n                } catch (error) {\n                  console.error('保存字段失败:', field, error)\n                }\n              }\n            }\n\n            ElMessage.success('表设计保存成功')\n            closeTableDesignModal()\n            // 重新加载项目表单列表\n            if (currentProjectInfo.value.pid) {\n              loadProjectTables(currentProjectInfo.value.pid)\n            }\n          } else {\n            ElMessage.error(res.msg || '表保存失败')\n          }\n        } catch (error) {\n          console.error('保存表设计失败:', error)\n          ElMessage.error('保存表设计失败，请检查网络连接')\n        }\n      }\n\n      // 删除表\n      const deleteTable = async (table) => {\n        try {\n          await ElMessageBox.confirm(\n            `确定要删除表 \"${table.tword || table.tname}\" 吗？`,\n            '确认删除',\n            {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              type: 'warning',\n            }\n          )\n\n          const url = base + \"/tables/del?id=\" + table.tid\n          const res = await request.post(url, {})\n\n          if (res.code === 200) {\n            ElMessage.success('删除成功')\n            // 重新加载表单列表\n            if (currentProjectInfo.value && currentProjectInfo.value.pid) {\n              loadProjectTables(currentProjectInfo.value.pid)\n            }\n          } else {\n            ElMessage.error(res.msg || '删除失败')\n          }\n        } catch (error) {\n          if (error !== 'cancel') {\n            console.error('删除表失败:', error)\n            ElMessage.error('删除失败，请检查网络连接')\n          }\n        }\n      }\n\n      // 下一步操作\n      const nextStep = async () => {\n        if (activeTab.value === 'project') {\n          // 从项目配置到表单设计\n          if (!selectedProject.value) {\n            ElMessage.warning('请先选择项目类型')\n            return\n          }\n          if (!projectForm.name) {\n            ElMessage.warning('请输入项目中文名称')\n            return\n          }\n\n          // 验证必填字段\n          if (projectForm.databaseMode === 'new') {\n            if (!projectForm.projectCode) {\n              ElMessage.warning('请输入项目编号')\n              return\n            }\n            if (!projectForm.databaseName) {\n              ElMessage.warning('请输入数据库名称')\n              return\n            }\n          } else if (projectForm.databaseMode === 'existing') {\n            if (!projectForm.selectedDatabase) {\n              ElMessage.warning('请选择已有数据库')\n              return\n            }\n          }\n\n          // 设置当前项目信息\n          currentProjectInfo.value = {\n            name: projectForm.name,\n            projectCode: projectForm.projectCode,\n            databaseName: projectForm.databaseName,\n            pid: projectForm.selectedDatabase || null\n          }\n\n          // 保存或更新项目到数据库\n          const result = await saveOrUpdateProject()\n          if (!result.success) return\n\n          // 更新项目ID\n          if (result.projectId) {\n            currentProjectInfo.value.pid = result.projectId\n          }\n\n          // 如果是已有数据库模式，加载项目表单\n          if (projectForm.databaseMode === 'existing' && projectForm.selectedDatabase) {\n            loadProjectTables(projectForm.selectedDatabase)\n          } else if (currentProjectInfo.value.pid) {\n            // 新建项目也加载表单（可能为空）\n            loadProjectTables(currentProjectInfo.value.pid)\n          }\n\n          activeTab.value = 'form'\n          ElMessage.success('项目配置保存成功，请继续设计表单')\n        } else if (activeTab.value === 'form') {\n          // 从表单设计到项目生成\n          if (!projectTables.value || projectTables.value.length === 0) {\n            ElMessage.warning('请先设计数据表，不能为空')\n            return\n          }\n          activeTab.value = 'generate'\n          ElMessage.success('表单设计完成，请生成项目')\n        }\n      }\n\n      const handleTabClick = (tab) => {\n        console.log('切换到标签页:', tab.props.name)\n      }\n\n      // 组件挂载时检查登录状态\n      onMounted(() => {\n        checkLoginStatus()\n      })\n\n      return {\n        activeTab,\n        logLevel,\n        isLoggedIn,\n        loginDialogVisible,\n        loginLoading,\n        loginFormRef,\n        userInfo,\n        loginForm,\n        loginRules,\n        selectedProject,\n        projectsLoading,\n        availableDatabases,\n        projectForm,\n        currentProjectInfo,\n        projectTables,\n        projectTablesLoading,\n        showTableDesignModal,\n        currentTableTab,\n        currentTableDesign,\n        formFields,\n        tableData,\n        sqlContent,\n        logs,\n        handleTabClick,\n        handleLogin,\n        handleUserCommand,\n        handleLogout,\n        selectProject,\n        getProjectName,\n        openTemplateModal,\n        openFrontTemplateModal,\n        handleDatabaseModeChange,\n        handleProjectSelect,\n        loadProjectTables,\n        openTableDesignModal,\n        closeTableDesignModal,\n        switchTableTab,\n        addNewFieldToDesign,\n        clearAllFields,\n        deleteFieldFromDesign,\n        moveFieldUp,\n        moveFieldDown,\n        saveTableDesign,\n        deleteTable,\n        nextStep,\n        getTableFieldCount,\n        getTableFunctions,\n        backendFunctions,\n        frontendFunctions,\n        miniFunctions,\n        resetFields,\n        showAiGeneratorModal,\n\n        // AI生成器相关\n        showAiGeneratorDialog,\n        aiGeneratorInput,\n        aiGenerationInProgress,\n        hideAiGeneratorModal,\n        confirmAiGeneration,\n        doubaoGenerate,\n        createFormFromAI,\n        inferControlType,\n\n        // 模板选择相关\n        showBackendTemplateDialog,\n        showFrontendTemplateDialog,\n        showTemplateDetailDialog,\n        backendTemplates,\n        frontendTemplates,\n        templatesLoading,\n        currentTemplateDetail,\n        showBackendTemplateSelector,\n        showFrontendTemplateSelector,\n        selectBackendTemplate,\n        selectFrontendTemplate,\n        viewTemplateDetail,\n        getTemplateImageUrl,\n        handleImageError,\n\n        // 项目生成相关\n        generationInProgress,\n        generationResult,\n        canGenerate,\n        generateProject,\n        downloadProject,\n\n        // SQL脚本生成相关\n        sqlGenerationInProgress,\n        generateSqlScript,\n        exportSqlScript,\n        copySqlScript,\n\n        // 数据脚本生成相关\n        dataGenerationInProgress,\n        dataContent,\n        generateDataScript,\n        copyDataScript,\n\n        // 字段设置相关\n        showFieldSettingsDialog,\n        currentFieldSettings,\n        showInSearchList,\n        editFieldSettings,\n        closeFieldSettingsDialog,\n        saveFieldSettings,\n        showRelatedTableSelector,\n\n        // 关联表选择相关\n        showRelatedTableDialog,\n        availableRelatedTables,\n        closeRelatedTableDialog,\n        confirmRelatedTableSelection\n      }\n    }\n  }\n</script>"]}]}