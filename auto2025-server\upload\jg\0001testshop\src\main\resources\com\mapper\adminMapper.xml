<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!--
    管理员数据访问层映射文件

    功能说明：
    1. 提供管理员的数据库操作SQL映射
    2. 支持动态条件查询和分页查询
    3. 包含完整的CRUD操作

    对应表：admin
    对应实体：com.model.Admin
-->
<mapper namespace="com.mapper.AdminMapper">

	<!-- ==================== 查询操作 ==================== -->

	<!-- 查询所有管理员记录 -->
	<select id="findAdminList" resultType="com.model.Admin">
        select * from admin
        order by id desc
	</select>

	<!-- 动态条件查询管理员记录 -->
	<select id="query" parameterType="java.util.Map" resultType="com.model.Admin">
        select *
        from admin a
		<where>
			<!-- 主键查询条件 -->
			<if test="id != null and id != 0">
                and a.id = #{id}
			</if>
			<!-- 动态字段查询条件 -->
            		<if test="id != null and id != 0">
		    and a.id = #{id}
		</if>
		<if test="lname != null and lname != ''">
		    and a.lname = #{lname}
		</if>
		<if test="password != null and password != ''">
		    and a.password = #{password}
		</if>
		<if test="marrys != null and marrys != ''">
		    and a.marrys = #{marrys}
		</if>

			<!-- 自定义查询条件 -->
			<if test="condition != null and condition != ''">
                ${condition}
			</if>
		</where>
		<!-- 排序逻辑 -->
		<if test="sort != null and sort != ''">
            order by ${sort}
		</if>
		<if test="sort == null or sort == ''">
            order by a.id desc
		</if>
		<!-- 分页逻辑 -->
		<if test="page != null and page != ''">
            limit #{offset}, #{pageSize}
		</if>
	</select>

	<!-- 统计符合条件的管理员记录总数 -->
	<select id="getCount" parameterType="java.util.Map" resultType="Int">
        select count(0) from admin a
		<where>
			<!-- 主键查询条件 -->
			<if test="id != null and id != 0">
                and a.id = #{id}
			</if>
			<!-- 动态字段查询条件 -->
            		<if test="id != null and id != 0">
		    and a.id = #{id}
		</if>
		<if test="lname != null and lname != ''">
		    and a.lname = #{lname}
		</if>
		<if test="password != null and password != ''">
		    and a.password = #{password}
		</if>
		<if test="marrys != null and marrys != ''">
		    and a.marrys = #{marrys}
		</if>

			<!-- 自定义查询条件 -->
			<if test="condition != null and condition != ''">
                ${condition}
			</if>
		</where>
	</select>

	<!-- 根据ID查询单个管理员记录 -->
	<select id="queryAdminById" parameterType="Integer" resultType="com.model.Admin">
        select *
        from admin a
        where a.id = #{value}
	</select>

	<!-- ==================== 插入操作 ==================== -->

	<!-- 新增管理员记录 -->
	<insert id="insertAdmin" useGeneratedKeys="true" keyProperty="id" parameterType="com.model.Admin">
        insert into admin
        (id,lname,password,marrys)
        values
        (#{id},#{lname},#{password},#{marrys})
	</insert>

	<!-- ==================== 更新操作 ==================== -->

	<!-- 更新管理员记录 -->
	<update id="updateAdmin" parameterType="com.model.Admin">
        update admin
		<set>
			<!-- 动态更新字段 -->
            		<if test="lname != null and lname != ''">
		    lname = #{lname},
		</if>
		<if test="password != null and password != ''">
		    password = #{password},
		</if>
		<if test="marrys != null and marrys != ''">
		    marrys = #{marrys},
		</if>

		</set>
		<where>
			<!-- 自定义更新条件 -->
			<if test="condition != null and condition != ''">
                ${condition}
			</if>
			<!-- 主键更新条件 -->
			<if test="id != null">
				id = #{id}
			</if>
		</where>
	</update>

	<!-- ==================== 删除操作 ==================== -->

	<!-- 根据ID删除管理员记录 -->
	<delete id="deleteAdmin" parameterType="Integer">
        delete from admin
        where id = #{value}
	</delete>

</mapper>
