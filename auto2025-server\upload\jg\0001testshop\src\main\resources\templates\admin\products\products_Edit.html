<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
<head th:replace="@{/admin/head.html}"></head>
  
</head>

<body>
    <div class="content-area" style="height: 100%; padding: 20px;">
        <h4 class="page-title">编辑产品</h4>

        <div>
        <div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">编辑产品</h5>
        </div>
        <div class="card-body">
            <form id="productsForm" method="post">
                        <input type="hidden" id="pid" name="pid" th:value="${item.pid}">
                <div class="form-group row mb-3">
                    <label for="pname" class="col-sm-3 col-form-label">产品名称 <span class="required">*</span></label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="pname" name="pname" th:value="${item.pname}">
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label for="cid" class="col-sm-3 col-form-label">产品分类 <span class="required">*</span></label>
                    <div class="col-sm-9">
                        <input type="hidden" id="cid" name="cid" th:value="${item.cid}">
                        <input type="text" class="form-control" readonly th:value="${item.cid}">
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label for="photo" class="col-sm-3 col-form-label">产品图片 <span class="required">*</span></label>
                    <div class="col-sm-9">
                        <div class="image-upload-container">
                            <input type="file" class="form-control" id="photo_file" accept="image/*" onchange="uploadImage(this, 'photo')">
                            <input type="hidden" id="photo_hidden" name="photo" th:value="${item.photo}">
                            <div class="current-image mt-2" th:if="${item.photo}">
                                <small class="text-muted">当前图片: </small><br>
                                <img th:src="@{'/upload/' + ${item.photo}}" alt="当前图片" style="max-width: 150px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px; padding: 5px;">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label for="price" class="col-sm-3 col-form-label">价格 <span class="required">*</span></label>
                    <div class="col-sm-9">
                        <input type="number" class="form-control" id="price" name="price" th:value="${item.price}">
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label for="quantity" class="col-sm-3 col-form-label">数量 <span class="required">*</span></label>
                    <div class="col-sm-9">
                        <input type="number" class="form-control" id="quantity" name="quantity" th:value="${item.quantity}">
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label for="pmemo" class="col-sm-3 col-form-label">产品介绍 <span class="required">*</span></label>
                    <div class="col-sm-9">
                        <link th:href="@{/kindeditor/themes/default/default.css}" rel="stylesheet">
                        <link th:href="@{/kindeditor/plugins/code/prettify.css}" rel="stylesheet">
                        <script th:src="@{/kindeditor/kindeditor.js}"></script>
                        <script th:src="@{/kindeditor/lang/zh_CN.js}"></script>
                        <script th:src="@{/kindeditor/plugins/code/prettify.js}"></script>
                        <script>
                            KindEditor.ready(function(K) {
                                var editor1 = K.create('textarea[name="pmemo"]', {
                                    cssPath : '/kindeditor/plugins/code/prettify.css',
                                    uploadJson : '/fileUpload',
                                    allowFileManager : true,
                                    afterCreate : function() {
                                        var self = this;
                                        K.ctrl(document, 13, function() {
                                            self.sync();
                                            document.forms['example'].submit();
                                        });
                                        K.ctrl(self.edit.doc, 13, function() {
                                            self.sync();
                                            document.forms['example'].submit();
                                        });
                                    }
                                });
                                prettyPrint();
                            });
                        </script>
                        <textarea id="pmemo" name="pmemo" cols="100" rows="8" style="width:700px;height:400px;visibility:hidden;" th:text="${item.pmemo}"></textarea>
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-9 offset-sm-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-floppy-fill"></i> 保存
                        </button>
                        <a href="/productsList" class="btn btn-secondary ml-2">
                            <i class="bi bi-arrow-left"></i> 返回
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
        <script>
        // 文件上传函数
        function uploadFile(input, fieldName) {
            if (input.files && input.files[0]) {
                var formData = new FormData();
                formData.append('file', input.files[0]);
                formData.append('c', fieldName);
                
                $.ajax({
                    url: '/upload_re',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            // 将服务器返回的文件名设置到隐藏字段中
                            $('#' + fieldName + '_hidden').val(response.fileName);
                            CommonUtils.showToast('文件上传成功', 'success');
                        } else {
                            CommonUtils.showToast(response.message || '文件上传失败', 'error');
                        }
                    },
                    error: function() {
                        CommonUtils.showToast('文件上传失败', 'error');
                    }
                });
            }
        }
        
        // 图片上传函数
        function uploadImage(input, fieldName) {
            if (input.files && input.files[0]) {
                var formData = new FormData();
                formData.append('file', input.files[0]);
                formData.append('c', fieldName);
                
                $.ajax({
                    url: '/upload_re',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            // 将服务器返回的文件名设置到隐藏字段中
                            $('#' + fieldName + '_hidden').val(response.fileName);
                            CommonUtils.showToast('图片上传成功', 'success');
                            
                            // 显示预览图片
                            var previewContainer = $('#' + fieldName + '_file').closest('.image-upload-container').find('.current-image');
                            if (previewContainer.length === 0) {
                                $('#' + fieldName + '_file').closest('.image-upload-container').append('<div class="current-image mt-2"><small class="text-muted">当前图片: </small><br><img src="/upload/' + response.fileName + '" alt="预览图片" style="max-width: 150px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px; padding: 5px;"></div>');
                            } else {
                                previewContainer.find('img').attr('src', '/upload/' + response.fileName);
                            }
                        } else {
                            CommonUtils.showToast(response.message || '图片上传失败', 'error');
                        }
                    },
                    error: function() {
                        CommonUtils.showToast('图片上传失败', 'error');
                    }
                });
            }
        }
        
        // 图片预览函数
        function previewImage(src) {
            var modal = '<div class="modal fade" id="imagePreviewModal" tabindex="-1">' +
                       '<div class="modal-dialog modal-lg modal-dialog-centered">' +
                       '<div class="modal-content">' +
                       '<div class="modal-header">' +
                       '<h5 class="modal-title">图片预览</h5>' +
                       '<button type="button" class="btn-close" data-bs-dismiss="modal"></button>' +
                       '</div>' +
                       '<div class="modal-body text-center">' +
                       '<img src="' + src + '" class="img-fluid" style="max-height: 70vh;">' +
                       '</div>' +
                       '</div></div></div>';
            
            $('body').append(modal);
            var modalInstance = new bootstrap.Modal(document.getElementById('imagePreviewModal'));
            modalInstance.show();
            
            $('#imagePreviewModal').on('hidden.bs.modal', function() {
                $(this).remove();
            });
        }
        
        $(document).ready(function() {
            $('#productsForm').on('submit', function(e) {
                e.preventDefault();
                
                // 获取表单字段值
                var quantity = $('#quantity').val().trim();
                var pname = $('#pname').val().trim();
                var price = $('#price').val().trim();
                var pmemo = $('#pmemo').val().trim();
                var photo = $('#photo').val().trim();
                var pid = $('#pid').val().trim();
                var cid = $('#cid').val().trim();
                
                // 非空验证
                if (!pid) {
                    CommonUtils.showToast('请输入产品id', 'error');
                    return;
                }
                if (!pname) {
                    CommonUtils.showToast('请输入产品名称', 'error');
                    return;
                }
                if (!cid) {
                    CommonUtils.showToast('请输入产品分类', 'error');
                    return;
                }
                if (!photo) {
                    CommonUtils.showToast('请输入产品图片', 'error');
                    return;
                }
                if (!price) {
                    CommonUtils.showToast('请输入价格', 'error');
                    return;
                }
                if (!quantity) {
                    CommonUtils.showToast('请输入数量', 'error');
                    return;
                }
                if (!pmemo) {
                    CommonUtils.showToast('请输入产品介绍', 'error');
                    return;
                }
                
                
                // 使用通用表单提交方法
                CommonUtils.submitForm('#productsForm', 
                    '/productsEdit', 
                    function(response) {
                        // 成功回调
                        CommonUtils.showToast('操作成功！', 'success');
                        setTimeout(function() {
                            window.location.href = '/productsList';
                        }, 1500);
                    },
                    function(error) {
                        // 错误回调
                        console.error('提交失败:', error);
                    }
                );
            });
        });
        </script>
        <script>
        $(document).ready(function() {
            // 页面加载时的初始化操作
            console.log('products 页面加载完成');
        });
        </script>

        </div>
    </div>

    <!-- Bootstrap JS -->
</body>

</html>