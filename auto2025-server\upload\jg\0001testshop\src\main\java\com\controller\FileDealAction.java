package com.controller;

import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.servlet.ServletContext;
import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.fileupload.FileUploadException;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.util.ComUtil;
import com.util.DateUtils;
import com.util.PageBean;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

@Controller
public class FileDealAction {

	@RequestMapping(value = "/toupload")
	public String toupload(HttpServletRequest req) {
		req.setAttribute("cc", req.getParameter("c"));
		return "common/upload1";
	}

	// 前台上传文件
	@RequestMapping(value = "/toupload2")
	public String toupload2(HttpServletRequest req) {
		req.setAttribute("cc", req.getParameter("c"));
		return "/common/upload1";
	}

	@RequestMapping(value = "/upload_re")
	@ResponseBody
	public Map<String, Object> upload_re(HttpServletRequest req, @RequestParam("file") MultipartFile data)
			throws Exception {
		Map<String, Object> result = new HashMap<String, Object>();

		try {
			System.out.println("开始处理文件上传...");

			// 验证文件是否为空
			if (data.isEmpty()) {
				result.put("success", false);
				result.put("message", "请选择文件");
				return result;
			}

			String file_name = data.getOriginalFilename();
			System.out.println("原始文件名: " + file_name);

			// 验证文件类型
			if (file_name == null || !file_name.toLowerCase().matches(".*\\.(jpg|jpeg|png|gif|bmp)$")) {
				result.put("success", false);
				result.put("message", "只允许上传图片文件(jpg, jpeg, png, gif, bmp)");
				return result;
			}

			// 验证文件大小（限制为2MB）
			if (data.getSize() > 2 * 1024 * 1024) {
				result.put("success", false);
				result.put("message", "文件大小不能超过2MB");
				return result;
			}

			String newFile1Name = new Date().getTime() + file_name.substring(file_name.lastIndexOf("."));
			System.out.println("新文件名: " + newFile1Name);

			// 修改保存路径 - 同时保存到两个位置
			String projectRoot = System.getProperty("user.dir");

			// 保存到 target/classes/static/upload/ (运行时访问)
			String runtimeUploadPath = projectRoot + "/target/classes/static/upload/";
			File runtimeDir = new File(runtimeUploadPath);
			if (!runtimeDir.exists()) {
				runtimeDir.mkdirs();
			}
			File runtimeFile = new File(runtimeDir, newFile1Name);

			// 保存到 src/main/resources/static/upload/ (源代码目录)
			String sourceUploadPath = projectRoot + "/src/main/resources/static/upload/";
			File sourceDir = new File(sourceUploadPath);
			if (!sourceDir.exists()) {
				sourceDir.mkdirs();
			}
			File sourceFile = new File(sourceDir, newFile1Name);

			// 保存文件到两个位置
			data.transferTo(runtimeFile);
			Files.copy(runtimeFile.toPath(), sourceFile.toPath(), StandardCopyOption.REPLACE_EXISTING);

			System.out.println("文件保存成功: " + runtimeFile.getAbsolutePath());
			System.out.println("文件备份成功: " + sourceFile.getAbsolutePath());

			// 返回JSON格式的成功响应
			result.put("success", true);
			result.put("fileName", newFile1Name);
			result.put("originalName", file_name);
			result.put("message", "上传成功");

		} catch (Exception e) {
			// 返回JSON格式的错误响应
			result.put("success", false);
			result.put("message", "上传失败: " + e.getMessage());
			System.err.println("文件上传失败: " + e.getMessage());
			e.printStackTrace();
		}

		return result;
	}

	private void copyFileToStaticFolder(String fileName) throws IOException {
		String projectRoot = System.getProperty("user.dir");
		String staticUploadPath = projectRoot + "/src/main/resources/static/upload/";

		// 确保目标目录存在
		File staticFolder = new File(staticUploadPath);
		if (!staticFolder.exists()) {
			staticFolder.mkdirs();
		}

		File sourceFile = new File(projectRoot + "/target/classes/static/upload/", fileName);
		File destFile = new File(staticUploadPath, fileName);

		// 如果源文件存在，则复制文件
		if (sourceFile.exists()) {
			Files.copy(sourceFile.toPath(), destFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
		} else {
			System.out.println("警告：源文件不存在: " + sourceFile.getAbsolutePath());
		}
	}

	private static final ObjectMapper objectMapper = new ObjectMapper();
	private PrintWriter writer = null;

	@RequestMapping(value = "fileUpload", method = RequestMethod.POST)
	public void fileUpload(MultipartHttpServletRequest request,
			HttpServletResponse response) throws ServletException, IOException,
			FileUploadException {
		ServletContext application = request.getSession().getServletContext();
		String savePath = ComUtil.getResourceBasePath();

		// 文件保存目录URL
		String saveUrl = request.getContextPath() + "/upload/";

		// 定义允许上传的文件扩展名
		HashMap<String, String> extMap = new HashMap<String, String>();
		extMap.put("image", "gif,jpg,jpeg,png,bmp");
		extMap.put("flash", "swf,flv");
		extMap.put("media", "swf,flv,mp3,wav,wma,wmv,mid,avi,mpg,asf,rm,rmvb");
		extMap.put("file", "doc,docx,xls,xlsx,ppt,htm,html,txt,zip,rar,gz,bz2");

		// 最大文件大小
		long maxSize = 1000000;

		response.reset();
		response.setCharacterEncoding("UTF-8");
		response.setContentType("text/html");
		writer = response.getWriter();
		// writer.println(json); //想办法把map转成json

		if (!ServletFileUpload.isMultipartContent(request)) {
			writer.println(objectMapper.writeValueAsString(getError("请选择文件。")));
			return;

		}
		// 检查目录
		File uploadDir = new File(savePath);
		if (!uploadDir.isDirectory()) {
			writer.println(objectMapper.writeValueAsString(getError("上传目录不存在。")));
			return;
		}
		// 检查目录写权限
		if (!uploadDir.canWrite()) {
			writer.println(objectMapper.writeValueAsString(getError("上传目录没有写权限。")));
			return;
		}

		String dirName = request.getParameter("dir");
		if (dirName == null) {
			dirName = "";
		}
		if (!extMap.containsKey(dirName)) {
			writer.println(objectMapper.writeValueAsString(getError("目录名不正确。")));
			return;
		}
		// 创建文件夹
		savePath += "/" + dirName + "/";
		saveUrl += dirName + "/";
		File saveDirFile = new File(savePath);
		if (!saveDirFile.exists()) {
			saveDirFile.mkdirs();
		}
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		String ymd = sdf.format(new Date());
		savePath += ymd + "/";
		saveUrl += ymd + "/";
		File dirFile = new File(savePath);
		if (!dirFile.exists()) {
			dirFile.mkdirs();
		}
		List<MultipartFile> files = request.getFiles("imgFile");

		Iterator<MultipartFile> itr = files.iterator();
		while (itr.hasNext()) {
			MultipartFile item = itr.next();
			String fileName = item.getOriginalFilename();
			// 检查文件大小
			if (item.getSize() > maxSize) {
				writer.println(objectMapper.writeValueAsString(getError("上传文件大小超过限制。")));
			}
			// 检查扩展名
			String fileExt = fileName.substring(
					fileName.lastIndexOf(".") + 1).toLowerCase();
			if (!Arrays.<String>asList(extMap.get(dirName).split(","))
					.contains(fileExt)) {
				writer.println(objectMapper
						.writeValueAsString(getError("上传文件扩展名是不允许的扩展名。\n只允许" + extMap.get(dirName) + "格式。")));
				return;
			}

			SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
			String newFileName = df.format(new Date()) + "_"
					+ new Random().nextInt(1000) + "." + fileExt;
			try {
				File uploadedFile = new File(savePath, newFileName);
				item.transferTo(uploadedFile);

			} catch (Exception e) {
				writer.println(objectMapper.writeValueAsString(getError("上传文件失败。")));
			}

			Map<String, Object> msg = new HashMap<String, Object>();
			msg.put("error", 0);
			msg.put("url", saveUrl + newFileName);
			writer.println(objectMapper.writeValueAsString(msg));
			return;

		}
		return;
	}

	private Map<String, Object> getError(String message) {
		Map<String, Object> msg = new HashMap<String, Object>();
		msg.put("error", 1);
		msg.put("message", message);
		return msg;
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@RequestMapping(value = "fileManager", method = RequestMethod.GET)
	public void fileManager(HttpServletRequest request,
			HttpServletResponse response) throws ServletException, IOException {
		ServletContext application = request.getSession().getServletContext();
		ServletOutputStream out = response.getOutputStream();
		// 根目录路径，可以指定绝对路径，比如 /var/www/attached/
		String rootPath = application.getRealPath("/") + "attached/";
		// 根目录URL，可以指定绝对路径，比如 http://www.yoursite.com/attached/
		String rootUrl = request.getContextPath() + "/attached/";
		// 图片扩展名
		String[] fileTypes = new String[] { "gif", "jpg", "jpeg", "png", "bmp" };

		String dirName = request.getParameter("dir");
		if (dirName != null) {
			if (!Arrays.<String>asList(
					new String[] { "image", "flash", "media", "file" })
					.contains(dirName)) {
				out.println("Invalid Directory name.");
				return;
			}
			rootPath += dirName + "/";
			rootUrl += dirName + "/";
			File saveDirFile = new File(rootPath);
			if (!saveDirFile.exists()) {
				saveDirFile.mkdirs();
			}
		}
		// 根据path参数，设置各路径和URL
		String path = request.getParameter("path") != null ? request
				.getParameter("path") : "";
		String currentPath = rootPath + path;
		String currentUrl = rootUrl + path;
		String currentDirPath = path;
		String moveupDirPath = "";
		if (!"".equals(path)) {
			String str = currentDirPath.substring(0,
					currentDirPath.length() - 1);
			moveupDirPath = str.lastIndexOf("/") >= 0 ? str.substring(0,
					str.lastIndexOf("/") + 1) : "";
		}

		// 排序形式，name or size or type
		String order = request.getParameter("order") != null ? request
				.getParameter("order").toLowerCase() : "name";

		// 不允许使用..移动到上一级目录
		if (path.indexOf("..") >= 0) {
			out.println("Access is not allowed.");
			return;
		}
		// 最后一个字符不是/
		if (!"".equals(path) && !path.endsWith("/")) {
			out.println("Parameter is not valid.");
			return;
		}
		// 目录不存在或不是目录
		File currentPathFile = new File(currentPath);
		if (!currentPathFile.isDirectory()) {
			out.println("Directory does not exist.");
			return;
		}
		// 遍历目录取的文件信息
		List<Hashtable> fileList = new ArrayList<Hashtable>();
		if (currentPathFile.listFiles() != null) {
			for (File file : currentPathFile.listFiles()) {
				Hashtable<String, Object> hash = new Hashtable<String, Object>();
				String fileName = file.getName();
				if (file.isDirectory()) {
					hash.put("is_dir", true);
					hash.put("has_file", (file.listFiles() != null));
					hash.put("filesize", 0L);
					hash.put("is_photo", false);
					hash.put("filetype", "");
				} else if (file.isFile()) {
					String fileExt = fileName.substring(
							fileName.lastIndexOf(".") + 1).toLowerCase();
					hash.put("is_dir", false);
					hash.put("has_file", false);
					hash.put("filesize", file.length());
					hash.put("is_photo", Arrays.<String>asList(fileTypes)
							.contains(fileExt));
					hash.put("filetype", fileExt);
				}
				hash.put("filename", fileName);
				hash.put("datetime",
						new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(file
								.lastModified()));
				fileList.add(hash);
			}
		}

		if ("size".equals(order)) {
			Collections.sort(fileList, new SizeComparator());
		} else if ("type".equals(order)) {
			Collections.sort(fileList, new TypeComparator());
		} else {
			Collections.sort(fileList, new NameComparator());
		}
		Map<String, Object> msg = new HashMap<String, Object>();
		msg.put("moveup_dir_path", moveupDirPath);
		msg.put("current_dir_path", currentDirPath);
		msg.put("current_url", currentUrl);
		msg.put("total_count", fileList.size());
		msg.put("file_list", fileList);
		response.setContentType("application/json; charset=UTF-8");
		String msgStr = objectMapper.writeValueAsString(msg);
		out.println(msgStr);
	}

	@SuppressWarnings("rawtypes")
	class NameComparator implements Comparator {
		public int compare(Object a, Object b) {
			Hashtable hashA = (Hashtable) a;
			Hashtable hashB = (Hashtable) b;
			if (((Boolean) hashA.get("is_dir"))
					&& !((Boolean) hashB.get("is_dir"))) {
				return -1;
			} else if (!((Boolean) hashA.get("is_dir"))
					&& ((Boolean) hashB.get("is_dir"))) {
				return 1;
			} else {
				return ((String) hashA.get("filename"))
						.compareTo((String) hashB.get("filename"));
			}
		}
	}

	@SuppressWarnings("rawtypes")
	class SizeComparator implements Comparator {
		public int compare(Object a, Object b) {
			Hashtable hashA = (Hashtable) a;
			Hashtable hashB = (Hashtable) b;
			if (((Boolean) hashA.get("is_dir"))
					&& !((Boolean) hashB.get("is_dir"))) {
				return -1;
			} else if (!((Boolean) hashA.get("is_dir"))
					&& ((Boolean) hashB.get("is_dir"))) {
				return 1;
			} else {
				if (((Long) hashA.get("filesize")) > ((Long) hashB
						.get("filesize"))) {
					return 1;
				} else if (((Long) hashA.get("filesize")) < ((Long) hashB
						.get("filesize"))) {
					return -1;
				} else {
					return 0;
				}
			}
		}
	}

	@SuppressWarnings("rawtypes")
	class TypeComparator implements Comparator {
		public int compare(Object a, Object b) {
			Hashtable hashA = (Hashtable) a;
			Hashtable hashB = (Hashtable) b;
			if (((Boolean) hashA.get("is_dir"))
					&& !((Boolean) hashB.get("is_dir"))) {
				return -1;
			} else if (!((Boolean) hashA.get("is_dir"))
					&& ((Boolean) hashB.get("is_dir"))) {
				return 1;
			} else {
				return ((String) hashA.get("filetype"))
						.compareTo((String) hashB.get("filetype"));
			}
		}
	}

}
