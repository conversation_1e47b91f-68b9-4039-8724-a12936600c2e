{"remainingRequest": "J:\\auto2025\\auto2025-web\\node_modules\\babel-loader\\lib\\index.js!J:\\auto2025\\auto2025-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!J:\\auto2025\\auto2025-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!J:\\auto2025\\auto2025-web\\src\\views\\admin\\small\\SmallAdd.vue?vue&type=script&lang=js", "dependencies": [{"path": "J:\\auto2025\\auto2025-web\\src\\views\\admin\\small\\SmallAdd.vue", "mtime": 1749439569666}, {"path": "J:\\auto2025\\auto2025-web\\babel.config.js", "mtime": 1748614864000}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748675476717}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748675484569}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748675476717}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1748675485115}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["request", "base", "WangEditor", "name", "components", "data", "activeTab", "uploadVisible", "btnLoading", "formData", "add<PERSON><PERSON>", "bid", "required", "message", "trigger", "sname", "mounted", "getbigList", "methods", "save", "$refs", "validate", "valid", "url", "post", "then", "res", "code", "$message", "type", "offset", "$router", "push", "path", "msg", "goBack", "para", "listLoading", "bigList", "resdata", "<PERSON><PERSON><PERSON><PERSON>", "val", "memo4"], "sources": ["J:\\auto2025\\auto2025-web\\src\\views\\admin\\small\\SmallAdd.vue"], "sourcesContent": ["<template>\r\n  <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n    <el-form :model=\"formData\" label-width=\"20%\" ref=\"formDataRef\" :rules=\"addrules\" align=\"left\">\r\n      <el-form-item label=\"大类\" prop=\"bid\">\r\n        <el-select v-model=\"formData.bid\" placeholder=\"请选择\" size=\"small\">\r\n          <el-option v-for=\"item in bigList\" :key=\"item.bid\" :label=\"item.bname\" :value=\"item.bid\"></el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"小类名称\" prop=\"sname\">\r\n        <el-input v-model=\"formData.sname\" placeholder=\"小类名称\" style=\"width:50%;\"></el-input>\r\n      </el-form-item>\r\n\r\n      <el-tabs v-model=\"activeTab\">\r\n        <el-tab-pane label=\"内容1\" name=\"memo1\">\r\n          <el-form-item label=\"内容1\" prop=\"memo1\">\r\n            <el-input type=\"textarea\" :rows=\"25\" v-model=\"formData.memo1\" placeholder=\"内容1\" size=\"small\"></el-input>\r\n          </el-form-item>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"内容2\" name=\"memo2\">\r\n          <el-form-item label=\"内容2\" prop=\"memo2\">\r\n            <el-input type=\"textarea\" :rows=\"25\" v-model=\"formData.memo2\" placeholder=\"内容2\" size=\"small\"></el-input>\r\n          </el-form-item>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"内容3\" name=\"memo3\">\r\n          <el-form-item label=\"内容3\" prop=\"memo3\">\r\n            <el-input type=\"textarea\" :rows=\"25\" v-model=\"formData.memo3\" placeholder=\"内容3\" size=\"small\"></el-input>\r\n          </el-form-item>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"内容4\" name=\"memo4\">\r\n          <el-form-item label=\"内容4\" prop=\"memo4\">\r\n            <WangEditor ref=\"wangEditorRef\" v-model=\"formData.memo4\" :config=\"editorConfig\" :isClear=\"isClear\"\r\n              @change=\"editorChange\"></WangEditor>\r\n          </el-form-item>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"备用1\" name=\"by1\">\r\n          <el-form-item label=\"备用1\" prop=\"by1\">\r\n            <el-input type=\"textarea\" :rows=\"25\" v-model=\"formData.by1\" placeholder=\"备用1\" size=\"small\"></el-input>\r\n          </el-form-item>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"备用2\" name=\"by2\">\r\n          <el-form-item label=\"备用2\" prop=\"by2\">\r\n            <el-input type=\"textarea\" :rows=\"25\" v-model=\"formData.by2\" placeholder=\"备用2\" size=\"small\"></el-input>\r\n          </el-form-item>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"备用3\" name=\"by3\">\r\n          <el-form-item label=\"备用3\" prop=\"by3\">\r\n            <el-input type=\"textarea\" :rows=\"25\" v-model=\"formData.by3\" placeholder=\"备用3\" size=\"small\"></el-input>\r\n          </el-form-item>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"备用4\" name=\"by4\">\r\n          <el-form-item label=\"备用4\" prop=\"by4\">\r\n            <el-input type=\"textarea\" :rows=\"25\" v-model=\"formData.by4\" placeholder=\"备用4\" size=\"small\"></el-input>\r\n          </el-form-item>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"备用5\" name=\"by5\">\r\n          <el-form-item label=\"备用5\" prop=\"by5\">\r\n            <el-input type=\"textarea\" :rows=\"25\" v-model=\"formData.by5\" placeholder=\"备用5\" size=\"small\"></el-input>\r\n          </el-form-item>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"备用6\" name=\"by6\">\r\n          <el-form-item label=\"备用6\" prop=\"by6\">\r\n            <el-input type=\"textarea\" :rows=\"25\" v-model=\"formData.by6\" placeholder=\"备用6\" size=\"small\"></el-input>\r\n          </el-form-item>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"备用7\" name=\"by7\">\r\n          <el-form-item label=\"备用7\" prop=\"by7\">\r\n            <el-input type=\"textarea\" :rows=\"25\" v-model=\"formData.by7\" placeholder=\"备用7\" size=\"small\"></el-input>\r\n          </el-form-item>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"备用8\" name=\"by8\">\r\n          <el-form-item label=\"备用8\" prop=\"by8\">\r\n            <el-input type=\"textarea\" :rows=\"25\" v-model=\"formData.by8\" placeholder=\"备用8\" size=\"small\"></el-input>\r\n          </el-form-item>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"备用9\" name=\"by9\">\r\n          <el-form-item label=\"备用9\" prop=\"by9\">\r\n            <el-input type=\"textarea\" :rows=\"25\" v-model=\"formData.by9\" placeholder=\"备用9\" size=\"small\"></el-input>\r\n          </el-form-item>\r\n        </el-tab-pane>\r\n\r\n        <el-tab-pane label=\"备用10\" name=\"by10\">\r\n          <el-form-item label=\"备用10\" prop=\"by10\">\r\n            <el-input type=\"textarea\" :rows=\"25\" v-model=\"formData.by10\" placeholder=\"备用10\" size=\"small\"></el-input>\r\n          </el-form-item>\r\n        </el-tab-pane>\r\n\r\n\r\n\r\n      </el-tabs>\r\n\r\n      <el-form-item>\r\n        <el-button type=\"primary\" size=\"small\" @click=\"save\" :loading=\"btnLoading\" icon=\"el-icon-upload\">提 交</el-button>\r\n        <el-button type=\"info\" size=\"small\" @click=\"goBack\" icon=\"el-icon-back\">返 回</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n  import request, { base } from \"../../../../utils/http\";\r\n  import WangEditor from \"../../../components/WangEditor\";\r\n  export default {\r\n    name: 'SmallAdd',\r\n    components: {\r\n      WangEditor,\r\n    },\r\n    data() {\r\n      return {\r\n        activeTab: 'memo1', // 默认激活第一个标签页\r\n        uploadVisible: false,\r\n        btnLoading: false, //保存按钮加载状态     \r\n        formData: {}, //表单数据           \r\n        addrules: {\r\n          bid: [{ required: true, message: '请选择大类', trigger: 'onchange' }],\r\n          sname: [{ required: true, message: '请输入小类名称', trigger: 'blur' },\r\n          ],\r\n        },\r\n\r\n      };\r\n    },\r\n    mounted() {\r\n\r\n      this.getbigList();\r\n    },\r\n\r\n\r\n    methods: {\r\n      // 添加\r\n      save() {\r\n        this.$refs[\"formDataRef\"].validate((valid) => { //验证表单\r\n          if (valid) {\r\n            let url = base + \"/small/add\";\r\n            this.btnLoading = true;\r\n            request.post(url, this.formData).then((res) => { //发送请求         \r\n              if (res.code == 200) {\r\n                this.$message({\r\n                  message: \"操作成功\",\r\n                  type: \"success\",\r\n                  offset: 320,\r\n                });\r\n                this.$router.push({\r\n                  path: \"/SmallManage\",\r\n                });\r\n              } else {\r\n                this.$message({\r\n                  message: res.msg,\r\n                  type: \"error\",\r\n                  offset: 320,\r\n                });\r\n              }\r\n              this.btnLoading = false;\r\n            });\r\n          }\r\n\r\n        });\r\n      },\r\n\r\n      // 返回\r\n      goBack() {\r\n        this.$router.push({\r\n          path: \"/SmallManage\",\r\n        });\r\n      },\r\n\r\n\r\n      getbigList() {\r\n        let para = {};\r\n        this.listLoading = true;\r\n        let url = base + \"/big/list?currentPage=1&pageSize=1000\";\r\n        request.post(url, para).then((res) => {\r\n          this.bigList = res.resdata;\r\n        });\r\n      },\r\n\r\n\r\n      // 富文本编辑器\r\n      editorChange(val) {\r\n        this.formData.memo4 = val;\r\n      },\r\n\r\n    },\r\n  }\r\n\r\n</script>\r\n<style scoped>\r\n</style>"], "mappings": ";AAiHE,OAAOA,OAAO,IAAIC,IAAG,QAAS,wBAAwB;AACtD,OAAOC,UAAS,MAAO,gCAAgC;AACvD,eAAe;EACbC,IAAI,EAAE,UAAU;EAChBC,UAAU,EAAE;IACVF;EACF,CAAC;EACDG,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE,OAAO;MAAE;MACpBC,aAAa,EAAE,KAAK;MACpBC,UAAU,EAAE,KAAK;MAAE;MACnBC,QAAQ,EAAE,CAAC,CAAC;MAAE;MACdC,QAAQ,EAAE;QACRC,GAAG,EAAE,CAAC;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAW,CAAC,CAAC;QAChEC,KAAK,EAAE,CAAC;UAAEH,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAO,CAAC;MAEjE;IAEF,CAAC;EACH,CAAC;EACDE,OAAOA,CAAA,EAAG;IAER,IAAI,CAACC,UAAU,CAAC,CAAC;EACnB,CAAC;EAGDC,OAAO,EAAE;IACP;IACAC,IAAIA,CAAA,EAAG;MACL,IAAI,CAACC,KAAK,CAAC,aAAa,CAAC,CAACC,QAAQ,CAAEC,KAAK,IAAK;QAAE;QAC9C,IAAIA,KAAK,EAAE;UACT,IAAIC,GAAE,GAAItB,IAAG,GAAI,YAAY;UAC7B,IAAI,CAACO,UAAS,GAAI,IAAI;UACtBR,OAAO,CAACwB,IAAI,CAACD,GAAG,EAAE,IAAI,CAACd,QAAQ,CAAC,CAACgB,IAAI,CAAEC,GAAG,IAAK;YAAE;YAC/C,IAAIA,GAAG,CAACC,IAAG,IAAK,GAAG,EAAE;cACnB,IAAI,CAACC,QAAQ,CAAC;gBACZf,OAAO,EAAE,MAAM;gBACfgB,IAAI,EAAE,SAAS;gBACfC,MAAM,EAAE;cACV,CAAC,CAAC;cACF,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC;gBAChBC,IAAI,EAAE;cACR,CAAC,CAAC;YACJ,OAAO;cACL,IAAI,CAACL,QAAQ,CAAC;gBACZf,OAAO,EAAEa,GAAG,CAACQ,GAAG;gBAChBL,IAAI,EAAE,OAAO;gBACbC,MAAM,EAAE;cACV,CAAC,CAAC;YACJ;YACA,IAAI,CAACtB,UAAS,GAAI,KAAK;UACzB,CAAC,CAAC;QACJ;MAEF,CAAC,CAAC;IACJ,CAAC;IAED;IACA2B,MAAMA,CAAA,EAAG;MACP,IAAI,CAACJ,OAAO,CAACC,IAAI,CAAC;QAChBC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC;IAGDhB,UAAUA,CAAA,EAAG;MACX,IAAImB,IAAG,GAAI,CAAC,CAAC;MACb,IAAI,CAACC,WAAU,GAAI,IAAI;MACvB,IAAId,GAAE,GAAItB,IAAG,GAAI,uCAAuC;MACxDD,OAAO,CAACwB,IAAI,CAACD,GAAG,EAAEa,IAAI,CAAC,CAACX,IAAI,CAAEC,GAAG,IAAK;QACpC,IAAI,CAACY,OAAM,GAAIZ,GAAG,CAACa,OAAO;MAC5B,CAAC,CAAC;IACJ,CAAC;IAGD;IACAC,YAAYA,CAACC,GAAG,EAAE;MAChB,IAAI,CAAChC,QAAQ,CAACiC,KAAI,GAAID,GAAG;IAC3B;EAEF;AACF", "ignoreList": []}]}