package com.service;

import java.util.List;

import com.model.Products;
import com.util.PageBean;

/**
 * 产品业务逻辑服务接口
 *
 * 功能说明：
 * 1. 定义产品相关的业务逻辑接口
 * 2. 提供完整的CRUD操作方法
 * 3. 支持分页查询和条件查询
 * 4. 处理业务逻辑和数据验证
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024
 */
public interface ProductsService {

	/**
	 * 分页查询产品列表
	 * @param products 查询条件对象
	 * @param page 分页参数
	 * @return 产品列表
	 * @throws Exception 异常
	 */
	public List<Products> queryProductsList(Products products, PageBean page) throws Exception;

	/**
	 * 新增产品
	 * @param products 产品实体对象
	 * @return 影响的行数
	 * @throws Exception 异常
	 */
	public int insertProducts(Products products) throws Exception;

	/**
	 * 根据ID删除产品
	 * @param id 主键ID
	 * @return 影响的行数
	 * @throws Exception 异常
	 */
	public int deleteProducts(Integer id) throws Exception;

	/**
	 * 更新产品
	 * @param products 产品实体对象
	 * @return 影响的行数
	 * @throws Exception 异常
	 */
	public int updateProducts(Products products) throws Exception;

	/**
	 * 根据ID查询产品详情
	 * @param id 主键ID
	 * @return 产品实体对象，如果不存在则返回null
	 * @throws Exception 异常
	 */
	public Products queryProductsById(Integer id) throws Exception;

	/**
	 * 统计产品记录总数
	 * @param products 查询条件对象
	 * @return 记录总数
	 */
	int getCount(Products products);

}
