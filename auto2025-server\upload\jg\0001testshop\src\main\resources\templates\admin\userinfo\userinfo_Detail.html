<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
<head th:replace="@{/admin/head.html}"></head>
  
</head>

<body>
    <div class="content-area" style="height: 100%; padding: 20px;">
        <h4 class="page-title">详情用户信息</h4>

        <div>
        <div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">用户信息详情</h5>
        </div>
        <div class="card-body">
                <div class="form-group row mb-3">
                    <label class="col-sm-3 col-form-label fw-bold">用户账号:</label>
                    <div class="col-sm-9">
                        <p class="form-control-plaintext border-bottom pb-2" th:text="${item.account}"></p>
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label class="col-sm-3 col-form-label fw-bold">登录密码:</label>
                    <div class="col-sm-9">
                        <p class="form-control-plaintext border-bottom pb-2" th:text="${item.password}"></p>
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label class="col-sm-3 col-form-label fw-bold">姓名:</label>
                    <div class="col-sm-9">
                        <p class="form-control-plaintext border-bottom pb-2" th:text="${item.uname}"></p>
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label class="col-sm-3 col-form-label fw-bold">性别:</label>
                    <div class="col-sm-9">
                        <p class="form-control-plaintext border-bottom pb-2" th:text="${item.gender}"></p>
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label class="col-sm-3 col-form-label fw-bold">手机号码:</label>
                    <div class="col-sm-9">
                        <p class="form-control-plaintext border-bottom pb-2" th:text="${item.phone}"></p>
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label class="col-sm-3 col-form-label fw-bold">电子邮箱:</label>
                    <div class="col-sm-9">
                        <p class="form-control-plaintext border-bottom pb-2" th:text="${item.email}"></p>
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label class="col-sm-3 col-form-label fw-bold">照片:</label>
                    <div class="col-sm-9">
                        <div th:if="${item.photo}">
                            <img th:src="@{'/upload/' + ${item.photo}}" alt="照片" style="width: 180px; height: 180px; object-fit: cover; border: 1px solid #ddd; border-radius: 4px; cursor: pointer;" onclick="previewImage(this.src)">
                        </div>
                        <span th:unless="${item.photo}">无图片</span>
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label class="col-sm-3 col-form-label fw-bold">注册时间:</label>
                    <div class="col-sm-9">
                        <p class="form-control-plaintext border-bottom pb-2" th:text="${#dates.format(item.registertime, 'yyyy-MM-dd HH:mm:ss')}"></p>
                    </div>
                </div>
            <div class="form-group row">
                <div class="col-sm-9 offset-sm-3">
                    <a href="#" onclick="history.back()" class="btn btn-secondary ml-2">
                        <i class="bi bi-arrow-left"></i> 返回
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
        <script>
        $(document).ready(function() {
            // 页面加载时的初始化操作
            console.log('userinfo 页面加载完成');
        });
        </script>

        </div>
    </div>

    <!-- Bootstrap JS -->
</body>

</html>