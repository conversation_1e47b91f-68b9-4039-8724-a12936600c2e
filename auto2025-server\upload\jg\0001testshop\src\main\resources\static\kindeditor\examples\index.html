<!doctype html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>KindEditor Examples</title>
		<style>
			body {
				font-size: 12px;
				font-family: Tahoma, Simsun;
			}
			h3 {
				font-size: 14px;
			}
			a {
				color: #1870a9;
				text-decoration: none;
			}
			a:hover {
				color: #BC2A4D;
			}
			li {
				margin: 5px;
			}
		</style>
	</head>
	<body>
		<h3>编辑器演示</h3>
		<ol>
			<li><a href="default.html" target="_blank">default.html</a> (默认模式)</li>
			<li><a href="simple.html" target="_blank">simple.html</a> (简单模式)</li>
			<li><a href="dynamic-load.html" target="_blank">dynamic-load.html</a> (异步加载)</li>
			<li><a href="multi-language.html" target="_blank">multi-language.html</a> (多语言)</li>
			<li><a href="readonly.html" target="_blank">readonly.html</a> (只读模式)</li>
			<li><a href="newline.html" target="_blank">newline.html</a> (回车换行设置)</li>
			<li><a href="word-count.html" target="_blank">word-count.html</a> (统计字数)</li>
			<li><a href="filter-mode.html" target="_blank">filter-mode.html</a> (关闭HTML过滤)</li>
			<li><a href="url-type.html" target="_blank">url-type.html</a> (URL设置)</li>
			<li><a href="paste-type.html" target="_blank">paste-type.html</a> (粘贴设置)</li>
			<li><a href="auto-height.html" target="_blank">auto-height.html</a> (自动调整高度)</li>
			<li><a href="custom-theme.html" target="_blank">custom-theme.html</a> (自定义风格)</li>
			<li><a href="qqstyle.html" target="_blank">qqstyle.html</a> (自定义风格 仿QQ邮箱)</li>
			<li><a href="custom-plugin.html" target="_blank">custom-plugin.html</a> (自定义插件)</li>
		</ol>
		<h3>使用其它类库</h3>
		<ol>
			<li><a href="jquery.html" target="_blank">jquery.html</a> (jQuery)</li>
			<li><a href="jquery-ui.html" target="_blank">jquery-ui.html</a> (jQuery UI)</li>
		</ol>
		<h3>单独调用组件</h3>
		<ol>
			<li><a href="node.html" target="_blank">node.html</a> (Node操作)</li>
			<li><a href="uploadbutton.html" target="_blank">uploadbutton.html</a> (上传按钮)</li>
			<li><a href="dialog.html" target="_blank">dialog.html</a> (弹出框)</li>
			<li><a href="colorpicker.html" target="_blank">colorpicker.html</a> (取色器)</li>
			<li><a href="file-manager.html" target="_blank">file-manager.html</a> (浏览服务器)</li>
			<li><a href="/admin/image-dialog.html" target="_blank">image-dialog.html</a> (上传图片弹出框)</li>
			<li><a href="multi-image-dialog.html" target="_blank">multi-image-dialog.html</a> (批量上传弹出框)</li>
			<li><a href="file-dialog.html" target="_blank">file-dialog.html</a> (上传文件弹出框)</li>
		</ol>
	</body>
</html>
