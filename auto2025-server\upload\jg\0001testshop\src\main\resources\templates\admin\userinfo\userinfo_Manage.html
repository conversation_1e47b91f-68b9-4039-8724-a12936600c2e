<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
<head th:replace="@{/admin/head.html}"></head>
  
</head>

<body>
    <div class="content-area" style="height: 100%; padding: 20px;">
        <h4 class="page-title">管理用户信息</h4>

        <div>
        <div class="container-fluid">
    <div class="card mb-3">
        <div class="card-body">
            <form class="search-form" method="get">
                <div class="row g-3 align-items-end">
                    <div class="col-md-3">
                        <label for="search_account" class="form-label">用户账号</label>
                        <input type="text" class="form-control" id="search_account" name="account" placeholder="请输入用户账号" th:value="${param.account}">
                    </div>
                    <div class="col-md-3">
                        <label for="search_uname" class="form-label">姓名</label>
                        <input type="text" class="form-control" id="search_uname" name="uname" placeholder="请输入姓名" th:value="${param.uname}">
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-search"></i> 搜索
                        </button>
                        <a href="?" class="btn btn-secondary">
                            <i class="bi bi-arrow-clockwise"></i> 重置
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">用户信息列表</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="thead-light">
                        <tr>
                    <th>用户账号</th>
                    <th>登录密码</th>
                    <th>姓名</th>
                    <th>性别</th>
                    <th>手机号码</th>
                    <th>电子邮箱</th>
                    <th>照片</th>
                    <th>注册时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr th:each="item : ${list}">
                    <td th:text="${item.account}"></td>
                    <td th:text="${item.password}"></td>
                    <td th:text="${item.uname}"></td>
                    <td th:text="${item.gender}"></td>
                    <td th:text="${item.phone}"></td>
                    <td th:text="${item.email}"></td>
                    <td>
                        <div th:if="${item.photo}">
                            <img th:src="@{'/upload/' + ${item.photo}}" alt="图片" style="width: 60px; height: 60px; object-fit: cover; border: 1px solid #ddd; border-radius: 4px; cursor: pointer;" onclick="previewImage(this.src)">
                        </div>
                        <span th:unless="${item.photo}">无图片</span>
></td>
                    <td th:text="${#dates.format(item.registertime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                    <td>
                        <a th:href="@{/userinfoToDetail(id=${item.account})}" class="btn btn-sm btn-info me-1">
                            <i class="bi bi-eye-fill"></i> 详情
                        </a>
                        <a th:href="@{/userinfoToEdit(id=${item.account})}" class="btn btn-sm btn-primary me-1">
                            <i class="bi bi-pencil-square"></i> 编辑
                        </a>
                        <a href="javascript:void(0)" th:data-id="${item.account}" onclick="deleteData('userinfoDel',this)" class="btn btn-sm btn-danger">
                            <i class="bi bi-trash3"></i> 删除
                        </a>
                    </td>
                        </tr>
                        <tr th:if="${#lists.isEmpty(list)}">
                            <td colspan="100%" class="text-center text-muted">暂无数据</td>
                        </tr>
                    </tbody>
                </table>
            
            <!-- 分页区域 -->
            <nav aria-label="分页导航" th:if="${totalPages > 1}">
                <ul class="pagination justify-content-center">
                    <li class="page-item" th:classappend="${currentPage == 1} ? 'disabled'">
                        <a class="page-link" th:href="@{''(page=${currentPage - 1})}">上一页</a>
                    </li>
                    <li class="page-item" th:each="i : ${#numbers.sequence(1, totalPages)}" 
                        th:classappend="${i == currentPage} ? 'active'">
                        <a class="page-link" th:href="@{''(page=${i})}" th:text="${i}"></a>
                    </li>
                    <li class="page-item" th:classappend="${currentPage == totalPages} ? 'disabled'">
                        <a class="page-link" th:href="@{''(page=${currentPage + 1})}">下一页</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
</div>
        <script>
        // 分页功能
        function goToPage(page) {
            var url = new URL(window.location);
            url.searchParams.set('page', page);
            window.location.href = url.toString();
        }
        
        // 图片预览函数
        function previewImage(src) {
            var modal = '<div class="modal fade" id="imagePreviewModal" tabindex="-1">' +
                       '<div class="modal-dialog modal-lg modal-dialog-centered">' +
                       '<div class="modal-content">' +
                       '<div class="modal-header">' +
                       '<h5 class="modal-title">图片预览</h5>' +
                       '<button type="button" class="btn-close" data-bs-dismiss="modal"></button>' +
                       '</div>' +
                       '<div class="modal-body text-center">' +
                       '<img src="' + src + '" class="img-fluid" style="max-height: 70vh;">' +
                       '</div>' +
                       '</div></div></div>';
            
            $('body').append(modal);
            var modalInstance = new bootstrap.Modal(document.getElementById('imagePreviewModal'));
            modalInstance.show();
            
            $('#imagePreviewModal').on('hidden.bs.modal', function() {
                $(this).remove();
            });
        }
        </script>

        </div>
    </div>

    <!-- Bootstrap JS -->
</body>

</html>