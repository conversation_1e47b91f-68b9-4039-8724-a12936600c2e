{"remainingRequest": "J:\\auto2025\\auto2025-web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!J:\\auto2025\\auto2025-web\\src\\views\\admin\\small\\SmallDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "J:\\auto2025\\auto2025-web\\src\\views\\admin\\small\\SmallDetail.vue", "mtime": 1749439666296}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748675476717}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748675484569}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748675476717}, {"path": "J:\\auto2025\\auto2025-web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1748675485115}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogICAgICAgIA0KICAgICAgICBpbXBvcnQgcmVxdWVzdCwgeyBiYXNlIH0gZnJvbSAiLi4vLi4vLi4vLi4vdXRpbHMvaHR0cCI7DQogICAgICAgIGV4cG9ydCBkZWZhdWx0IHsNCiAgICAgICAgICAgIG5hbWU6ICdTbWFsbERldGFpbCcsDQogICAgICAgICAgICBjb21wb25lbnRzOiB7DQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgZGF0YSgpIHsNCiAgICAgICAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgICAgICAgICBpZDogJycsDQogICAgICAgICAgICAgICAgICAgIGZvcm1EYXRhOiB7fSwgLy/ooajljZXmlbDmja4gICAgICAgICANCiAgICAgICAgDQogICAgICAgICAgICAgICAgfTsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBjcmVhdGVkKCkgew0KICAgICAgICAgICAgICAgIHRoaXMuaWQgPSB0aGlzLiRyb3V0ZS5xdWVyeS5pZDsgLy/ojrflj5blj4LmlbANCiAgICAgICAgICAgICAgICB0aGlzLmdldERhdGFzKCk7DQogICAgICAgICAgICB9LA0KICAgICAgICANCiAgICAgICAgDQogICAgICAgICAgICBtZXRob2RzOiB7DQogICAgICAgIA0KICAgICAgICAgICAgICAgIC8v6I635Y+W5YiX6KGo5pWw5o2uDQogICAgICAgICAgICAgICAgZ2V0RGF0YXMoKSB7DQogICAgICAgICAgICAgICAgICAgIGxldCBwYXJhID0gew0KICAgICAgICAgICAgICAgICAgICB9Ow0KICAgICAgICAgICAgICAgICAgICB0aGlzLmxpc3RMb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAgICAgICAgICAgICAgbGV0IHVybCA9IGJhc2UgKyAiL3NtYWxsL2dldD9pZD0iICsgdGhpcy5pZDsNCiAgICAgICAgICAgICAgICAgICAgcmVxdWVzdC5wb3N0KHVybCwgcGFyYSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmZvcm1EYXRhID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeShyZXMucmVzZGF0YSkpOw0KICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5saXN0TG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICANCiAgICAgICAgICAgICAgICAvLyDov5Tlm54NCiAgICAgICAgICAgICAgICBiYWNrKCkgew0KICAgICAgICAgICAgICAgICAgICAvL+i/lOWbnuS4iuS4gOmhtQ0KICAgICAgICAgICAgICAgICAgICB0aGlzLiRyb3V0ZXIuZ28oLTEpOw0KICAgICAgICAgICAgICAgIH0sDQogICAgICAgIA0KICAgICAgICAgICAgfSwNCiAgICAgICAgfQ0KDQo="}, {"version": 3, "sources": ["J:\\auto2025\\auto2025-web\\src\\views\\admin\\small\\SmallDetail.vue"], "names": [], "mappings": ";;QAiDQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACZ,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;oBACH,CAAC,CAAC,EAAE,CAAC,CAAC;oBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;gBAEvB,CAAC;YACL,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC;;;YAGD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;gBAEL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;oBACP,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;oBACX,CAAC;oBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;oBACvB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;wBAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC5B,CAAC,CAAC;gBACN,CAAC;;gBAED,CAAC,EAAE,CAAC;gBACJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;oBACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACvB,CAAC;;YAEL,CAAC;QACL", "file": "J:/auto2025/auto2025-web/src/views/admin/small/SmallDetail.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n    <div style=\"width: 100%;line-height: 30px;text-align: left;\">\r\n       <el-form :model=\"formData\" label-width=\"20%\" align=\"left\">\r\n<el-form-item label=\"小类ID\">\r\n{{formData.sid}}</el-form-item>\r\n<el-form-item label=\"大类\">\r\n{{formData.bname}}</el-form-item>\r\n<el-form-item label=\"小类名称\">\r\n{{formData.sname}}</el-form-item>\r\n<el-form-item label=\"内容1\">\r\n{{formData.memo1}}</el-form-item>\r\n<el-form-item label=\"内容2\">\r\n{{formData.memo2}}</el-form-item>\r\n<el-form-item label=\"内容3\">\r\n{{formData.memo3}}</el-form-item>\r\n<el-form-item label=\"内容4\" prop=\"memo4\">\r\n<div v-html=\"formData.memo4\"></div>\r\n</el-form-item>\r\n<el-form-item label=\"备用1\">\r\n{{formData.by1}}</el-form-item>\r\n<el-form-item label=\"备用2\">\r\n{{formData.by2}}</el-form-item>\r\n<el-form-item label=\"备用3\">\r\n{{formData.by3}}</el-form-item>\r\n<el-form-item label=\"备用4\">\r\n{{formData.by4}}</el-form-item>\r\n<el-form-item label=\"备用5\">\r\n{{formData.by5}}</el-form-item>\r\n<el-form-item label=\"备用6\">\r\n{{formData.by6}}</el-form-item>\r\n<el-form-item label=\"备用7\">\r\n{{formData.by7}}</el-form-item>\r\n<el-form-item label=\"备用8\">\r\n{{formData.by8}}</el-form-item>\r\n<el-form-item label=\"备用9\">\r\n{{formData.by9}}</el-form-item>\r\n<el-form-item label=\"备用10\">\r\n{{formData.by10}}</el-form-item>\r\n<el-form-item>\r\n<el-button type=\"info\" size=\"small\" @click=\"back\" icon=\"el-icon-back\">返 回</el-button>\r\n</el-form-item>\r\n</el-form>\r\n\r\n\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n        \r\n        import request, { base } from \"../../../../utils/http\";\r\n        export default {\r\n            name: 'SmallDetail',\r\n            components: {\r\n            },\r\n            data() {\r\n                return {\r\n                    id: '',\r\n                    formData: {}, //表单数据         \r\n        \r\n                };\r\n            },\r\n            created() {\r\n                this.id = this.$route.query.id; //获取参数\r\n                this.getDatas();\r\n            },\r\n        \r\n        \r\n            methods: {\r\n        \r\n                //获取列表数据\r\n                getDatas() {\r\n                    let para = {\r\n                    };\r\n                    this.listLoading = true;\r\n                    let url = base + \"/small/get?id=\" + this.id;\r\n                    request.post(url, para).then((res) => {\r\n                        this.formData = JSON.parse(JSON.stringify(res.resdata));\r\n                        this.listLoading = false;\r\n                    });\r\n                },\r\n        \r\n                // 返回\r\n                back() {\r\n                    //返回上一页\r\n                    this.$router.go(-1);\r\n                },\r\n        \r\n            },\r\n        }\r\n\r\n</script>\r\n<style scoped>\r\n</style>\r\n \r\n\r\n"]}]}