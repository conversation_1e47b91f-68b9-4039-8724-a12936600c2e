package com.mapper;

import java.util.List;
import java.util.Map;

import com.model.Products;

/**
 * 产品数据访问层接口
 *
 * 功能说明：
 * 1. 提供产品的CRUD操作接口
 * 2. 支持条件查询和分页查询
 * 3. 使用MyBatis框架进行数据库操作
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024
 */
public interface ProductsMapper {

	/**
	 * 查询所有产品记录
	 * @return 产品列表
	 */
	public List<Products> findProductsList();

	/**
	 * 根据条件查询产品记录
	 * @param inputParam 查询参数Map，支持动态条件查询
	 * @return 符合条件的产品列表
	 */
	public List<Products> query(Map<String, Object> inputParam);

	/**
	 * 统计符合条件的产品记录总数
	 * @param inputParam 查询参数Map
	 * @return 记录总数
	 */
	int getCount(Map<String, Object> inputParam);

	/**
	 * 新增产品记录
	 * @param products 产品实体对象
	 * @return 影响的行数
	 */
	public int insertProducts(Products products);

	/**
	 * 根据ID删除产品记录
	 * @param id 主键ID
	 * @return 影响的行数
	 */
	public int deleteProducts(Integer id);

	/**
	 * 更新产品记录
	 * @param products 产品实体对象
	 * @return 影响的行数
	 */
	public int updateProducts(Products products);

	/**
	 * 根据ID查询产品记录
	 * @param id 主键ID
	 * @return 产品实体对象，如果不存在则返回null
	 */
	public Products queryProductsById(Integer id);

}

